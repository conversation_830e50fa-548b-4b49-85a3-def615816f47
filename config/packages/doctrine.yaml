doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'
        server_version: '17.6'
        logging: true

        profiling_collect_backtrace: '%kernel.debug%'
        use_savepoints: true

        types:
            permission_enum_array: App\Infrastructure\Doctrine\Type\PermissionEnumArrayType

    orm:
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        report_fields_where_declared: true
        validate_xml_mapping: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        identity_generation_preferences:
            Doctrine\DBAL\Platforms\PostgreSQLPlatform: identity
        auto_mapping: true
        mappings:
            App:
                type: attribute
                is_bundle: false
                dir: '%kernel.project_dir%/src/Domain/Entity'
                prefix: 'App\Domain\Entity'
                alias: App
        controller_resolver:
            auto_mapping: true
        filters:
            tenant:
                class: App\Domain\Service\Tenant\DoctrineTenantFilter
                enabled: true
        dql:
            numeric_functions:
                CAST_AS_INTEGER: App\Infrastructure\Doctrine\CastAsInteger

when@test:
    doctrine:
        dbal:
            # "TEST_TOKEN" is typically set by ParaTest
            dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod:
    doctrine:
        orm:
            auto_generate_proxy_classes: false
            proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
            query_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            result_cache_driver:
                type: pool
                pool: doctrine.result_cache_pool

    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.app
                doctrine.system_cache_pool:
                    adapter: cache.system
