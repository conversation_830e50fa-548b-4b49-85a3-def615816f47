framework:
    messenger:
        default_bus: messenger.bus.default
        buses:
            messenger.bus.default:
                default_middleware: true
                middleware:
                    - App\Infrastructure\MessageQueue\Middleware\ContextLoaderMiddleware
                    - App\Infrastructure\MessageQueue\Middleware\ContextSetterMiddleware
                    - App\Infrastructure\MessageQueue\Middleware\DoctrineTransactionMiddleware
        failure_transport: dead_letter

        transports:
            failed: 'doctrine://default?queue_name=failed'
            sync: 'sync://'

            dead_letter:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                retry_strategy:
                    max_retries: 100
                    delay: 0
                options:
                    cacert: '%env(MESSENGER_TRANSPORT_CACERT)%'
                    confirm_timeout: 2 # Maximum number of seconds to wait for confirmation from the broker
                    exchange:
                        type: fanout
                        name: dead_letter_exchange
                    queues:
                        dead_letter:
                            binding_keys:
                                - dead_letter
                            arguments:
                                x-queue-type: quorum
                                x-quorum-initial-group-size: '%env(int:RABBITMQ_QUORUM_GROUP_SIZE)%'

            async:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                failure_transport: failed
                retry_strategy:
                    max_retries: 3
                    delay: 1000
                    multiplier: 2
                options:
                    cacert: '%env(MESSENGER_TRANSPORT_CACERT)%'
                    confirm_timeout: 2 # Maximum number of seconds to wait for confirmation from the broker
                    exchange:
                        type: direct
                        name: async_exchange
                        default_publish_routing_key: async
                    queues:
                        async:
                            binding_keys:
                                - async
                            arguments:
                                x-queue-type: quorum
                                x-quorum-initial-group-size: '%env(int:RABBITMQ_QUORUM_GROUP_SIZE)%'

        routing:
            Symfony\Component\Mailer\Messenger\SendEmailMessage: async

when@localdev:
    framework:
        messenger:
            transports:
                async: 'sync://'

when@dev:
    framework:
        messenger:
            transports:
                async: 'sync://'
