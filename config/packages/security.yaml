security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\InMemoryUser: plaintext
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        users_in_memory: { memory: null }

        sap_europe_user_provider:
            memory:
                users:
                    '%env(INPUT_API_SAP_EUROPE_USERNAME)%': { password: '%env(INPUT_API_SAP_EUROPE_PASSWORD)%', roles: 'ROLE_INPUT' }

        keycloak:
            id: App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUserProvider

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        health_checks:
            pattern: ^/monitor/health
            security: false
            stateless: true

        api_docs:
            pattern: ^/doc/
            security: false
            stateless: true

        api_sap_europe:
            pattern: ^/api/sap-europe
            security: true
            stateless: true
            provider: sap_europe_user_provider
            http_basic:
                realm: "SAP Europe"

        api_portal:
            pattern: ^/api/portal
            security: true
            stateless: true
            provider: keycloak
            access_token:
                token_handler:
                    oidc:
                        claim: preferred_username
                        algorithm: 'RS256'
                        audience: 'myprezero-backend'
                        discovery:
                            base_uri: '%env(KEYCLOAK_URL)%/realms/myprezero/'
                            cache:
                                id: keycloak.cache
                        issuers:
                            - '%env(KEYCLOAK_ISSUER)%'
                            - 'http://localhost:8014/realms/myprezero' # For local development, won't be an issue on prod

        main:
            lazy: true
            provider: users_in_memory

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/api/sap-europe, roles: ROLE_INPUT }
        - { path: ^/api/portal, roles: ROLE_PORTAL }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
