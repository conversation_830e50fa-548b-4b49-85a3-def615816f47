api:
    consider_nullable_properties_as_optional: true
    areas:
        portal:
            max_per_page: 100
            default_per_page: 25
            resource_path:
                - 'src/Infrastructure/Portal/Customer/Resource'
                - 'src/Infrastructure/Portal/Management/Resource'
            url_prefix: 'api/portal/v1'
            global_request_headers:
                Accept-Language:
                    type: 'string'
                    example: 'en'
                    description: 'The language of the response, default is en'
                x-current-user-context-id:
                    format: 'guid'
                    example: 'acfe025b-0beb-393d-adb7-c92033f1fa2c'
                    description: 'The unique identifier of the business partner id'
                    required: false
            open_api:
                info:
                    title: Portal API
                    description: Autogenerated documentation for Portal API
                    version: 1.0.0
                components:
                    securitySchemes:
                        Bearer:
                            type: http
                            scheme: bearer
                            bearerFormat: JWT
                security:
                    -   Bearer: [ ]
        sap_europe:
            resource_path: 'src/Infrastructure/SapEurope/Resource'
            url_prefix: 'api/sap-europe/v1'
            open_api:
                info:
                    title: SAP Europe
                    description: Autogenerated documentation for My API
                    version: 1.0.0
                components:
                    securitySchemes:
                        basicAuth:
                            type: http
                            scheme: basic
                security:
                    -   basicAuth: [ ]
