<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

use <PERSON>\CodeQuality\Rector\Identical\FlipTypeControlToUseExclusiveTypeRector;
use <PERSON>\CodeQuality\Rector\Switch_\SwitchTrueToIfRector;
use <PERSON>\CodingStyle\Rector\If_\NullableCompareToNullRector;
use <PERSON>\Config\RectorConfig;
use <PERSON>\Doctrine\Set\DoctrineSetList;
use <PERSON>\Doctrine\TypedCollections\Rector\ClassMethod\DefaultCollectionKeyRector;
use <PERSON>\Php74\Rector\Property\RestoreDefaultNullToNullableTypePropertyRector;
use <PERSON>\Php80\Rector\Class_\ClassPropertyAssignToConstructorPromotionRector;
use Rector\Php81\Rector\FuncCall\NullToStrictStringFuncCallArgRector;
use <PERSON>\Php83\Rector\ClassMethod\AddOverrideAttributeToOverriddenMethodsRector;
use Rector\TypeDeclaration\Rector\StmtsAwareInterface\DeclareStrictTypesRector;
use <PERSON><PERSON><PERSON>ikhail\AddNamedArgumentsRector\AddNamedArgumentsRector;

return RectorConfig::configure()
    ->withPaths([
        __DIR__.'/src',
        __DIR__.'/tests',
    ])
    ->withSkip([
        __DIR__.'/config/bundles.php',
        __DIR__.'/tests/_output',
        __DIR__.'/tests/Support/_generated',
        RestoreDefaultNullToNullableTypePropertyRector::class,
        NullToStrictStringFuncCallArgRector::class,
        FlipTypeControlToUseExclusiveTypeRector::class,
        AddOverrideAttributeToOverriddenMethodsRector::class,
        ClassPropertyAssignToConstructorPromotionRector::class => [
            __DIR__.'/src/Domain/Entity/*.php',
        ],
        SwitchTrueToIfRector::class,
        // Skip DefaultCollectionKeyRector for files using PreZero\ApiBundle\Collection\Collection
        // as it only supports one generic type parameter, not two
        DefaultCollectionKeyRector::class => [
            __DIR__.'/src/Infrastructure/Portal/*/HttpEndpoint/*.php',
            __DIR__.'/src/Infrastructure/SapEurope/HttpEndpoint/*.php',
        ],
        AddNamedArgumentsRector::class => [
            __DIR__.'/src/Infrastructure/Redis/Redis.php',
        ],
    ])
    ->withImportNames(importShortClasses: false, removeUnusedImports: true)
    ->withRules([
        DeclareStrictTypesRector::class,
        NullableCompareToNullRector::class,
        AddNamedArgumentsRector::class,
    ])
    ->withPhpSets()
    ->withPreparedSets(
        deadCode: true,
        codeQuality: true,
        typeDeclarations: true,
    )
    ->withAttributesSets(
        symfony: true,
        doctrine: true,
    )
    ->withComposerBased(
        doctrine: true,
        symfony: true,
    )
    ->withSets([
        DoctrineSetList::DOCTRINE_CODE_QUALITY,
        DoctrineSetList::TYPED_COLLECTIONS,
        DoctrineSetList::YAML_TO_ANNOTATIONS,
    ]);
