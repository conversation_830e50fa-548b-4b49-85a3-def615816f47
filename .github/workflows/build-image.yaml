name: Build Release Image

on:
  release:
    types:
      - created

run-name: Build Release Image (${{ github.event.release.tag_name }})

jobs:
  build-release-images:
    name: Build release images
    uses: prezero/workflows/.github/workflows/docker-build.yaml@21d8ec9faa630c059134d02061adc9fa04da8f97 # v1.36.0
    with:
      ENV: prod
      DOCKERFILE: docker/Dockerfile
      IMAGE: ghcr.io/${{ github.repository }}
      TAG: ${{ github.event.release.tag_name }}
      RELEASE_CREATED: true
    secrets: inherit
