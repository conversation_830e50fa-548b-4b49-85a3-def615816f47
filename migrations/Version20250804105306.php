<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250804105306 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE waste_statistic (ext_business_partner_id VARCHAR(10) NOT NULL, ext_contract_id VARCHAR(10) NOT NULL, ext_waste_statistic_location_id VARCHAR(10) DEFAULT NULL, waste_statistic_location_id UUID DEFAULT NULL, avv_text VARCHAR(250) NOT NULL, avv_id VARCHAR(10) NOT NULL, order_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, material VARCHAR(250) NOT NULL, ext_material_id VARCHAR(10) NOT NULL, amount DOUBLE PRECISION NOT NULL, unit_om VARCHAR(3) NOT NULL, net DOUBLE PRECISION NOT NULL, currency VARCHAR(5) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_9020A742A6689BB8 ON waste_statistic (ext_business_partner_id)');
        $this->addSql('CREATE INDEX IDX_9020A74250FD782 ON waste_statistic (ext_contract_id)');
        $this->addSql('CREATE INDEX IDX_9020A742284B2922 ON waste_statistic (ext_waste_statistic_location_id)');
        $this->addSql('CREATE INDEX IDX_9020A7424E59C462 ON waste_statistic (tenant)');
        $this->addSql('CREATE TABLE waste_statistic_location (ext_id VARCHAR(10) NOT NULL, address VARCHAR(250) NOT NULL, info VARCHAR(250) DEFAULT NULL, name VARCHAR(100) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_F26926605D04BFAD ON waste_statistic_location (ext_id)');
        $this->addSql('CREATE INDEX IDX_F26926604E59C462 ON waste_statistic_location (tenant)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE waste_statistic');
        $this->addSql('DROP TABLE waste_statistic_location');
    }
}
