<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250818094241 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE notice_text ALTER ext_id DROP NOT NULL');
        $this->addSql('ALTER TABLE notice_text ALTER ext_pos_id DROP NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_id DROP NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_pos_id DROP NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_service_id DROP NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_service_pos_id DROP NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_object_id DROP NOT NULL');
        $this->addSql('ALTER TABLE service ALTER ext_id DROP NOT NULL');
        $this->addSql('ALTER TABLE service ALTER ext_pos_id DROP NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE notice_text ALTER ext_id SET NOT NULL');
        $this->addSql('ALTER TABLE notice_text ALTER ext_pos_id SET NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_id SET NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_pos_id SET NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_service_id SET NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_service_pos_id SET NOT NULL');
        $this->addSql('ALTER TABLE "order" ALTER ext_object_id SET NOT NULL');
        $this->addSql('ALTER TABLE service ALTER ext_id SET NOT NULL');
        $this->addSql('ALTER TABLE service ALTER ext_pos_id SET NOT NULL');
    }
}
