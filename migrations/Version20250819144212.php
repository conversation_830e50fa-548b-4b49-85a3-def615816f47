<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250819144212 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP TABLE translation');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE TABLE translation (locale VARCHAR(255) NOT NULL, tech_name VARCHAR(255) NOT NULL, translation VARCHAR(255) NOT NULL, system_code INT NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, longtext TEXT DEFAULT NULL, PRIMARY KEY (id))');
    }
}
