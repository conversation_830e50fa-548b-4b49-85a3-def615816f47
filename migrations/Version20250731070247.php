<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250731070247 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE order_agreement (name VARCHAR(30) NOT NULL, service_type VARCHAR(30) DEFAULT NULL, required BOOLEAN DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_92E221DC4E59C462 ON order_agreement (tenant)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE order_agreement');
    }
}
