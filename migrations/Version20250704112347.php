<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250704112347 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX uniq_1cce56315d04bfad');
        $this->addSql('ALTER TABLE service_product DROP ext_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE service_product ADD ext_id VARCHAR(20) NOT NULL');
        $this->addSql('CREATE UNIQUE INDEX uniq_1cce56315d04bfad ON service_product (ext_id)');
    }
}
