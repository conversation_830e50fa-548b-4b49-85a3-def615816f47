<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250806085553 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE user_business_partner (user_id UUID NOT NULL, business_partner_id UUID NOT NULL, PRIMARY KEY (user_id, business_partner_id))');
        $this->addSql('CREATE INDEX IDX_110DC786A76ED395 ON user_business_partner (user_id)');
        $this->addSql('CREATE INDEX IDX_110DC7865330F055 ON user_business_partner (business_partner_id)');
        $this->addSql('ALTER TABLE user_business_partner ADD CONSTRAINT FK_110DC786A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE user_business_partner ADD CONSTRAINT FK_110DC7865330F055 FOREIGN KEY (business_partner_id) REFERENCES business_partner (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE "user" ADD first_name VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE "user" ADD last_name VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE "user" ADD permissions varchar(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE user_business_partner DROP CONSTRAINT FK_110DC786A76ED395');
        $this->addSql('ALTER TABLE user_business_partner DROP CONSTRAINT FK_110DC7865330F055');
        $this->addSql('DROP TABLE user_business_partner');
        $this->addSql('ALTER TABLE "user" DROP first_name');
        $this->addSql('ALTER TABLE "user" DROP last_name');
        $this->addSql('ALTER TABLE "user" DROP permissions');
    }
}
