<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250704113713 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE archive_document ADD invoice_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE archive_document ADD order_id UUID DEFAULT NULL');
        $this->addSql('CREATE INDEX IDX_F506451A2989F1FD ON archive_document (invoice_id)');
        $this->addSql('CREATE INDEX IDX_F506451A8D9F6D38 ON archive_document (order_id)');
        $this->addSql('ALTER TABLE bank_account ADD business_partner_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_53A23E0A5330F055 ON bank_account (business_partner_id)');
        $this->addSql('ALTER TABLE contact_person ADD business_partner_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_A44EE6F75330F055 ON contact_person (business_partner_id)');
        $this->addSql('ALTER TABLE contract_partner ADD contract_id UUID NOT NULL');
        $this->addSql('ALTER TABLE contract_partner ADD contract_pos_id UUID DEFAULT NULL');
        $this->addSql('CREATE INDEX IDX_397A43DE2576E0FD ON contract_partner (contract_id)');
        $this->addSql('CREATE INDEX IDX_397A43DE7D48BAE3 ON contract_partner (contract_pos_id)');
        $this->addSql('ALTER TABLE contract_position ADD contract_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_D39BFF0A2576E0FD ON contract_position (contract_id)');
        $this->addSql('ALTER TABLE identification_element ADD business_partner_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_8B587A955330F055 ON identification_element (business_partner_id)');
        $this->addSql('ALTER TABLE invoice_archive_document DROP CONSTRAINT fk_fd2f9c982989f1fd');
        $this->addSql('ALTER TABLE invoice_contract ADD invoice_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_F6A772012989F1FD ON invoice_contract (invoice_id)');
        $this->addSql('ALTER TABLE notice_text ADD service_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_69C99236ED5CA9E6 ON notice_text (service_id)');
        $this->addSql('ALTER TABLE order_archive_document DROP CONSTRAINT fk_971051068d9f6d38');
        $this->addSql('ALTER TABLE order_confirmation ADD supplier_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_4CFD86562ADD6D8C ON order_confirmation (supplier_id)');
        $this->addSql('ALTER TABLE sales_organisation ADD business_partner_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_7AAC3F0C5330F055 ON sales_organisation (business_partner_id)');
        $this->addSql('ALTER TABLE service_frequency ADD route_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_7EEE825934ECB4E6 ON service_frequency (route_id)');
        $this->addSql('ALTER TABLE sign_data ADD supervision_document_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE sign_data ADD verification_document_id UUID DEFAULT NULL');
        $this->addSql('CREATE INDEX IDX_AA98380A5D2030F6 ON sign_data (supervision_document_id)');
        $this->addSql('CREATE INDEX IDX_AA98380A241CC2BA ON sign_data (verification_document_id)');
        $this->addSql('ALTER TABLE supervision_document_partner ADD supervision_document_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_E7296AAA5D2030F6 ON supervision_document_partner (supervision_document_id)');
        $this->addSql('ALTER TABLE supervision_document_sign_data DROP CONSTRAINT fk_8eee9a515d2030f6');
        $this->addSql('ALTER TABLE verification_document_partner ADD verification_document_id UUID NOT NULL');
        $this->addSql('CREATE INDEX IDX_85C9DF85241CC2BA ON verification_document_partner (verification_document_id)');
        $this->addSql('ALTER TABLE verification_document_sign_data DROP CONSTRAINT fk_27f987df241cc2ba');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_A44EE6F75330F055');
        $this->addSql('ALTER TABLE contact_person DROP business_partner_id');
        $this->addSql('ALTER TABLE order_archive_document ADD CONSTRAINT fk_971051068d9f6d38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE verification_document_sign_data ADD CONSTRAINT fk_27f987df241cc2ba FOREIGN KEY (verification_document_id) REFERENCES verification_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP INDEX IDX_7EEE825934ECB4E6');
        $this->addSql('ALTER TABLE service_frequency DROP route_id');
        $this->addSql('DROP INDEX IDX_7AAC3F0C5330F055');
        $this->addSql('ALTER TABLE sales_organisation DROP business_partner_id');
        $this->addSql('ALTER TABLE supervision_document_sign_data ADD CONSTRAINT fk_8eee9a515d2030f6 FOREIGN KEY (supervision_document_id) REFERENCES supervision_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP INDEX IDX_53A23E0A5330F055');
        $this->addSql('ALTER TABLE bank_account DROP business_partner_id');
        $this->addSql('DROP INDEX IDX_69C99236ED5CA9E6');
        $this->addSql('ALTER TABLE notice_text DROP service_id');
        $this->addSql('DROP INDEX IDX_D39BFF0A2576E0FD');
        $this->addSql('ALTER TABLE contract_position DROP contract_id');
        $this->addSql('DROP INDEX IDX_F6A772012989F1FD');
        $this->addSql('ALTER TABLE invoice_contract DROP invoice_id');
        $this->addSql('DROP INDEX IDX_4CFD86562ADD6D8C');
        $this->addSql('ALTER TABLE order_confirmation DROP supplier_id');
        $this->addSql('DROP INDEX IDX_AA98380A5D2030F6');
        $this->addSql('DROP INDEX IDX_AA98380A241CC2BA');
        $this->addSql('ALTER TABLE sign_data DROP supervision_document_id');
        $this->addSql('ALTER TABLE sign_data DROP verification_document_id');
        $this->addSql('DROP INDEX IDX_397A43DE2576E0FD');
        $this->addSql('DROP INDEX IDX_397A43DE7D48BAE3');
        $this->addSql('ALTER TABLE contract_partner DROP contract_id');
        $this->addSql('ALTER TABLE contract_partner DROP contract_pos_id');
        $this->addSql('DROP INDEX IDX_E7296AAA5D2030F6');
        $this->addSql('ALTER TABLE supervision_document_partner DROP supervision_document_id');
        $this->addSql('ALTER TABLE invoice_archive_document ADD CONSTRAINT fk_fd2f9c982989f1fd FOREIGN KEY (invoice_id) REFERENCES invoice (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('DROP INDEX IDX_85C9DF85241CC2BA');
        $this->addSql('ALTER TABLE verification_document_partner DROP verification_document_id');
        $this->addSql('DROP INDEX IDX_F506451A2989F1FD');
        $this->addSql('DROP INDEX IDX_F506451A8D9F6D38');
        $this->addSql('ALTER TABLE archive_document DROP invoice_id');
        $this->addSql('ALTER TABLE archive_document DROP order_id');
        $this->addSql('DROP INDEX IDX_8B587A955330F055');
        $this->addSql('ALTER TABLE identification_element DROP business_partner_id');
    }
}
