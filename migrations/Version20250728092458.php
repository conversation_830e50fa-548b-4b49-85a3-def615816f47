<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250728092458 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE INDEX IDX_F52993985330F055 ON "order" (business_partner_id)');
        $this->addSql('CREATE INDEX IDX_E7296AAA5330F055 ON supervision_document_partner (business_partner_id)');
        $this->addSql('CREATE INDEX IDX_85C9DF855330F055 ON verification_document_partner (business_partner_id)');
        $this->addSql('CREATE INDEX IDX_38D3D55D5330F055 ON waste_eco_savings (business_partner_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_F52993985330F055');
        $this->addSql('DROP INDEX IDX_E7296AAA5330F055');
        $this->addSql('DROP INDEX IDX_85C9DF855330F055');
        $this->addSql('DROP INDEX IDX_38D3D55D5330F055');
    }
}
