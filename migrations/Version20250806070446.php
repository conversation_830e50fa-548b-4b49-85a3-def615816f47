<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250806070446 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE service_location_group (group_id UUID NOT NULL, service_location_id UUID NOT NULL, PRIMARY KEY (group_id, service_location_id))');
        $this->addSql('CREATE INDEX IDX_18FA5C56FE54D947 ON service_location_group (group_id)');
        $this->addSql('CREATE INDEX IDX_18FA5C56CAD70722 ON service_location_group (service_location_id)');
        $this->addSql('ALTER TABLE service_location_group ADD CONSTRAINT FK_18FA5C56FE54D947 FOREIGN KEY (group_id) REFERENCES "group" (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE service_location_group ADD CONSTRAINT FK_18FA5C56CAD70722 FOREIGN KEY (service_location_id) REFERENCES service_location (id) NOT DEFERRABLE');
        $this->addSql('ALTER TABLE "group" ADD business_partner_id UUID DEFAULT NULL');
        $this->addSql('ALTER TABLE "group" ADD CONSTRAINT FK_6DC044C55330F055 FOREIGN KEY (business_partner_id) REFERENCES business_partner (id)');
        $this->addSql('CREATE INDEX IDX_6DC044C55330F055 ON "group" (business_partner_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE service_location_group DROP CONSTRAINT FK_18FA5C56FE54D947');
        $this->addSql('ALTER TABLE service_location_group DROP CONSTRAINT FK_18FA5C56CAD70722');
        $this->addSql('DROP TABLE service_location_group');
        $this->addSql('ALTER TABLE "group" DROP CONSTRAINT FK_6DC044C55330F055');
        $this->addSql('DROP INDEX IDX_6DC044C55330F055');
        $this->addSql('ALTER TABLE "group" DROP business_partner_id');
    }
}
