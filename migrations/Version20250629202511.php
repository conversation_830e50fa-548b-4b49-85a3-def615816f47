<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250629202511 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE archive_document (ext_id VARCHAR(2) NOT NULL, ext_document_id VARCHAR(40) NOT NULL, document_type VARCHAR(10) NOT NULL, storage_date DATE NOT NULL, expiry_date DATE DEFAULT NULL, ext_object VARCHAR(10) NOT NULL, ext_object_id VARCHAR(50) NOT NULL, value VARCHAR(255) DEFAULT NULL, mime_type VARCHAR(100) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F506451AE3464F48 ON archive_document (ext_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F506451A4E59C462 ON archive_document (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_F506451A5D04BFAD7BD7CAF4FD2E66F4 ON archive_document (ext_id, ext_object, ext_object_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE bank_account (ext_id VARCHAR(4) DEFAULT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, account_holder VARCHAR(60) DEFAULT NULL, iban VARCHAR(34) NOT NULL, bic VARCHAR(11) NOT NULL, bank_country VARCHAR(3) DEFAULT NULL, sepa_flag BOOLEAN DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_53A23E0AA6689BB8 ON bank_account (ext_business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_53A23E0A4E59C462 ON bank_account (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_53A23E0AA6689BB8FAD56E62 ON bank_account (ext_business_partner_id, iban) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE business_partner (ext_id VARCHAR(10) NOT NULL, type VARCHAR(1) NOT NULL, industry_code VARCHAR(10) DEFAULT NULL, title VARCHAR(20) DEFAULT NULL, name VARCHAR(120) NOT NULL, email VARCHAR(241) DEFAULT NULL, telephone VARCHAR(30) NOT NULL, mobile_phone VARCHAR(30) DEFAULT NULL, tax_number VARCHAR(20) DEFAULT NULL, deleted BOOLEAN DEFAULT NULL, order_block VARCHAR(2) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, street VARCHAR(60) NOT NULL, house_number VARCHAR(10) NOT NULL, postal_code VARCHAR(10) NOT NULL, city VARCHAR(40) NOT NULL, district VARCHAR(40) DEFAULT NULL, country VARCHAR(3) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6C18855B4E59C462 ON business_partner (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_6C18855B5D04BFAD ON business_partner (ext_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE contact_person (ext_id VARCHAR(10) NOT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, ext_sales_organisation_id VARCHAR(4) NOT NULL, function VARCHAR(2) DEFAULT NULL, role VARCHAR(2) NOT NULL, title VARCHAR(20) DEFAULT NULL, last_name VARCHAR(40) DEFAULT NULL, first_name VARCHAR(40) DEFAULT NULL, country VARCHAR(3) DEFAULT NULL, email VARCHAR(241) DEFAULT NULL, telephone VARCHAR(30) DEFAULT NULL, mobile_phone VARCHAR(30) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A44EE6F75D04BFAD ON contact_person (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A44EE6F7A6689BB8 ON contact_person (ext_business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A44EE6F7D95539F8 ON contact_person (ext_sales_organisation_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A44EE6F74E59C462 ON contact_person (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_A44EE6F75D04BFADA6689BB8D95539F857698A6A ON contact_person (ext_id, ext_business_partner_id, ext_sales_organisation_id, role) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE contract (ext_id VARCHAR(10) NOT NULL, ext_sales_organisation_id VARCHAR(4) NOT NULL, ext_route_id VARCHAR(10) DEFAULT NULL, shipping_point VARCHAR(4) DEFAULT NULL, contract_start_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, contract_end_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, contract_type VARCHAR(2) NOT NULL, description VARCHAR(40) NOT NULL, ext_service_location_id VARCHAR(30) DEFAULT NULL, payment_term VARCHAR(4) NOT NULL, payment_method VARCHAR(20) DEFAULT NULL, ext_transaction_id VARCHAR(24) DEFAULT NULL, ext_customer_purchase_order_id VARCHAR(20) DEFAULT NULL, customer_purchase_order_type VARCHAR(4) NOT NULL, discount VARCHAR(12) DEFAULT NULL, nt_relevance BOOLEAN DEFAULT NULL, customer_group VARCHAR(3) DEFAULT NULL, ext_subcontractor_id VARCHAR(10) DEFAULT NULL, price_transport_sub DOUBLE PRECISION DEFAULT NULL, price_recycling_sub DOUBLE PRECISION DEFAULT NULL, price_rent_sub DOUBLE PRECISION DEFAULT NULL, contract_doc_type VARCHAR(4) NOT NULL, collection_point_type VARCHAR(2) DEFAULT NULL, collection_point_text VARCHAR(60) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F2859D95539F8 ON contract (ext_sales_organisation_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F28598C42AE0B ON contract (ext_route_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F28593F8F6CCF ON contract (ext_service_location_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F28594E59C462 ON contract (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_E98F28595D04BFAD ON contract (ext_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE contract_partner (ext_id VARCHAR(10) NOT NULL, ext_contract_id VARCHAR(10) NOT NULL, ext_contract_pos_id VARCHAR(6) DEFAULT NULL, role VARCHAR(2) NOT NULL, title VARCHAR(20) DEFAULT NULL, name VARCHAR(80) NOT NULL, street VARCHAR(60) DEFAULT NULL, house_number VARCHAR(10) DEFAULT NULL, postal_code VARCHAR(10) DEFAULT NULL, city VARCHAR(40) DEFAULT NULL, district VARCHAR(40) DEFAULT NULL, country VARCHAR(3) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_397A43DE5D04BFAD ON contract_partner (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_397A43DE50FD782 ON contract_partner (ext_contract_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_397A43DE4E59C462 ON contract_partner (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_397A43DE5D04BFAD50FD782AAA31B4B57698A6A ON contract_partner (ext_id, ext_contract_id, ext_contract_pos_id, role) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE contract_position (ext_id VARCHAR(10) NOT NULL, ext_pos_id VARCHAR(6) NOT NULL, ext_parent_pos_id VARCHAR(6) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit_om VARCHAR(3) NOT NULL, net_value DOUBLE PRECISION DEFAULT NULL, currency VARCHAR(5) NOT NULL, price_index VARCHAR(3) DEFAULT NULL, material_type VARCHAR(4) NOT NULL, ext_material_id VARCHAR(40) NOT NULL, material_group VARCHAR(4) NOT NULL, material_text VARCHAR(200) NOT NULL, ext_waste_disposal_site_id VARCHAR(20) NOT NULL, waste_disposal_site_description VARCHAR(254) NOT NULL, rent_type VARCHAR(2) DEFAULT NULL, certificate_number VARCHAR(12) DEFAULT NULL, certificate_type VARCHAR(2) DEFAULT NULL, config_name VARCHAR(40) DEFAULT NULL, config_value VARCHAR(70) DEFAULT NULL, display BOOLEAN NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D39BFF0A5D04BFAD ON contract_position (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D39BFF0A4E59C462 ON contract_position (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_D39BFF0A5D04BFAD92723EB6 ON contract_position (ext_id, ext_pos_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE contract_service_location (ext_id VARCHAR(19) NOT NULL, ext_pos_id VARCHAR(10) NOT NULL, valid_to TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, valid_from TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, ext_contract_id VARCHAR(10) NOT NULL, ext_contract_pos_id VARCHAR(6) DEFAULT NULL, equipment VARCHAR(18) NOT NULL, serial_number VARCHAR(18) NOT NULL, ext_container_material_id VARCHAR(40) NOT NULL, ext_service_location_id VARCHAR(30) NOT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, timeto TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, timefr TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FA5397895D04BFAD ON contract_service_location (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FA53978950FD782 ON contract_service_location (ext_contract_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FA5397893F8F6CCF ON contract_service_location (ext_service_location_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FA539789A6689BB8 ON contract_service_location (ext_business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FA5397894E59C462 ON contract_service_location (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_FA5397895D04BFAD92723EB68C3213D0 ON contract_service_location (ext_id, ext_pos_id, valid_to) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE identification_element (ext_business_partner_id VARCHAR(10) NOT NULL, ext_identification_category VARCHAR(6) NOT NULL, ext_identification_number VARCHAR(60) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8B587A95A6689BB8 ON identification_element (ext_business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8B587A954E59C462 ON identification_element (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_8B587A95A6689BB86C482CD5C6F98C ON identification_element (ext_business_partner_id, ext_identification_category, ext_identification_number) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice (ext_id VARCHAR(10) NOT NULL, billing_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(4) NOT NULL, ext_cancelled_billing_id VARCHAR(10) DEFAULT NULL, ext_sales_organisation_id VARCHAR(4) NOT NULL, net_value DOUBLE PRECISION NOT NULL, currency VARCHAR(5) NOT NULL, ext_payer_id VARCHAR(10) NOT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_90651744D95539F8 ON invoice (ext_sales_organisation_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_90651744A6689BB8 ON invoice (ext_business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_906517444E59C462 ON invoice (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_906517445D04BFAD ON invoice (ext_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_archive_document (ext_document_id VARCHAR(40) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, invoice_id UUID NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FD2F9C982989F1FD ON invoice_archive_document (invoice_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_FD2F9C98E3464F48 ON invoice_archive_document (ext_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_FD2F9C982989F1FDE3464F48 ON invoice_archive_document (invoice_id, ext_document_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_contract (ext_id VARCHAR(10) NOT NULL, ext_invoice_id VARCHAR(10) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F6A772015D04BFAD ON invoice_contract (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F6A772014E59C462 ON invoice_contract (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_F6A772015D04BFAD3F3B3D88 ON invoice_contract (ext_id, ext_invoice_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE notice_text (ext_id VARCHAR(10) NOT NULL, ext_pos_id VARCHAR(5) NOT NULL, end_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(5) NOT NULL, content VARCHAR(50) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_69C992365D04BFAD ON notice_text (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_69C992364E59C462 ON notice_text (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_69C992365D04BFAD92723EB6845CBB3E8CDE5729 ON notice_text (ext_id, ext_pos_id, end_date, type) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE "order" (ext_id VARCHAR(20) NOT NULL, ext_pos_id VARCHAR(4) NOT NULL, ext_service_id VARCHAR(10) NOT NULL, ext_service_pos_id VARCHAR(5) NOT NULL, ext_object_id VARCHAR(22) NOT NULL, order_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, order_status VARCHAR(1) NOT NULL, certificate_number_internal VARCHAR(10) DEFAULT NULL, certificate_number VARCHAR(20) DEFAULT NULL, disposal_document_uns VARCHAR(15) DEFAULT NULL, disposal_document_bgs VARCHAR(15) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F52993985D04BFAD ON "order" (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F5299398FBEE6593 ON "order" (ext_service_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F52993984E59C462 ON "order" (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_F5299398FD2E66F4 ON "order" (ext_object_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_F52993985D04BFAD92723EB6 ON "order" (ext_id, ext_pos_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE order_archive_document (ext_document_id VARCHAR(40) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, order_id UUID NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_971051068D9F6D38 ON order_archive_document (order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_97105106E3464F48 ON order_archive_document (ext_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_971051068D9F6D38E3464F48 ON order_archive_document (order_id, ext_document_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE route (ext_id VARCHAR(10) NOT NULL, description VARCHAR(30) NOT NULL, start_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2C420794E59C462 ON route (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_2C420795D04BFAD ON route (ext_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE sales_organisation (ext_id VARCHAR(4) NOT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_7AAC3F0C5D04BFAD ON sales_organisation (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_7AAC3F0CA6689BB8 ON sales_organisation (ext_business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_7AAC3F0C4E59C462 ON sales_organisation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_7AAC3F0C5D04BFADA6689BB8 ON sales_organisation (ext_id, ext_business_partner_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE service (ext_id VARCHAR(10) NOT NULL, ext_pos_id VARCHAR(5) NOT NULL, start_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, end_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, period VARCHAR(1) NOT NULL, ext_contract_id VARCHAR(10) NOT NULL, ext_contract_pos_id VARCHAR(6) NOT NULL, service_type VARCHAR(2) NOT NULL, ext_container_material_id VARCHAR(40) NOT NULL, ext_waste_material_id VARCHAR(40) NOT NULL, ext_service_location_id VARCHAR(30) NOT NULL, container_count INT NOT NULL, equipment VARCHAR(18) NOT NULL, ext_route_id VARCHAR(10) NOT NULL, service_frequency VARCHAR(1) NOT NULL, daily_frequency VARCHAR(1) NOT NULL, weekly_frequency VARCHAR(2) DEFAULT NULL, monthly_frequency VARCHAR(2) DEFAULT NULL, monthly_frequency_day VARCHAR(2) DEFAULT NULL, weekday VARCHAR(1) DEFAULT NULL, link VARCHAR(255) DEFAULT NULL, mime_type VARCHAR(5) DEFAULT NULL, timeto TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, timefr TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E19D9AD25D04BFAD ON service (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E19D9AD250FD782 ON service (ext_contract_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E19D9AD23F8F6CCF ON service (ext_service_location_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E19D9AD28C42AE0B ON service (ext_route_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E19D9AD24E59C462 ON service (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_E19D9AD25D04BFAD92723EB6845CBB3E ON service (ext_id, ext_pos_id, end_date) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE service_frequency (ext_id VARCHAR(10) NOT NULL, ext_pos_id VARCHAR(5) NOT NULL, start_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, end_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, service_type VARCHAR(2) NOT NULL, service_frequency VARCHAR(1) NOT NULL, daily_frequency VARCHAR(1) DEFAULT NULL, weekly_frequency VARCHAR(2) DEFAULT NULL, monthly_frequency VARCHAR(2) DEFAULT NULL, monthly_frequency_day VARCHAR(2) DEFAULT NULL, weekday VARCHAR(1) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_7EEE82595D04BFAD ON service_frequency (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_7EEE82594E59C462 ON service_frequency (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_7EEE82595D04BFAD92723EB6 ON service_frequency (ext_id, ext_pos_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE service_location (ext_id VARCHAR(30) NOT NULL, name VARCHAR(80) NOT NULL, additional_information VARCHAR(30) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, street VARCHAR(60) NOT NULL, house_number VARCHAR(10) NOT NULL, postal_code VARCHAR(10) NOT NULL, city VARCHAR(40) NOT NULL, district VARCHAR(40) DEFAULT NULL, country VARCHAR(3) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A7E8D2F64E59C462 ON service_location (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_A7E8D2F65D04BFAD ON service_location (ext_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE sign_data (ext_doc_id VARCHAR(50) NOT NULL, sequence INT NOT NULL, ext_id VARCHAR(70) NOT NULL, count INT NOT NULL, role VARCHAR(6) NOT NULL, sign_date_time TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, signer VARCHAR(70) NOT NULL, status VARCHAR(30) NOT NULL, status_description VARCHAR(60) NOT NULL, ext_valid BOOLEAN NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_AA98380A5D04BFAD ON sign_data (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_AA98380A4E59C462 ON sign_data (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_AA98380A5286D72B5D04BFAD85D94462 ON sign_data (sequence, ext_id, count) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE supervision_document (ext_id VARCHAR(15) NOT NULL, ext_doc_id VARCHAR(50) NOT NULL, ext_verification_document_id VARCHAR(10) NOT NULL, ext_verification_document_version VARCHAR(2) NOT NULL, certificate_number VARCHAR(20) NOT NULL, ext_route_id VARCHAR(10) NOT NULL, description VARCHAR(254) NOT NULL, avv_id VARCHAR(6) NOT NULL, avv_description VARCHAR(255) NOT NULL, ext_waste_material_id VARCHAR(40) DEFAULT NULL, amount DOUBLE PRECISION NOT NULL, amount_unit_om VARCHAR(3) NOT NULL, ext_order_object_id VARCHAR(22) NOT NULL, ext_order_id VARCHAR(20) NOT NULL, ext_order_pos_id VARCHAR(4) NOT NULL, order_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, ext_sales_organisation_id VARCHAR(4) NOT NULL, created_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, role VARCHAR(6) NOT NULL, status VARCHAR(30) NOT NULL, data_status VARCHAR(6) DEFAULT NULL, value VARCHAR(255) DEFAULT NULL, mime_type VARCHAR(100) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9755AD22353177D5 ON supervision_document (ext_order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9755AD22D95539F8 ON supervision_document (ext_sales_organisation_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9755AD22131BDB26 ON supervision_document (ext_verification_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9755AD223005EFE3 ON supervision_document (certificate_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9755AD2248418C7B ON supervision_document (ext_order_object_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9755AD224E59C462 ON supervision_document (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_9755AD225D04BFAD ON supervision_document (ext_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_9755AD225A2C29A4 ON supervision_document (ext_doc_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE supervision_document_partner (ext_doc_id VARCHAR(50) NOT NULL, ext_id VARCHAR(9) NOT NULL, ext_check_id VARCHAR(1) NOT NULL, type VARCHAR(5) NOT NULL, layername VARCHAR(20) NOT NULL, ext_supervision_document_id VARCHAR(15) NOT NULL, name VARCHAR(70) NOT NULL, contact_person VARCHAR(35) DEFAULT NULL, telephone VARCHAR(30) DEFAULT NULL, fax VARCHAR(30) DEFAULT NULL, email VARCHAR(241) DEFAULT NULL, licence_plate_number1 VARCHAR(15) DEFAULT NULL, licence_plate_number2 VARCHAR(15) DEFAULT NULL, amount DOUBLE PRECISION NOT NULL, amount_unit_om VARCHAR(3) NOT NULL, takeover_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, receipt_flag BOOLEAN DEFAULT NULL, role_allowed BOOLEAN DEFAULT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, street VARCHAR(60) NOT NULL, house_number VARCHAR(10) NOT NULL, postal_code VARCHAR(10) NOT NULL, city VARCHAR(40) NOT NULL, district VARCHAR(40) DEFAULT NULL, country VARCHAR(3) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E7296AAA5D04BFAD ON supervision_document_partner (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E7296AAA82025C1D ON supervision_document_partner (ext_supervision_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E7296AAA4E59C462 ON supervision_document_partner (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_E7296AAA5D04BFADC83D9F0A8CDE572982025C1D ON supervision_document_partner (ext_id, ext_check_id, type, ext_supervision_document_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE supervision_document_sign_data (ext_sign_data_id VARCHAR(70) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, supervision_document_id UUID NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8EEE9A515D2030F6 ON supervision_document_sign_data (supervision_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8EEE9A514207F25B ON supervision_document_sign_data (ext_sign_data_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_8EEE9A515D2030F64207F25B ON supervision_document_sign_data (supervision_document_id, ext_sign_data_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE "user" (username VARCHAR(255) NOT NULL, tenant VARCHAR(5) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_8D93D649F85E0677 ON "user" (username) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE verification_document (ext_id VARCHAR(10) NOT NULL, ext_version VARCHAR(2) NOT NULL, certificate_number VARCHAR(12) NOT NULL, certificate_type VARCHAR(2) NOT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, ext_sales_organisation_id VARCHAR(4) NOT NULL, ext_contract_id VARCHAR(10) NOT NULL, avv_id VARCHAR(6) NOT NULL, avv_description VARCHAR(255) NOT NULL, ext_service_location_id VARCHAR(30) NOT NULL, approved_amount_document DOUBLE PRECISION NOT NULL, approved_amount_year DOUBLE PRECISION DEFAULT NULL, approved_rem_amount_document DOUBLE PRECISION NOT NULL, approved_rem_amount_year DOUBLE PRECISION NOT NULL, amount_unit_om VARCHAR(3) NOT NULL, deletion_flag BOOLEAN DEFAULT NULL, application_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, date_customer TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, date_disposer TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, confirmation_authority TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, approval TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, end_date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, locked_date TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, ext_object_id VARCHAR(22) NOT NULL, description VARCHAR(100) NOT NULL, ext_doc_id VARCHAR(50) NOT NULL, status VARCHAR(40) NOT NULL, status_description VARCHAR(100) NOT NULL, data_status VARCHAR(6) DEFAULT NULL, value VARCHAR(255) DEFAULT NULL, mime_type VARCHAR(100) DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_29A60264A6689BB8 ON verification_document (ext_business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_29A60264D95539F8 ON verification_document (ext_sales_organisation_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_29A6026450FD782 ON verification_document (ext_contract_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_29A602643005EFE3 ON verification_document (certificate_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_29A602644E59C462 ON verification_document (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_29A602645D04BFADACA331F4 ON verification_document (ext_id, ext_version) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_29A60264FD2E66F4 ON verification_document (ext_object_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_29A602645A2C29A4 ON verification_document (ext_doc_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE verification_document_partner (ext_doc_id VARCHAR(50) NOT NULL, ext_id VARCHAR(9) NOT NULL, ext_check_id VARCHAR(1) DEFAULT NULL, type VARCHAR(6) NOT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, ext_verification_document_id VARCHAR(10) NOT NULL, ext_verification_document_version VARCHAR(2) NOT NULL, name VARCHAR(35) NOT NULL, contact_person VARCHAR(35) DEFAULT NULL, telephone VARCHAR(30) DEFAULT NULL, fax VARCHAR(30) DEFAULT NULL, email VARCHAR(241) DEFAULT NULL, role_allowed BOOLEAN NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, street VARCHAR(60) NOT NULL, house_number VARCHAR(10) NOT NULL, postal_code VARCHAR(10) NOT NULL, city VARCHAR(40) NOT NULL, district VARCHAR(40) DEFAULT NULL, country VARCHAR(3) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_85C9DF855D04BFAD ON verification_document_partner (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_85C9DF85A6689BB8 ON verification_document_partner (ext_business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_85C9DF85131BDB26 ON verification_document_partner (ext_verification_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_85C9DF854E59C462 ON verification_document_partner (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_85C9DF855D04BFADC83D9F0A8CDE5729131BDB26A75156E5 ON verification_document_partner (ext_id, ext_check_id, type, ext_verification_document_id, ext_verification_document_version) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE verification_document_sign_data (ext_sign_data_id VARCHAR(70) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, verification_document_id UUID NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_27F987DF241CC2BA ON verification_document_sign_data (verification_document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_27F987DF4207F25B ON verification_document_sign_data (ext_sign_data_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_27F987DF241CC2BA4207F25B ON verification_document_sign_data (verification_document_id, ext_sign_data_id) NULLS NOT DISTINCT
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_archive_document ADD CONSTRAINT FK_FD2F9C982989F1FD FOREIGN KEY (invoice_id) REFERENCES invoice (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_archive_document ADD CONSTRAINT FK_971051068D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supervision_document_sign_data ADD CONSTRAINT FK_8EEE9A515D2030F6 FOREIGN KEY (supervision_document_id) REFERENCES supervision_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE verification_document_sign_data ADD CONSTRAINT FK_27F987DF241CC2BA FOREIGN KEY (verification_document_id) REFERENCES verification_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_archive_document DROP CONSTRAINT FK_FD2F9C982989F1FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE order_archive_document DROP CONSTRAINT FK_971051068D9F6D38
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supervision_document_sign_data DROP CONSTRAINT FK_8EEE9A515D2030F6
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE verification_document_sign_data DROP CONSTRAINT FK_27F987DF241CC2BA
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE archive_document
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE bank_account
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE business_partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE contact_person
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE contract
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE contract_partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE contract_position
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE contract_service_location
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE identification_element
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice_archive_document
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice_contract
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE notice_text
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE "order"
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE order_archive_document
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE route
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE sales_organisation
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE service
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE service_frequency
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE service_location
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE sign_data
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE supervision_document
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE supervision_document_partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE supervision_document_sign_data
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE "user"
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE verification_document
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE verification_document_partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE verification_document_sign_data
        SQL);
    }
}
