<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250815091301 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE waste_statistic ALTER ext_contract_id DROP NOT NULL');
        $this->addSql('ALTER TABLE waste_statistic RENAME COLUMN material TO material_text');
        $this->addSql('CREATE INDEX IDX_9020A742D926543A ON waste_statistic (waste_statistic_location_id)');
        $this->addSql('ALTER TABLE waste_statistic_location ALTER ext_id DROP NOT NULL');
        $this->addSql('ALTER TABLE waste_statistic_location ALTER address DROP NOT NULL');
        $this->addSql('ALTER TABLE waste_statistic_location ALTER name DROP NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX IDX_9020A742D926543A');
        $this->addSql('ALTER TABLE waste_statistic ALTER ext_contract_id SET NOT NULL');
        $this->addSql('ALTER TABLE waste_statistic RENAME COLUMN material_text TO material');
        $this->addSql('ALTER TABLE waste_statistic_location ALTER ext_id SET NOT NULL');
        $this->addSql('ALTER TABLE waste_statistic_location ALTER address SET NOT NULL');
        $this->addSql('ALTER TABLE waste_statistic_location ALTER name SET NOT NULL');
    }
}
