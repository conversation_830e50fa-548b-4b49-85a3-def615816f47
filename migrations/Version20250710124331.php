<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250710124331 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX idx_d39bff0a2576e0fd');
        $this->addSql('CREATE INDEX IDX_D39BFF0A2576E0FD4E59C462C3719B10 ON contract_position (contract_id, tenant, ext_material_id)');
        $this->addSql('DROP INDEX idx_e19d9ad25d04bfad');
        $this->addSql('CREATE INDEX IDX_E19D9AD25D04BFAD92723EB6 ON service (ext_id, ext_pos_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP INDEX IDX_D39BFF0A2576E0FD4E59C462C3719B10');
        $this->addSql('CREATE INDEX idx_d39bff0a2576e0fd ON contract_position (contract_id)');
        $this->addSql('DROP INDEX IDX_E19D9AD25D04BFAD92723EB6');
        $this->addSql('CREATE INDEX idx_e19d9ad25d04bfad ON service (ext_id)');
    }
}
