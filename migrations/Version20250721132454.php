<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250721132454 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE material_service_type (ext_material_id VARCHAR(10) NOT NULL, service_type VARCHAR(10) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_4CE577C5C3719B10 ON material_service_type (ext_material_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE material_service_type');
    }
}
