<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250731073545 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE business_partner ADD federal_state VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE service_location ADD federal_state VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE supervision_document_partner ADD federal_state VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD federal_state VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE verification_document_partner ADD federal_state VARCHAR(50) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE business_partner DROP federal_state');
        $this->addSql('ALTER TABLE service_location DROP federal_state');
        $this->addSql('ALTER TABLE supervision_document_partner DROP federal_state');
        $this->addSql('ALTER TABLE supplier DROP federal_state');
        $this->addSql('ALTER TABLE verification_document_partner DROP federal_state');
    }
}
