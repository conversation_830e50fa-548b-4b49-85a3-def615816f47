<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250709073750 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX uniq_4cfd86565d04bfad');
        $this->addSql('ALTER TABLE order_confirmation DROP ext_id');
        $this->addSql('DROP INDEX uniq_9b2a6c7e5d04bfad');
        $this->addSql('ALTER TABLE supplier ADD district VARCHAR(40) DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier DROP ext_id');
        $this->addSql('ALTER TABLE supplier ALTER street TYPE VARCHAR(60)');
        $this->addSql('ALTER TABLE supplier ALTER house_number TYPE VARCHAR(10)');
        $this->addSql('ALTER TABLE supplier ALTER city TYPE VARCHAR(40)');
        $this->addSql('ALTER TABLE supplier ALTER postal_code TYPE VARCHAR(10)');
        $this->addSql('ALTER TABLE supplier ALTER country TYPE VARCHAR(3)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE supplier ADD ext_id VARCHAR(20) NOT NULL');
        $this->addSql('ALTER TABLE supplier DROP district');
        $this->addSql('ALTER TABLE supplier ALTER street TYPE VARCHAR(50)');
        $this->addSql('ALTER TABLE supplier ALTER house_number TYPE VARCHAR(50)');
        $this->addSql('ALTER TABLE supplier ALTER postal_code TYPE VARCHAR(50)');
        $this->addSql('ALTER TABLE supplier ALTER city TYPE VARCHAR(50)');
        $this->addSql('ALTER TABLE supplier ALTER country TYPE VARCHAR(50)');
        $this->addSql('CREATE UNIQUE INDEX uniq_9b2a6c7e5d04bfad ON supplier (ext_id)');
        $this->addSql('ALTER TABLE order_confirmation ADD ext_id VARCHAR(50) NOT NULL');
        $this->addSql('CREATE UNIQUE INDEX uniq_4cfd86565d04bfad ON order_confirmation (ext_id)');
    }
}
