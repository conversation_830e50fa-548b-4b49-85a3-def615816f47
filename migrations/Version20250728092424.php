<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250728092424 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create entity order activity.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE order_activity (type VARCHAR(255) NOT NULL, name VARCHAR(255) DEFAULT NULL, description VARCHAR(255) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, order_id UUID DEFAULT NULL, PRIMARY KEY (id))');
        $this->addSql('CREATE INDEX IDX_4D8CEBEB8D9F6D38 ON order_activity (order_id)');
        $this->addSql('ALTER TABLE order_activity ADD CONSTRAINT FK_4D8CEBEB8D9F6D38 FOREIGN KEY (order_id) REFERENCES "order" (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE order_activity DROP CONSTRAINT FK_4D8CEBEB8D9F6D38');
        $this->addSql('DROP TABLE order_activity');
    }
}
