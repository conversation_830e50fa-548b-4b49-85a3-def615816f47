<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250718124444 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE bank_account ALTER business_partner_id TYPE UUID');
        $this->addSql('ALTER TABLE bank_account ALTER business_partner_id DROP NOT NULL');
        $this->addSql('ALTER TABLE contact_person ALTER business_partner_id TYPE UUID');
        $this->addSql('ALTER TABLE contact_person ALTER business_partner_id DROP NOT NULL');
        $this->addSql('ALTER TABLE identification_element ALTER business_partner_id TYPE UUID');
        $this->addSql('ALTER TABLE identification_element ALTER business_partner_id DROP NOT NULL');
        $this->addSql('ALTER TABLE sales_organisation ALTER business_partner_id TYPE UUID');
        $this->addSql('ALTER TABLE sales_organisation ALTER business_partner_id DROP NOT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE bank_account ALTER business_partner_id TYPE UUID');
        $this->addSql('ALTER TABLE bank_account ALTER business_partner_id SET NOT NULL');
        $this->addSql('ALTER TABLE contact_person ALTER business_partner_id TYPE UUID');
        $this->addSql('ALTER TABLE contact_person ALTER business_partner_id SET NOT NULL');
        $this->addSql('ALTER TABLE identification_element ALTER business_partner_id TYPE UUID');
        $this->addSql('ALTER TABLE identification_element ALTER business_partner_id SET NOT NULL');
        $this->addSql('ALTER TABLE sales_organisation ALTER business_partner_id TYPE UUID');
        $this->addSql('ALTER TABLE sales_organisation ALTER business_partner_id SET NOT NULL');
    }
}
