<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250701143334 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE order_confirmation (ext_id VARCHAR(50) NOT NULL, email VARCHAR(50) NOT NULL, name VARCHAR(50) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4CFD86564E59C462 ON order_confirmation (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_4CFD86565D04BFAD ON order_confirmation (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE service_product (ext_id VARCHAR(20) NOT NULL, service_product VARCHAR(6) NOT NULL, container VARCHAR(8) NOT NULL, service_type VARCHAR(50) NOT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1CCE56314E59C462 ON service_product (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_1CCE56315D04BFAD ON service_product (ext_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE supplier (ext_id VARCHAR(20) NOT NULL, region VARCHAR(50) NOT NULL, location VARCHAR(50) NOT NULL, description VARCHAR(50) NOT NULL, number VARCHAR(50) NOT NULL, delay TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, name VARCHAR(50) NOT NULL, vkorg VARCHAR(50) NOT NULL, delay_order TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, street VARCHAR(50) NOT NULL, house_number VARCHAR(50) NOT NULL, city VARCHAR(50) NOT NULL, postal_code VARCHAR(50) NOT NULL, country VARCHAR(50) NOT NULL, email VARCHAR(50) NOT NULL, phone VARCHAR(50) NOT NULL, opening_hours VARCHAR(50) NOT NULL, delay_order_monday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, delay_order_tuesday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, delay_order_wednesday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, delay_order_thursday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, delay_order_friday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY(id))
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9B2A6C7E4E59C462 ON supplier (tenant)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_9B2A6C7E5D04BFAD ON supplier (ext_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE order_confirmation
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE service_product
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE supplier
        SQL);
    }
}
