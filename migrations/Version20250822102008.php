<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250822102008 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE order_confirmation ALTER supplier_id DROP NOT NULL');
        $this->addSql('ALTER TABLE order_confirmation ADD CONSTRAINT FK_4CFD86562ADD6D8C FOREIGN KEY (supplier_id) REFERENCES supplier (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE order_confirmation DROP CONSTRAINT FK_4CFD86562ADD6D8C');
        $this->addSql('ALTER TABLE order_confirmation ALTER supplier_id SET NOT NULL');
    }
}
