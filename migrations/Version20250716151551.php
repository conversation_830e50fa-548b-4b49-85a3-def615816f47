<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250716151551 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE waste_eco_savings (ext_id VARCHAR(10) NOT NULL, division VARCHAR(1) NOT NULL, ext_business_partner_id VARCHAR(10) NOT NULL, year VARCHAR(4) NOT NULL, month VARCHAR(2) NOT NULL, product_hierarchy VARCHAR(20) NOT NULL, co2_savings VARCHAR(10) DEFAULT NULL, co2_emissions_transport VARCHAR(10) DEFAULT NULL, co2_emissions_recycling VARCHAR(10) DEFAULT NULL, co2_emissions VARCHAR(10) DEFAULT NULL, total_weight VARCHAR(10) DEFAULT NULL, invoice_amount VARCHAR(10) DEFAULT NULL, billing_amount VARCHAR(10) DEFAULT NULL, business_partner_id UUID DEFAULT NULL, id UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, modified_by UUID NOT NULL, tenant VARCHAR(5) NOT NULL, PRIMARY KEY (id))');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE waste_eco_savings');
    }
}
