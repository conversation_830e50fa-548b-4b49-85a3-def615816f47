<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250730112244 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE supplier ADD order_delay VARCHAR(5) DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD order_delay_monday VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD order_delay_tuesday VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD order_delay_wednesday VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD order_delay_thursday VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD order_delay_friday VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier DROP delay');
        $this->addSql('ALTER TABLE supplier DROP delay_order');
        $this->addSql('ALTER TABLE supplier DROP delay_order_monday');
        $this->addSql('ALTER TABLE supplier DROP delay_order_tuesday');
        $this->addSql('ALTER TABLE supplier DROP delay_order_wednesday');
        $this->addSql('ALTER TABLE supplier DROP delay_order_thursday');
        $this->addSql('ALTER TABLE supplier DROP delay_order_friday');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE supplier ADD delay TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD delay_order TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL');
        $this->addSql('ALTER TABLE supplier ADD delay_order_monday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD delay_order_tuesday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD delay_order_wednesday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD delay_order_thursday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier ADD delay_order_friday TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE supplier DROP order_delay');
        $this->addSql('ALTER TABLE supplier DROP order_delay_monday');
        $this->addSql('ALTER TABLE supplier DROP order_delay_tuesday');
        $this->addSql('ALTER TABLE supplier DROP order_delay_wednesday');
        $this->addSql('ALTER TABLE supplier DROP order_delay_thursday');
        $this->addSql('ALTER TABLE supplier DROP order_delay_friday');
    }
}
