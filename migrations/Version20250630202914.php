<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250630202914 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE contract ADD business_partner_id UUID DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F28595330F055 ON contract (business_partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_906517445330F055 ON invoice (business_partner_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_906517445330F055
        SQL);
        $this->addSql(<<<'SQL'
            DROP INDEX IDX_E98F28595330F055
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract DROP business_partner_id
        SQL);
    }
}
