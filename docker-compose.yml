name: myprezero

services:
    backend:
        container_name: myprezero_backend
        platform: linux/amd64
        build:
            context: .
            dockerfile: docker/Dockerfile
            target: base
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        volumes:
            - ${APP_VOLUME:-.}:/app
            - var:/app/var
            - caddy_data:/data
            - caddy_config:/config
        depends_on: [ 'db' ]
        ports:
            - '8000:8080'
        restart: unless-stopped
        environment:
            MERCURE_EXTRA_DIRECTIVES: demo
            MERCURE_PUBLISHER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
            MERCURE_SUBSCRIBER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
            TRUSTED_PROXIES: ${TRUSTED_PROXIES:-*********/8,10.0.0.0/8,**********/12,***********/16}
            TRUSTED_HOSTS: ^${SERVER_NAME:-example\.com|localhost}|app
            PHP_IDE_CONFIG: serverName=${XDEBUG_SERVER_NAME:-myprezero}
            XDEBUG_MODE: ${XDEBUG_MODE:-debug} #debug
        extra_hosts:
            - host.docker.internal:host-gateway
        tty: true
        networks: [ myprezero_network ]
        healthcheck:
            test: [ "CMD-SHELL", "exec curl --head -fsS http://localhost:8080" ]
            interval: 10s
            timeout: 10s
            retries: 20

    db:
        container_name: myprezero_db
        image: postgres:17.6-alpine3.22@sha256:3406990b6e4c7192317b6fdc5680498744f6142f01f0287f4ee0420d8c74063c
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        environment:
            POSTGRES_DB: myprezero
            POSTGRES_USER: user
            POSTGRES_PASSWORD: userpwd
            PGDATA: /var/lib/postgresql/data/pgdata
        volumes:
            - database-data:/var/lib/postgresql/data
        ports:
            - '8001:5432'
        networks: [ myprezero_network ]

    mailcatcher:
        container_name: myprezero_mailcatcher
        platform: linux/amd64
        image: yappabe/mailcatcher@sha256:cc7241dc09fe299f899473cafd96ea68bd8f829485213abcae5b6ced16cfec8a
        ports:
            - '8002:1080'
        networks: [ myprezero_network ]

    s3storage:
        container_name: myprezero_s3storage
        image: quay.io/minio/minio:RELEASE.2025-07-23T15-54-02Z@sha256:d249d1fb6966de4d8ad26c04754b545205ff15a62e4fd19ebd0f26fa5baacbc0
        env_file: [ { path: docker/local/.env, required: true }, { path: .env.local, required: false } ]
        ports:
            - '8003:8003'
            - '8004:8004'
        environment:
            MINIO_ROOT_USER: minio
            MINIO_ROOT_PASSWORD: minio123
            MINIO_REGION_NAME: eu01
        command:
            - 'server'
            - '/data'
            - '--address'
            - ':8003'
            - '--console-address'
            - ':8004'
        volumes:
            - s3-data:/data
        networks: [ myprezero_network ]
        healthcheck:
            test: [ "CMD-SHELL", "exec curl --head -fsS http://localhost:8003/minio/health/live" ]
            interval: 10s
            timeout: 10s
            retries: 20

    mockserver:
        container_name: myprezero_mockserver
        platform: linux/amd64
        image: mockserver/mockserver:5.15.0@sha256:0f9ef78c94894ac3e70135d156193b25e23872575d58e2228344964273b4af6b
        ports:
            - '8005:1080'
            - '8006:1090'
        networks: [ myprezero_network ]

    message_queue:
        container_name: myprezero_message_queue
        platform: linux/amd64
        image: rabbitmq:4.1.3@sha256:9005827533ea6f179cb07564f9ffafe96ce151ca49d5549bece49d46e0ab2084
        ports:
            - '8007:5672'
            - '8008:15672'
        environment:
            RABBITMQ_DEFAULT_USER: rabbitmq
            RABBITMQ_DEFAULT_PASS: rabbitmq123
        command: rabbitmq-server
        networks: [ myprezero_network ]

    keycloak:
        container_name: myprezero_keycloak
        platform: linux/amd64
        image: ghcr.io/prezero/keycloak:3.1.1@sha256:936b9d3eb3677d108305d6545a59c623f7fabee97914b6abb9891c252324b0ad
        ports:
            - '8014:8080'
        environment:
            KC_BOOTSTRAP_ADMIN_USERNAME: admin
            KC_BOOTSTRAP_ADMIN_PASSWORD: admin
            KC_DB: postgres
            KC_DB_SCHEMA: public
            KC_DB_URL_DATABASE: keycloak
            KC_DB_USERNAME: postgres
            KC_DB_PASSWORD: password
            KC_DB_URL_HOST: keycloak-db
            KC_DB_URL_PORT: 5432
            KC_HEALTH_ENABLED: true
        command:
            - start-dev
        depends_on:
            - keycloak-db
        networks: [ myprezero_network ]
        healthcheck:
            test: ["CMD-SHELL", "exec 3<> /dev/tcp/127.0.0.1/9000; echo -e 'GET /health/ready HTTP/1.1\\r\\nhost: http://localhost\\r\\nConnection: close\\r\\n\\r\\n' >&3; if [ $? -eq 0 ]; then echo 'Healthcheck Successful'; exit 0; else echo 'Healthcheck Failed'; exit 1; fi;"]
            interval: 10s
            timeout: 10s
            retries: 20

    keycloak-db:
        image: postgres:17.6-alpine3.22@sha256:3406990b6e4c7192317b6fdc5680498744f6142f01f0287f4ee0420d8c74063c
        restart: always
        environment:
            POSTGRES_DB: keycloak
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: password
        ports:
            - '8015:5432'
        networks: [ myprezero_network ]

    keycloak-configuration:
        image: adorsys/keycloak-config-cli:latest@sha256:44fcacaba522c159f4bb23a6bef0dd9e82291c5c84be3ed0ada77f6ff4663fd4
        restart: "no"
        environment:
            KEYCLOAK_URL: http://keycloak:8080/
            KEYCLOAK_USER: admin
            KEYCLOAK_PASSWORD: admin
            KEYCLOAK_SSLVERIFY: false
            KEYCLOAK_AVAILABILITYCHECK_ENABLED: true
            KEYCLOAK_AVAILABILITYCHECK_TIMEOUT: 120s
            IMPORT_FILES_LOCATIONS: /config/*
        volumes:
            - ./docker/local/keycloak-config:/config
        depends_on:
            keycloak:
                condition: service_healthy
        networks: [ myprezero_network ]

    redis:
        image: valkey/valkey:8.1.3-alpine3.22@sha256:d827e7f7552cdee40cc7482dbae9da020f42bc47669af6f71182a4ef76a22773
        ports:
            - '8012:6379'
        networks: [ myprezero_network ]

volumes:
    var:
    caddy_data:
    caddy_config:
    database-data:
    s3-data:

networks:
    myprezero_network:
        name: myprezero_network
