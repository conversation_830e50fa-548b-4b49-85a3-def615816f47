# MyPreZero Backend

## Setup development environment
1. Clone the repo
    ```sh
    <NAME_EMAIL>:prezero/myprezero-sf.git
    ```

1. Copy ``auth.json.dist`` and rename it to ``auth.json``
    ```sh
    cp auth.json.dist auth.json
    ```
1. Add your github token to ``auth.json``
    ```json
    {
      "github-oauth": {
        "github.com": "ghp_abcdefghijklmnopqrstuvwxyz"
      }
    }
    ```

1. Start docker containers
    ```sh
    docker-compose up -d
    ```

1. Go inside container
    ```sh
    docker exec -it myprezero_backend bash
    ```
   or to execute docker commands from local machine
    ```sh
    docker-compose exec backend <command>
    ```

1. Install composer packages
    ```sh
    composer install
    ```

1. Activate githooks
    ```sh
   php vendor/bin/captainhook install
    ```

1. Migrate database
    ```sh
    php bin/console doctrine:migration:migrate
    ```

1. Generate fixtures
    ```sh
    php bin/console hautelook:fixtures:load
    ```

1. Run all tests
    ```sh
    php vendor/bin/codecept run
    ```
   to run only api tests
    ```sh
    php vendor/bin/codecept run Api
    ```
   to run only portal tests
    ```sh
    php vendor/bin/codecept run --group Portal
    ```
   to run only sap europe tests
    ```sh
    php vendor/bin/codecept run --group SapEurope
    ```
   to run tests for specific entities (works for both Portal and SapEurope)
    ```sh
    php vendor/bin/codecept run --group BusinessPartner
    php vendor/bin/codecept run --group Contract
    php vendor/bin/codecept run --group Order
    # etc.
    ```

## Test Execution Order

Tests are executed in the following order to ensure proper data dependencies:
1. **SapEurope tests** - Create test data in the system
2. **Portal tests** - Use the data created by SapEurope tests

This order is automatically enforced by the test scripts (`bin/api-tests.sh` and `run-tests.sh`).

## Available Test Groups

### Main Groups
- **SapEurope** - All SapEurope input tests
- **Portal** - All Portal API tests

### Entity Groups (available for both SapEurope and Portal)
- **BusinessPartner** - Business partner related tests
- **Route** - Route related tests (SapEurope only)
- **ServiceLocation** - Service location related tests
- **Contract** - Contract related tests
- **ContractServiceLocation** - Contract service location related tests
- **Service** - Service related tests (SapEurope only)
- **Invoice** - Invoice related tests
- **Order** - Order related tests
- **VerificationDocument** - Verification document related tests
- **SupervisionDocument** - Supervision document related tests
- **Archive** - Archive document related tests (SapEurope only)

## SapEurope Test Data Configuration

The SapEurope test suite includes a flexible configuration system for test data generation, allowing you to configure different numbers of test data items per object type.

### Overview

The configuration system allows you to:

1. **Configure different counts per object type** - Set different numbers for BusinessPartner, Contract, Service, etc.
2. **Use predefined modes** - Choose from ci, dev, full, or stress test modes
3. **Create custom configurations** - Define your own counts for specific testing scenarios
4. **Respect data dependencies** - The system understands that Contracts depend on BusinessPartners, Services depend on Contracts, etc.

### Predefined Test Modes

- **`ci`** - Fast CI/CD pipeline mode using Create-1.json fixture files (1 real test data item per type)
- **`dev`** - Moderate generated test data for local development (3-12 items per type)
- **`full`** - Uses all real test data from fixture files (all Create-*.json files)
- **`stress`** - Large amounts of generated data for stress testing (50-1000 items per type)
- **`custom`** - User-defined counts per object type

### Object Types and Dependencies

The configuration supports these object types with their dependencies:

- **BusinessPartner** (independent)
- **Route** (independent)
- **ServiceLocation** (independent)
- **Contract** (depends on BusinessPartner + Route)
- **ContractServiceLocation** (depends on Contract + ServiceLocation)
- **Service** (depends on Contract + ServiceLocation)
- **Invoice** (depends on Contract + ArchiveDocument)
- **Order** (depends on Service + Invoice)
- **VerificationDocument** (depends on Contract + ServiceLocation)
- **SupervisionDocument** (depends on VerificationDocument + Route + Order)
- **ArchiveDocument** (independent)

### Usage Examples

#### Fast CI/CD Tests (Uses Create-1.json files)
```sh
export TEST_DATA_MODE=ci
vendor/bin/codecept run --group SapEurope
```

#### Development Testing
```sh
export TEST_DATA_MODE=dev
vendor/bin/codecept run --group SapEurope
```

#### Full Real Data Testing
```sh
export TEST_DATA_MODE=full
vendor/bin/codecept run --group SapEurope
```

#### Stress Testing
```sh
export TEST_DATA_MODE=stress
vendor/bin/codecept run --group SapEurope
```

### Mode Behavior Details

- **CI Mode**: Always uses Create-1.json fixture files for consistent, fast testing
- **Development Mode**: Generates test data, at least half of dependent objects connect to first parent data
- **Full Mode**: Uses all available Create-*.json fixture files (Create-0.json, Create-1.json, etc.)
- **Stress Mode**: Generates large amounts of test data, at least half of dependent objects connect to first parent data

### Custom Configuration

To create a custom configuration, modify the `custom` section in `tests/Support/Data/Config/sap-europe-test-data.json`:

```json
{
  "custom": {
    "BusinessPartner": 5,
    "Route": 3,
    "ServiceLocation": 8,
    "Contract": 10,
    "ContractServiceLocation": 15,
    "Service": 20,
    "Invoice": 12,
    "Order": 25,
    "VerificationDocument": 5,
    "SupervisionDocument": 8,
    "ArchiveDocument": 15
  }
}
```

Then use:
```bash
export TEST_DATA_MODE=custom
vendor/bin/codecept run --group SapEurope
```

### Benefits

1. **Faster Tests** - CI mode uses Create-1.json files for consistent, fast testing
2. **Better Development Experience** - Development mode provides enough generated data for realistic testing
3. **Comprehensive Testing** - Full mode uses all real fixture data
4. **Performance Testing** - Stress mode generates large datasets
5. **Dependency Awareness** - System ensures dependent objects have sufficient parent data
6. **Consistent Dependencies** - In development and stress modes, at least half of dependent objects connect to first parent data for reliable test relationships

Application is accessible on host machine port 8000 (http://localhost:8000)
