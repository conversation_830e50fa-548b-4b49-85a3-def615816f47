#!/bin/bash

# Fail on first error
set -eu

if [[ "${APP_ENV:-}" != *"dev" ]]; then
    echo "The execution of tests is only allowed on APP_ENV=dev (current: ${APP_ENV:-unset})"
    exit 1
fi

# Get current script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
PROJECT_DIR="$(dirname "${SCRIPT_DIR}")"
INPUT_PARAMS="$*"

# Generate openapi definitions for the tests
#echo "Generating openapi definitions for the tests..."
#STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console nelmio:apidoc:dump --area=hermes_app --format=json > "$PROJECT_DIR"/tests/Support/Data/openapi.json

# If the input parameters do not contain '--no-reset-db', then reset the database
#if [[ "$INPUT_PARAMS" != *--no-reset-db* ]]; then
#    echo "Resetting the database..."
#    STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console app:database:load-dump fixtures/dump.sql
#fi

# If the input parameters do not contain '--no-reset-mockserver', then reset the mockserver
#if [[ "$INPUT_PARAMS" != *--no-reset-mockserver* ]]; then
#    echo "Resetting the mockserver..."
#    STDOUT_LOG_LEVEL=alert php "$PROJECT_DIR"/bin/console app:mockserver:reset
#fi

# Remove 'no-reset-db' from the input parameters
#INPUT_PARAMS="${INPUT_PARAMS// --no-reset-db/}"
#INPUT_PARAMS="${INPUT_PARAMS// --no-reset-mockserver/}"

# Load fixtures
echo "Load fixtures..."
STDOUT_LOG_LEVEL=alert php -d error_reporting="E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED" "${PROJECT_DIR}/bin/console" --env=fixdev hautelook:fixtures:load -n

# Run the tests
rm -rf "$PROJECT_DIR"/tests/_output/*

echo "Running tests..."
STDOUT_LOG_LEVEL=alert php -d error_reporting="E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED" "${PROJECT_DIR}/vendor/bin/codecept" run Api $INPUT_PARAMS

echo "Load full test data..."
STDOUT_LOG_LEVEL=alert bash "${PROJECT_DIR}/load-test-data.sh" # todo remove after first test phase!!
