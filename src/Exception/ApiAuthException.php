<?php

declare(strict_types=1);

namespace App\Exception;

use Symfony\Component\HttpFoundation\Response;

class ApiAuthException extends \RuntimeException
{
    public function __construct(
        private readonly string $type,
        private readonly string $title,
        private readonly string $detail,
        string $message,
        int $code = Response::HTTP_UNAUTHORIZED,
    ) {
        parent::__construct($message, $code);
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getDetail(): string
    {
        return $this->detail;
    }
}
