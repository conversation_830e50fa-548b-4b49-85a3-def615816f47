<?php

declare(strict_types=1);

namespace App\Infrastructure\Holiday;

use Ya<PERSON><PERSON>\Holiday;
use Ya<PERSON><PERSON>\Provider\Germany;
use Ya<PERSON><PERSON>\Provider\Germany\BadenWurttemberg;
use Ya<PERSON><PERSON>\Provider\Germany\Bavaria;
use Ya<PERSON><PERSON>\Provider\Germany\Berlin;
use Ya<PERSON><PERSON>\Provider\Germany\Brandenburg;
use Ya<PERSON><PERSON>\Provider\Germany\Bremen;
use Ya<PERSON><PERSON>\Provider\Germany\Hamburg;
use Yasumi\Provider\Germany\Hesse;
use Ya<PERSON>mi\Provider\Germany\LowerSaxony;
use Yasumi\Provider\Germany\MecklenburgWesternPomerania;
use Yasumi\Provider\Germany\NorthRhineWestphalia;
use Yasumi\Provider\Germany\RhinelandPalatinate;
use Yasu<PERSON>\Provider\Germany\Saarland;
use Yasumi\Provider\Germany\Saxony;
use Yasumi\Provider\Germany\SaxonyAnhalt;
use Yasumi\Provider\Germany\SchleswigHolstein;
use Yasumi\Provider\Germany\Thuringia;
use Yasumi\Provider\Netherlands;
use Yasu<PERSON>\Yasumi;

final readonly class PublicHoliday
{
    /**
     * @return array<string, string>
     */
    public static function getPublicHolidaysFor(string $state, int $year): array
    {
        $holidays = [];

        $provider = self::getProvider($state);

        /** @var Holiday $holiday */
        foreach (Yasumi::create(class: $provider, year: $year)->getIterator()->getArrayCopy() as $holiday) {
            if (Holiday::TYPE_OFFICIAL !== $holiday->getType()) {
                continue;
            }

            $holidays[$holiday->format(format: 'Y-m-d')] = $holiday->getKey();
        }

        return $holidays;
    }

    /**
     * @return class-string
     */
    private static function getProvider(string $state): string
    {
        // TODO add other states.
        return match ($state) {
            'Baden-Wurttemberg' => BadenWurttemberg::class,
            'Bavaria' => Bavaria::class,
            'Berlin' => Berlin::class,
            'Brandenburg' => Brandenburg::class,
            'Bremen' => Bremen::class,
            'Hamburg' => Hamburg::class,
            'Hesse' => Hesse::class,
            'Lower-Saxony' => LowerSaxony::class,
            'Mecklenburg-Western-Pomerania' => MecklenburgWesternPomerania::class,
            'North-Rhine-Westphalia' => NorthRhineWestphalia::class,
            'Rhineland-Palatinate' => RhinelandPalatinate::class,
            'Saarland' => Saarland::class,
            'Saxony' => Saxony::class,
            'Saxony-Anhalt' => SaxonyAnhalt::class,
            'Schleswig-Holstein' => SchleswigHolstein::class,
            'Thuringia' => Thuringia::class,
            'North Holland' => Netherlands::class,
            default => Germany::class,
        };
    }
}
