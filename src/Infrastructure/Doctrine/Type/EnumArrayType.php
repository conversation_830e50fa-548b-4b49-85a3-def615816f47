<?php

declare(strict_types=1);

namespace App\Infrastructure\Doctrine\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\StringType;

abstract class EnumArrayType extends StringType
{
    protected const string SEPARATOR = ', ';

    /**
     * @return class-string
     */
    abstract protected function getEnum(): string;

    public function getSQLDeclaration(array $column, AbstractPlatform $platform): string
    {
        $column['length'] = 255;

        return parent::getSQLDeclaration($column, $platform);
    }

    /**
     * @param ?string $value
     *
     * @return array<int, \BackedEnum>
     */
    public function convertToPHPValue($value, AbstractPlatform $platform): array
    {
        if (null === $value) {
            return [];
        }

        $enumClass = $this->getEnum();
        $enumStrings = explode(separator: self::SEPARATOR, string: $value);

        $enum = [];
        foreach ($enumStrings as $enumString) {
            $enum[] = $enumClass::from($enumString);
        }

        /** @var array<int, \BackedEnum> $enum */
        return $enum;
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ([] === $value || !is_array(value: $value)) {
            return null;
        }

        $this->getEnum();
        $enumStrings = [];
        foreach ($value as $enum) {
            if ($enum instanceof \BackedEnum) {
                $enumStrings[] = $enum->value;
            }
        }

        return implode(separator: self::SEPARATOR, array: $enumStrings);
    }
}
