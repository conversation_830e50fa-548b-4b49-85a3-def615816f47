<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue\Order;

use App\Domain\Entity\NoticeText as NoticeTextEntity;
use App\Domain\Entity\Order as OrderEntity;
use App\Domain\Entity\Service as ServiceEntity;
use App\Domain\Repository\NoticeTextRepository;
use App\Domain\Repository\OrderRepository;
use App\Domain\Repository\ServiceRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Framework\Serializer\SerializerInterface;
use App\Infrastructure\SapEurope\Client\SapClientConfig;
use App\Infrastructure\SapEurope\Client\SapPostClient;
use App\Infrastructure\SapEurope\Resource\NoticeText as NoticeTextSap;
use App\Infrastructure\SapEurope\Resource\Service as ServiceSap;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;

#[AsMessageHandler]
readonly class OrderCreatedHandler
{
    public function __construct(
        private SapPostClient $postClient,
        private SapClientConfig $sapClientConfig,
        private ServiceRepository $serviceRepository,
        private SerializerInterface $serializer,
        private LoggerInterface $logger,
        private OrderRepository $orderRepository,
        private NoticeTextRepository $noticeTextRepository,
    ) {
    }

    /**
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function __invoke(OrderCreated $orderCreated): void
    {
        $service = $this->serviceRepository->find(id: $orderCreated->getServiceId());
        if (null === $service) {
            throw new NotFoundException(message: sprintf(
                'Service "%s" not found.',
                $orderCreated->getServiceId()
            ));
        }

        $noticeText = $this->noticeTextRepository->find(id: $orderCreated->getNoticeTextId());
        if (null === $noticeText) {
            throw new NotFoundException(message: sprintf(
                'Notice text "%s" not found.',
                $orderCreated->getNoticeTextId()
            ));
        }

        $order = $this->orderRepository->find(id: $orderCreated->getOrderId());
        if (null === $order) {
            throw new NotFoundException(message: sprintf(
                'Order "%s" not found.',
                $orderCreated->getOrderId()
            ));
        }

        $dto = $this->createDto(service: $service, noticeText: $noticeText);
        $url = $this->sapClientConfig->getServiceUrl();
        $context = [
            'groups' => [ServiceSap::SERVICE_SAP_CREATE],
        ];

        $serviceSap = $this->postClient->requestJsonResponse(url: $url, data: $dto, context: $context);

        try {
            /** @var ServiceSap $serviceSap */
            $serviceSap = $this->serializer->denormalize($serviceSap, ServiceSap::class);

            $dto->id = $serviceSap->id;
            $dto->posId = $serviceSap->posId;
            $dto->extServiceId = $serviceSap->extServiceId;

            $this->matchEntitiesToDto(
                service: $service,
                noticeText: $noticeText,
                order: $order,
                dto: $serviceSap);

            return;
        } catch (\Exception $e) {
            $this->logger->error('Deserialization failed', [
                'exception' => $e,
                'serviceData' => $serviceSap,
            ]);
        }

        throw new \RuntimeException(message: 'Failed to update entities with SAP Data.');
    }

    private function createDto(ServiceEntity $service, NoticeTextEntity $noticeText): ServiceSap
    {
        $dto = new ServiceSap();
        $dto->extServiceId = ''; // TODO
        $dto->startDate = $service->getStartDate()->format(format: 'Y-m-d H:i:s');
        $dto->endDate = $service->getEndDate()->format(format: 'Y-m-d H:i:s');
        $dto->period = $service->getPeriod();
        $dto->contractId = $service->getExtContractId();
        $dto->contractPosId = $service->getExtContractPosId();
        $dto->serviceType = $service->getServiceType();
        $dto->containerMaterialId = $service->getExtContainerMaterialId();
        $dto->wasteMaterialId = $service->getExtWasteMaterialId();
        $dto->serviceLocationId = $service->getExtServiceLocationId();
        $dto->containerCount = $service->getContainerCount();
        $dto->equipment = $service->getEquipment();
        $dto->routeId = $service->getExtRouteId();
        $dto->serviceFrequency = $service->getServiceFrequency();
        $dto->dailyFrequency = $service->getDailyFrequency();

        $dtoText = new NoticeTextSap();
        $dtoText->content = $noticeText->getContent();
        $dtoText->type = $noticeText->getType();
        $dto->noticeTexts[0] = $dtoText;

        return $dto;
    }

    private function matchEntitiesToDto(
        ServiceEntity $service,
        NoticeTextEntity $noticeText,
        OrderEntity $order,
        ServiceSap $dto,
    ): void {
        $service->setExtId(extId: $dto->id);
        $service->setExtPosId(extPosId: (string) $dto->posId);

        $noticeText->setExtId(extId: $dto->id);
        $noticeText->setExtPosId(extPosId: (string) $dto->posId);

        $order->setExtId(extId: $dto->id);
        $order->setExtPosId(extPosId: (string) $dto->posId);

        $this->orderRepository->save(entity: $order);
        $this->serviceRepository->save(entity: $service);
        $this->noticeTextRepository->save(entity: $noticeText);
    }
}
