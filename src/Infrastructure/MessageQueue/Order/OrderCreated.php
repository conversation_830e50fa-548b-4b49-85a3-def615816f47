<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue\Order;

use Symfony\Component\Messenger\Attribute\AsMessage;

#[AsMessage('async')]
readonly class OrderCreated
{
    public function __construct(
        private string $serviceId,
        private string $noticeTextId,
        private string $orderId,
    ) {
    }

    public function getServiceId(): string
    {
        return $this->serviceId;
    }

    public function getNoticeTextId(): string
    {
        return $this->noticeTextId;
    }

    public function getOrderId(): string
    {
        return $this->orderId;
    }
}
