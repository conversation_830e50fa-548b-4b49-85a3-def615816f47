<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue;

use <PERSON><PERSON>\Diagnostics\Check\CheckInterface;
use <PERSON><PERSON>\Diagnostics\Result\Failure;
use <PERSON><PERSON>\Diagnostics\Result\ResultInterface;
use <PERSON><PERSON>\Diagnostics\Result\Success;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Messenger\Bridge\Amqp\Transport\Connection;

readonly class RabbitMQCheck implements CheckInterface
{
    public function __construct(
        #[Autowire('%env(MESSENGER_TRANSPORT_DSN)%')]
        private string $dsn,
        #[Autowire('%env(MESSENGER_TRANSPORT_CACERT)%')]
        private string $caCertPath,
    ) {
    }

    public function check(): ResultInterface
    {
        try {
            $connection = Connection::fromDsn(dsn: $this->dsn, options: ['cacert' => $this->caCertPath]);
            $channel = $connection->channel();
            if ($connection->channel()->isConnected()) {
                $channel->close();
                $channel->getConnection()->disconnect();

                return new Success(message: 'RabbitMQ connection is OK');
            }
        } catch (\Throwable) {
            return new Failure(message: 'RabbitMQ connection is not OK');
        }

        return new Failure(message: 'RabbitMQ connection is not OK');
    }

    public function getLabel(): string
    {
        return 'RabbitMQ Connection';
    }
}
