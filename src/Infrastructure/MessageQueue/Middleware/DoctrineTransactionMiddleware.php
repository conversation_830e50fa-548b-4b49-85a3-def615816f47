<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue\Middleware;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\HandlerFailedException;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Messenger\Middleware\MiddlewareInterface;
use Symfony\Component\Messenger\Middleware\StackInterface;
use Symfony\Component\Messenger\Stamp\HandledStamp;
use Symfony\Component\Messenger\Stamp\ReceivedStamp;

readonly class DoctrineTransactionMiddleware implements MiddlewareInterface
{
    public function __construct(
        private ManagerRegistry $managerRegistry,
        private LoggerInterface $logger,
        private ?string $entityManagerName = null,
    ) {
    }

    /**
     * @throws \Throwable
     */
    final public function handle(Envelope $envelope, StackInterface $stack): Envelope
    {
        // Do not do anything if the message is not consumed by a worker
        if (null === $envelope->last(stampFqcn: ReceivedStamp::class)) {
            return $stack->next()->handle($envelope, $stack);
        }

        try {
            $entityManager = $this->managerRegistry->getManager($this->entityManagerName);
        } catch (\InvalidArgumentException $e) {
            throw new UnrecoverableMessageHandlingException(message: $e->getMessage(), code: 0, previous: $e);
        }

        assert(assertion: $entityManager instanceof EntityManagerInterface);

        return $this->handleForManager(entityManager: $entityManager, envelope: $envelope, stack: $stack);
    }

    /**
     * @throws \Throwable
     */
    protected function handleForManager(
        EntityManagerInterface $entityManager,
        Envelope $envelope,
        StackInterface $stack,
    ): Envelope {
        try {
            $envelope = $stack->next()->handle($envelope, $stack);
            $entityManager->flush();

            return $envelope;
        } catch (\Throwable $exception) {
            $this->logger->error(
                'An error occurred while handling a message. Rolling back the transaction.',
                [
                    'exception' => $exception,
                    'message' => $envelope->getMessage(),
                    'stamps' => $envelope->all(),
                ]
            );

            if ($exception instanceof HandlerFailedException) {
                // Remove all HandledStamp from the envelope so the retry will execute all handlers again.
                // When a handler fails, the queries of allegedly successful previous handlers just got rolled back.
                throw new HandlerFailedException(
                    envelope: $exception->getEnvelope()->withoutAll(stampFqcn: HandledStamp::class),
                    exceptions: $exception->getWrappedExceptions()
                );
            }

            throw $exception;
        }
    }
}
