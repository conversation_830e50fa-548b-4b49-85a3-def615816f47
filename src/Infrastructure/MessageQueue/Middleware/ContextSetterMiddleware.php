<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue\Middleware;

use App\Domain\Context\TenantContext;
use App\Domain\MessageQueue\RequireTenantIdentifier;
use App\Infrastructure\MessageQueue\EnvelopeContextSetterInterface;
use App\Infrastructure\MessageQueue\Stamp\TenantStamp;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Middleware\MiddlewareInterface;
use Symfony\Component\Messenger\Middleware\StackInterface;
use Symfony\Component\Messenger\Stamp\ConsumedByWorkerStamp;
use Symfony\Component\Messenger\Stamp\ReceivedStamp;

readonly class ContextSetterMiddleware implements MiddlewareInterface, EnvelopeContextSetterInterface
{
    public function __construct(
        private LoggerInterface $logger,
        private TenantContext $tenantContext,
    ) {
    }

    public function handle(Envelope $envelope, StackInterface $stack): Envelope
    {
        if ($envelope->last(stampFqcn: ReceivedStamp::class) || $envelope->last(stampFqcn: ConsumedByWorkerStamp::class)) {
            // This only sets the context when message is send. If it's received, we do not need to do anything.
            return $stack->next()->handle($envelope, $stack);
        }

        $envelope = $this->withContext(envelope: $envelope);

        // Go through the rest of the middleware chain (including sending of message)
        $envelope = $stack->next()->handle($envelope, $stack);

        $this->logger->debug(
            'Sending message for async handling',
            [
                'message' => $envelope->getMessage(),
                'stamps' => $envelope->all(),
                'message-class' => $envelope->getMessage()::class,
            ]
        );

        return $envelope;
    }

    public function withContext(Envelope $envelope): Envelope
    {
        $envelope = $this->setTenantStamp(envelope: $envelope);

        return $envelope;
    }

    private function setTenantStamp(Envelope $envelope): Envelope
    {
        if (null !== $envelope->last(stampFqcn: TenantStamp::class)) {
            return $envelope;
        }

        if ($envelope->getMessage() instanceof RequireTenantIdentifier && !$this->tenantContext->hasTenant()) {
            $this->logger->critical(
                'Tenant identifier required but not available',
                [
                    'messageClass' => $envelope->getMessage()::class,
                    'message' => $envelope->getMessage(),
                    'stamps' => $envelope->all(),
                ]
            );
            throw new \RuntimeException(message: 'Tenant identifier required but not available');
        }

        if ($this->tenantContext->hasTenant()) {
            $envelope = $envelope->with(new TenantStamp(tenant: $this->tenantContext->getTenant()));
        }

        return $envelope;
    }
}
