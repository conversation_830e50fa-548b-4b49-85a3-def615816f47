<?php

declare(strict_types=1);

namespace App\Infrastructure\MessageQueue\Middleware;

use App\Domain\Context\TenantContext;
use App\Domain\MessageQueue\RequireTenantIdentifier;
use App\Infrastructure\MessageQueue\Stamp\TenantStamp;
use Psr\Log\LoggerInterface;
use <PERSON>ymfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;
use Symfony\Component\Messenger\Middleware\MiddlewareInterface;
use Symfony\Component\Messenger\Middleware\StackInterface;
use Symfony\Component\Messenger\Stamp\ConsumedByWorkerStamp;
use Symfony\Component\Messenger\Stamp\ReceivedStamp;

readonly class ContextLoaderMiddleware implements MiddlewareInterface
{
    public function __construct(
        private TenantContext $tenantContext,
        private LoggerInterface $logger,
    ) {
    }

    public function handle(Envelope $envelope, StackInterface $stack): Envelope
    {
        if ($envelope->last(stampFqcn: ReceivedStamp::class) || $envelope->last(stampFqcn: ConsumedByWorkerStamp::class)) {
            $this->logger->debug(
                'Received message for async handling',
                [
                    'message' => $envelope->getMessage(),
                    'stamps' => $envelope->all(),
                    'message-class' => $envelope->getMessage()::class,
                ]
            );

            return $this->handleReceivedMessage(envelope: $envelope, stack: $stack);
        }

        // This only loads the context from messages, nothing to be done when messages is sent
        return $stack->next()->handle($envelope, $stack);
    }

    private function handleReceivedMessage(Envelope $envelope, StackInterface $stack): Envelope
    {
        // This happens before the message is handled
        $envelope = $this->beforeMessageHandling(envelope: $envelope);

        // Message is handled here
        return $stack->next()->handle($envelope, $stack);
    }

    private function beforeMessageHandling(Envelope $envelope): Envelope
    {
        $envelope = $this->loadTenant(envelope: $envelope);

        return $envelope;
    }

    private function loadTenant(Envelope $envelope): Envelope
    {
        $tenantStamp = $envelope->last(stampFqcn: TenantStamp::class);

        if ($envelope->getMessage() instanceof RequireTenantIdentifier && null === $tenantStamp) {
            throw new UnrecoverableMessageHandlingException(message: 'Tenant identifier required but not available');
        }

        if (null !== $tenantStamp) {
            $this->tenantContext->setTenant(tenant: $tenantStamp->tenant);
        }

        return $envelope;
    }
}
