<?php

declare(strict_types=1);

namespace App\Infrastructure\XliffParser\Service;

use App\Domain\Entity\Enum\Locale;
use App\Domain\Service\Translation\TranslationSystem;
use App\Infrastructure\XliffParser\Dto\XliffTranslationCollection;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class CsvImportService
{
    public function __construct(
        #[Autowire('%kernel.project_dir%')]
        private string $projectPath,
        private XliffParserService $xliffParserService,
    ) {
    }

    /**
     * @param resource $file
     *
     * @throws \Exception
     */
    public function import($file, TranslationSystem $system): void
    {
        $mapKeyToLanguage = [];
        $translationCollections = [];
        $keyIndex = 0;
        while ($values = fgetcsv($file, escape: '')) {
            /** @var array<int, string> $values */
            if ([] === $mapKeyToLanguage) {
                // First row should be the header.
                $keyIndex = $this->getKeyIndex(header: $values);
                $mapKeyToLanguage = $this->mapKeyToLanguage(header: $values);
                $translationCollections = $this->getTranslationCollections(
                    mapKeyToLanguage: $mapKeyToLanguage,
                    system: $system
                );

                continue;
            }

            $this->addTranslation(translationCollections: $translationCollections, values: $values, keyIndex: $keyIndex);
        }

        $this->updateXliffFiles(translationCollections: $translationCollections, system: $system);
    }

    /**
     * @param array<int, string> $header
     */
    private function getKeyIndex(array $header): int
    {
        foreach ($header as $index => $indexName) {
            if ('key' === strtolower(string: $indexName)) {
                return $index;
            }
        }

        return 0;
    }

    /**
     * @param array<int, string> $header
     *
     * @return array<int, Locale>
     */
    private function mapKeyToLanguage(array $header): array
    {
        $keyToLocaleMap = [];
        foreach ($header as $key => $value) {
            $locale = Locale::tryFrom(value: $value);

            if (null !== $locale) {
                $keyToLocaleMap[$key] = $locale;
            }
        }

        return $keyToLocaleMap;
    }

    /**
     * @param array<int, Locale> $mapKeyToLanguage
     *
     * @return array<int, XliffTranslationCollection>
     */
    private function getTranslationCollections(array $mapKeyToLanguage, TranslationSystem $system): array
    {
        $collections = [];

        foreach ($mapKeyToLanguage as $languageIndex => $value) {
            $collection = new XliffTranslationCollection();
            $collection->locale = $value;
            $collection->translations = $this->xliffParserService->getTranslationList(system: $system, locale: $value);

            $collections[$languageIndex] = $collection;
        }

        return $collections;
    }

    /**
     * @param array<int, XliffTranslationCollection> $translationCollections
     * @param array<int, string>                     $values
     */
    private function addTranslation(array $translationCollections, array $values, int $keyIndex): void
    {
        $translationKey = $values[$keyIndex];
        unset($values[$keyIndex]);

        foreach ($values as $index => $value) {
            $collection = $translationCollections[$index] ?? null;

            if (null === $collection) {
                continue;
            }

            if ('' === $value) {
                $value = $translationKey;
            }

            $collection->addTranslation(
                translationKey: strtolower(string: $translationKey),
                value: $value,
            );
        }
    }

    /**
     * @param array<int, XliffTranslationCollection> $translationCollections
     *
     * @throws \DOMException
     */
    private function updateXliffFiles(array $translationCollections, TranslationSystem $system): void
    {
        foreach ($translationCollections as $translationCollection) {
            $this->updateTranslationFile(xliffTranslationCollection: $translationCollection, system: $system);
        }
    }

    /**
     * @throws \DOMException
     */
    private function updateTranslationFile(
        XliffTranslationCollection $xliffTranslationCollection,
        TranslationSystem $system,
    ): void {
        $xliffFilePath = sprintf(
            '%s/translations/%s.%s.xlf',
            $this->projectPath,
            $system->value,
            $xliffTranslationCollection->locale->value,
        );

        $namespace = 'urn:oasis:names:tc:xliff:document:1.2';
        $dom = new \DOMDocument(version: '1.0', encoding: 'utf-8');
        $dom->formatOutput = true;
        $dom->preserveWhiteSpace = false;

        if (file_exists(filename: $xliffFilePath)) {
            $dom->load(filename: $xliffFilePath);
        } else {
            $this->createNewXliffDocument(dom: $dom, locale: $xliffTranslationCollection->locale->value, namespace: $namespace);
        }

        $xpath = new \DOMXPath(document: $dom);
        $xpath->registerNamespace(prefix: 'xliff', namespace: $namespace);

        $xliffBody = $xpath->query(expression: '//xliff:body');
        if (!$xliffBody instanceof \DOMNodeList) {
            throw new \RuntimeException(message: sprintf('Invalid XLIFF structure in %s', $xliffFilePath));
        }

        $bodyNode = $xliffBody->item(index: 0);
        if (null === $bodyNode) {
            throw new \RuntimeException(message: sprintf('Invalid XLIFF structure in %s', $xliffFilePath));
        }

        $existingTransUnits = $xpath->query(expression: '//xliff:trans-unit', contextNode: $bodyNode);
        if (!$existingTransUnits instanceof \DOMNodeList) {
            throw new \RuntimeException(message: sprintf('Invalid XLIFF structure in %s', $xliffFilePath));
        }

        foreach ($existingTransUnits as $transUnit) {
            $bodyNode->removeChild(child: $transUnit);
        }

        foreach ($xliffTranslationCollection->translations as $translation) {
            $transUnit = $dom->createElementNS(namespace: $namespace, qualifiedName: 'trans-unit');
            $transUnit->setAttribute(qualifiedName: 'id', value: $translation->getKey());

            $source = $dom->createElementNS(namespace: $namespace, qualifiedName: 'source');
            $source->textContent = $translation->getKey();
            $transUnit->appendChild(node: $source);

            $target = $dom->createElementNS(namespace: $namespace, qualifiedName: 'target');
            $target->textContent = $translation->getValue();
            $transUnit->appendChild(node: $target);

            $bodyNode->appendChild(node: $transUnit);
        }

        $dom->save(filename: $xliffFilePath);
    }

    /**
     * @throws \DOMException
     */
    private function createNewXliffDocument(\DOMDocument $dom, string $locale, string $namespace): void
    {
        $xliff = $dom->createElementNS(namespace: $namespace, qualifiedName: 'xliff');
        $xliff->setAttribute(qualifiedName: 'version', value: '1.2');
        $dom->appendChild(node: $xliff);

        $file = $dom->createElementNS(namespace: $namespace, qualifiedName: 'file');
        $file->setAttribute(qualifiedName: 'source-language', value: 'en-GB');
        $file->setAttribute(qualifiedName: 'target-language', value: $locale);
        $file->setAttribute(qualifiedName: 'datatype', value: 'plaintext');
        $file->setAttribute(qualifiedName: 'original', value: 'file.ext');
        $xliff->appendChild(node: $file);

        $header = $dom->createElementNS(namespace: $namespace, qualifiedName: 'header');
        $file->appendChild(node: $header);

        $tool = $dom->createElementNS(namespace: $namespace, qualifiedName: 'tool');
        $tool->setAttribute(qualifiedName: 'tool-id', value: 'symfony');
        $tool->setAttribute(qualifiedName: 'tool-name', value: 'Symfony');
        $header->appendChild(node: $tool);

        $body = $dom->createElementNS(namespace: $namespace, qualifiedName: 'body');
        $file->appendChild(node: $body);
    }
}
