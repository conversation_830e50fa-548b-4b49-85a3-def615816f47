<?php

declare(strict_types=1);

namespace App\Infrastructure\XliffParser\Service;

use App\Domain\Entity\Enum\Locale;
use App\Domain\Service\Translation\TranslationServiceInterface;
use App\Domain\Service\Translation\TranslationSystem;
use App\Exception\NotFoundException;
use App\Infrastructure\Redis\RedisInterface;
use App\Infrastructure\XliffParser\Dto\Xliff;
use App\Infrastructure\XliffParser\Dto\XliffTranslationCollection;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class XliffParserService implements TranslationServiceInterface
{
    private const string REDIS_PREFIX = 'translations-';
    private const int REDIS_EXPIRATION_TIME = 2592000; // 30 days

    public function __construct(
        #[Autowire('%kernel.project_dir%')]
        private string $projectPath,
        private LoggerInterface $logger,
        private RedisInterface $redis,
    ) {
    }

    /**
     * @return array<int, Xliff>
     */
    public function getTranslationList(TranslationSystem $system, Locale $locale): array
    {
        $redisKey = self::REDIS_PREFIX.$system->name.'-'.$locale->value;
        try {
            if ($this->redis->exists($redisKey)) {
                $translationsRaw = $this->redis->getString($redisKey);
                if (null !== $translationsRaw) {
                    /** @var array<array{'key': string, 'value': string}> $translations */
                    $translations = (array) json_decode(json: $translationsRaw, associative: true);
                    $collection = XliffTranslationCollection::fromArray(locale: $locale, translations: $translations);

                    return $collection->translations;
                }
            }
        } catch (\RedisException $e) {
            $this->logger->error('Unable to reach redis for portal translations', ['exception' => $e]);
        }

        $xliffFilePath = sprintf(
            '%s/translations/%s.%s.xlf',
            $this->projectPath,
            $system->value,
            $locale->value,
        );
        if (!file_exists(filename: $xliffFilePath)) {
            throw new NotFoundException();
        }

        $xml = simplexml_load_file(
            filename: $xliffFilePath,
            namespace_or_prefix: 'urn:oasis:names:tc:xliff:document:1.2',
        ) ?: throw new \RuntimeException(message: sprintf('xliff %s could not be read', $xliffFilePath));

        $xliffTranslationCollection = XliffTranslationCollection::fromXliffXmlElement(xmlElement: $xml, locale: $locale);

        try {
            $encodedTranslations = json_encode(value: $xliffTranslationCollection->translations);
            if ($encodedTranslations) {
                $this->redis->setString(
                    $redisKey,
                    $encodedTranslations,
                    self::REDIS_EXPIRATION_TIME,
                );
            }
        } catch (\RedisException $e) {
            $this->logger->error('Unable to store portal translations in redis', ['exception' => $e]);
        }

        return $xliffTranslationCollection->translations;
    }

    public function getTranslationTimestamp(TranslationSystem $system, Locale $locale): \DateTimeImmutable
    {
        $xliffFilePath = sprintf(
            '%s/translations/%s.%s.xlf',
            $this->projectPath,
            $system->value,
            $locale->value,
        );
        $modifiedTimestamp = filemtime(filename: $xliffFilePath);
        if (!$modifiedTimestamp) {
            throw new NotFoundException();
        }

        return new \DateTimeImmutable()->setTimestamp(timestamp: $modifiedTimestamp);
    }

    /**
     * @return array<string, string>
     */
    public function getTranslationDict(TranslationSystem $system, Locale $locale): array
    {
        $translationDict = [];
        $translations = $this->getTranslationList(system: $system, locale: $locale);
        foreach ($translations as $translation) {
            $translationDict[$translation->getKey()] = $translation->getValue();
        }

        return $translationDict;
    }
}
