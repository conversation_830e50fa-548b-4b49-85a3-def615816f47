<?php

declare(strict_types=1);

namespace App\Infrastructure\XliffParser\Dto;

use App\Domain\Service\Translation\TranslationInterface;
use Symfony\Component\Serializer\Attribute\SerializedName;

class Xliff implements TranslationInterface
{
    public function __construct(
        #[SerializedName('key')]
        public readonly string $key,
        #[SerializedName('value')]
        public string $value,
    ) {
    }

    public function getKey(): string
    {
        return $this->key;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function setValue(string $value): void
    {
        $this->value = $value;
    }
}
