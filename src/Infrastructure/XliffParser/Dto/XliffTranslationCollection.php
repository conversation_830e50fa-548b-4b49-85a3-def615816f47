<?php

declare(strict_types=1);

namespace App\Infrastructure\XliffParser\Dto;

use App\Domain\Entity\Enum\Locale;

class XliffTranslationCollection
{
    public Locale $locale;

    /**
     * @var array<int, Xliff>
     */
    public array $translations = [];

    public static function fromXliffXmlElement(\SimpleXMLElement $xmlElement, Locale $locale): self
    {
        $collection = new self();
        $collection->locale = $locale;

        foreach ($xmlElement->file->body->{'trans-unit'} as $xmlTranslation) {
            if (in_array(needle: trim(string: (string) $xmlTranslation->source), haystack: ['', '0'], strict: true)) {
                throw new \RuntimeException(message: 'invalid source/key in xliff');
            }
            $collection->translations[] = new Xliff(
                key: (string) $xmlTranslation->source,
                value: (string) $xmlTranslation->target,
            );
        }

        return $collection;
    }

    /**
     * @param array<array{'key': string, 'value': string}> $translations
     */
    public static function fromArray(Locale $locale, array $translations): self
    {
        $collection = new self();
        $collection->locale = $locale;
        foreach ($translations as $translation) {
            $collection->translations[] = new Xliff(key: $translation['key'], value: $translation['value']);
        }

        return $collection;
    }

    public function addTranslation(string $translationKey, string $value): void
    {
        foreach ($this->translations as $translation) {
            if ($translation->getKey() === $translationKey) {
                $translation->setValue(value: $value);

                return;
            }
        }

        $this->translations[] = new Xliff(key: $translationKey, value: $value);
    }
}
