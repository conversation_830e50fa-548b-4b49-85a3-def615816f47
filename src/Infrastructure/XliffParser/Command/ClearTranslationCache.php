<?php

declare(strict_types=1);

namespace App\Infrastructure\XliffParser\Command;

use App\Domain\Entity\Enum\Locale;
use App\Domain\Service\Translation\TranslationSystem;
use App\Infrastructure\Redis\RedisInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'app:xliff:clear-translation-cache',
    description: 'Remove all cached translations from redis',
)]
class ClearTranslationCache
{
    private const string REDIS_PREFIX = 'translations-';

    public function __construct(private readonly RedisInterface $redis)
    {
    }

    /**
     * @throws \RedisException
     */
    public function __invoke(OutputInterface $output): int
    {
        foreach (Locale::cases() as $locale) {
            foreach (TranslationSystem::cases() as $system) {
                $redisKey = self::REDIS_PREFIX.$system->name.'-'.$locale->value;
                if ($this->redis->exists($redisKey)) {
                    $this->redis->delete($redisKey);
                }
            }
        }

        return Command::SUCCESS;
    }
}
