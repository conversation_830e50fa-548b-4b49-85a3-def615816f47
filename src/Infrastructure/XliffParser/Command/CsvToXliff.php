<?php

declare(strict_types=1);

namespace App\Infrastructure\XliffParser\Command;

use App\Domain\Service\Translation\TranslationSystem;
use App\Infrastructure\XliffParser\Service\CsvImportService;
use Symfony\Component\Console\Attribute\Argument;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:xliff:import-csv',
    description: 'Import translations from CSV file.'
)]
readonly class CsvToXliff
{
    public function __construct(private CsvImportService $csvImportService)
    {
    }

    public function __invoke(
        InputInterface $input,
        OutputInterface $output,
        #[Argument('The path and name to the file that should be imported.')]
        string $filename,
        #[Argument('The translation system.')]
        string $translationSystem = TranslationSystem::PORTAL->value,
    ): int {
        $io = new SymfonyStyle(input: $input, output: $output);
        $io->info(message: 'Start importing translations from CSV file: '.$filename);

        if (!file_exists(filename: $filename)) {
            $io->error(message: 'File not found: '.$filename);

            return Command::INVALID;
        }

        $file = fopen(filename: $filename, mode: 'r');

        if (!$file) {
            $io->error(message: 'Cannot open file: '.$filename);

            return Command::FAILURE;
        }

        try {
            $this->csvImportService->import(file: $file, system: TranslationSystem::from(value: $translationSystem));
        } catch (\Exception $e) {
            $io->error(message: $e->getMessage());

            return Command::FAILURE;
        }

        fclose(stream: $file);

        $io->success(message: 'Successfully imported translations from CSV file.');

        return Command::SUCCESS;
    }
}
