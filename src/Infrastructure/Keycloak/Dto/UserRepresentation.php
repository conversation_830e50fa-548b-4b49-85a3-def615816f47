<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak\Dto;

class UserRepresentation
{
    /**
     * @param array<CredentialRepresentation|ImportCredentialRepresentation> $credentials
     * @param array<string>                                                  $groups
     * @param array<string>|null                                             $requiredActions
     */
    public function __construct(
        public string $firstName = '',
        public string $lastName = '',
        public bool $enabled = true,
        public ?string $username = null,
        public array $credentials = [],
        public array $groups = [],
        public ?string $email = null,
        public ?array $requiredActions = null,
        public ?string $id = null,
    ) {
    }
}
