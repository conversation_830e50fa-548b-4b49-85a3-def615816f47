<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak;

use App\Infrastructure\Keycloak\Dto\GroupMemberRepresentation;
use App\Infrastructure\Keycloak\Dto\GroupRepresentation;

interface KeycloakAdminInterface
{
    public function userExists(string $username): bool;

    public function emailExists(string $email): bool;

    /**
     * @param array<string>      $groups
     * @param array<string>|null $requiredActions
     */
    public function createUser(
        string $username,
        string $firstName,
        string $lastName,
        string $password,
        array $groups,
        ?string $email = null,
        ?array $requiredActions = null,
    ): void;

    /**
     * @param array<string>      $groups
     * @param array<string>|null $requiredActions
     */
    public function importUser(
        string $username,
        string $firstName,
        string $lastName,
        string $hashedPassword,
        array $groups,
        string $email,
        ?array $requiredActions = null,
    ): void;

    public function updateUser(
        string $userId,
        string $userName,
        string $firstName,
        string $lastName,
        ?string $email = null,
    ): void;

    /**
     * @return string[]
     */
    public function getUserGroups(string $userId): array;

    /**
     * @return string[]
     */
    public function getUserRoles(string $userId): array;

    public function deleteUser(string $userId, string $userName): void;

    /**
     * @return GroupMemberRepresentation[]
     */
    public function getGroupMembers(string $groupId): array;

    public function addGroupToUser(string $userId, string $groupId): void;

    public function removeGroupFromUser(string $userId, string $groupId): void;

    public function getUserIdByUsername(string $username): string;

    public function getGroupIdByGroupName(string $groupName): string;

    /**
     * @return GroupRepresentation[]
     */
    public function getGroups(): array;

    public function setPassword(string $userId, string $password): void;
}
