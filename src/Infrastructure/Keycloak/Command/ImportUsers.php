<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak\Command;

use App\Infrastructure\Keycloak\KeycloakAdminInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\Argument;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

#[AsCommand(
    name: 'app:keycloak:import-users-csv',
    description: 'Import users in Keycloak from csv file',
)]
readonly class ImportUsers
{
    public function __construct(
        private KeycloakAdminInterface $keycloakAdmin,
        #[Autowire('%kernel.environment%')]
        private string $kernelEnvironment,
        private EntityManagerInterface $entityManager,
    ) {
    }

    public function __invoke(
        #[Argument(description: 'Path to the CSV file containing user data (email, firstname, lastname, hashed_password)', name: 'csv-file')]
        string $csvFilePath, OutputInterface $output): int
    {
        if ('localdev' !== $this->kernelEnvironment && 'dev' !== $this->kernelEnvironment) {
            $output->writeln('This command can only be run in localdev or dev environment.');

            return Command::FAILURE;
        }

        // Validate CSV file exists
        if (!file_exists(filename: $csvFilePath)) {
            $output->writeln(sprintf('<error>CSV file not found: %s</error>', $csvFilePath));

            return Command::FAILURE;
        }

        if (!is_readable(filename: $csvFilePath)) {
            $output->writeln(sprintf('<error>CSV file is not readable: %s</error>', $csvFilePath));

            return Command::FAILURE;
        }

        // Parse CSV file
        $users = $this->parseCsvFile(csvFilePath: $csvFilePath, output: $output);
        if ([] === $users) {
            $output->writeln('<error>No valid users found in CSV file.</error>');

            return Command::FAILURE;
        }

        $output->writeln(sprintf('<info>Found %d users in CSV file. Starting import...</info>', count(value: $users)));

        $importedCount = 0;
        $skippedCount = 0;

        foreach ($users as $user) {
            if (!$this->keycloakAdmin->userExists($user['email'])) {
                $this->keycloakAdmin->importUser(
                    username: $user['email'],
                    firstName: $user['first_name'],
                    lastName: $user['last_name'],
                    hashedPassword: $user['hashed_password'],
                    groups: [
                        '/tenant-pz',
                    ],
                    email: $user['email'],
                );

                ++$importedCount;
                $output->writeln(sprintf('<info>Imported user: %s</info>', $user['email']));
            } else {
                ++$skippedCount;
                $output->writeln(sprintf('<comment>Skipped existing user: %s</comment>', $user['email']));
            }
        }

        $this->entityManager->flush();

        $output->writeln(sprintf('<info>Import completed. Imported: %d, Skipped: %d</info>', $importedCount, $skippedCount));

        return Command::SUCCESS;
    }

    /**
     * @return array<int, array{email: string, first_name: string, last_name: string, hashed_password: string}>
     */
    private function parseCsvFile(string $csvFilePath, OutputInterface $output): array
    {
        $users = [];
        $lineNumber = 0;

        $handle = fopen(filename: $csvFilePath, mode: 'r');
        if (false === $handle) {
            $output->writeln(sprintf('<error>Could not open CSV file: %s</error>', $csvFilePath));

            return [];
        }

        try {
            while (false !== ($data = fgetcsv($handle, escape: '\\'))) {
                ++$lineNumber;

                // Skip empty lines
                if ([] === $data || 1 === $lineNumber || (1 === count(value: $data) && in_array(needle: trim(string: (string) $data[0]), haystack: ['', '0'], strict: true))) {
                    continue;
                }

                // Validate CSV format (should have exactly 4 columns)
                if (4 !== count(value: $data)) {
                    $output->writeln(sprintf('<error>Invalid CSV format at line %d: Expected 4 columns (email, firstname, lastname, hashed_password), got %d</error>', $lineNumber, count(value: $data)));
                    continue;
                }

                [$email, $firstName, $lastName, $hashedPassword] = $data;

                // Ensure all values are strings and trim them
                $email = trim(string: (string) $email);
                $firstName = trim(string: (string) $firstName);
                $lastName = trim(string: (string) $lastName);
                $hashedPassword = trim(string: (string) $hashedPassword);

                // Validate required fields
                if (in_array(needle: $email, haystack: ['', '0'], strict: true)) {
                    $output->writeln(sprintf('<error>Empty email at line %d, skipping</error>', $lineNumber));
                    continue;
                }

                if (in_array(needle: $hashedPassword, haystack: ['', '0'], strict: true)) {
                    $output->writeln(sprintf('<error>Empty hashed password at line %d, skipping</error>', $lineNumber));
                    continue;
                }

                // Validate email format
                if (!filter_var(value: $email, filter: FILTER_VALIDATE_EMAIL)) {
                    $output->writeln(sprintf('<error>Invalid email format at line %d: %s</error>', $lineNumber, $email));
                    continue;
                }

                // Validate base64 hashed password
                $decoded = base64_decode($hashedPassword, true);
                if (false === $decoded) {
                    $output->writeln(sprintf('<error>Invalid base64 hashed password at line %d</error>', $lineNumber));
                    continue;
                }

                if (strlen(string: $decoded) < 17) { // At least 1 byte for algorithm + 16 bytes for salt + some bytes for password
                    $output->writeln(sprintf('<error>Hashed password too short at line %d</error>', $lineNumber));
                    continue;
                }

                $users[] = [
                    'email' => $email,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'hashed_password' => $hashedPassword,
                ];
            }
        } finally {
            fclose(stream: $handle);
        }

        return $users;
    }
}
