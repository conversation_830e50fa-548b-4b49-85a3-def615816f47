<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak\Command;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Tenant;
use App\Domain\Repository\UserRepository;
use App\Infrastructure\Keycloak\KeycloakAdminInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

#[AsCommand(
    name: 'app:keycloak:create-test-users',
    description: 'Create test users in Keycloak',
)]
class CreateTestUsers
{
    public function __construct(
        private readonly KeycloakAdminInterface $keycloakAdmin,
        private readonly UserRepository $userRepository,
        #[Autowire('%kernel.environment%')]
        private readonly string $kernelEnvironment,
        private readonly TenantContext $tenantContext,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    public function __invoke(OutputInterface $output): int
    {
        if ('localdev' !== $this->kernelEnvironment && 'dev' !== $this->kernelEnvironment) {
            $output->writeln('This command can only be run in localdev or dev environment.');

            return Command::FAILURE;
        }
        foreach (Tenant::LIST as $tenant) {
            $this->tenantContext->setTenant(tenant: $tenant);

            $users = $this->userRepository->findAll();

            foreach ($users as $user) {
                if (!$this->keycloakAdmin->userExists($user->getUserIdentifier())) {
                    // Generate email from username for test users
                    $email = $user->getUserIdentifier().'@test.local';

                    $this->keycloakAdmin->createUser(
                        username: $user->getUserIdentifier(),
                        firstName: 'Test',
                        lastName: 'User',
                        password: 'qwerty',
                        groups: [
                            match ($user->getTenant()) {
                                Tenant::EUROPE => '/tenant-pz',
                                Tenant::NETHERLANDS => '/tenant-nl',
                            },
                        ],
                        email: $email,
                    );
                }
            }

            $this->entityManager->flush();
        }

        return Command::SUCCESS;
    }
}
