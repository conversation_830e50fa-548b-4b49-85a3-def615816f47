<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Attribute\Option;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Process\Process;

#[AsCommand(
    name: 'app:keycloak:dump-database',
    description: 'Dumps the Keycloak database into main directory.',
)]
readonly class DumpKeycloakDatabase
{
    public function __construct(
        #[Autowire('%kernel.project_dir%')]
        private string $projectDir,
    ) {
    }

    public function __invoke(
        #[Option]
        string $name,
        #[Option]
        string $user,
        #[Option]
        string $pass,
        #[Option]
        string $host,
        #[Option]
        string $port,
        OutputInterface $output,
    ): int {
        $process = Process::fromShellCommandline(
            command: sprintf(
                'pg_dump --clean --if-exists -d %s -Z0 -Fp -U %s -h %s -p %s -n keycloak -O -x > %s',
                $name,
                $user,
                $host,
                $port,
                $this->projectDir.'/keycloak.dump.sql'
            ),
            env: [
                'PGPASSWORD' => $pass,
            ]
        );
        $process->run();
        if ($process->isSuccessful()) {
            $output->writeln('Database dumped successfully');

            return Command::SUCCESS;
        }
        $output->writeln('Database dump failed: '.$process->getErrorOutput());

        return Command::FAILURE;
    }
}
