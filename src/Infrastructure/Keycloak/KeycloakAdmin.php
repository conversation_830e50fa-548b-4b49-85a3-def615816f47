<?php

declare(strict_types=1);

namespace App\Infrastructure\Keycloak;

use App\Infrastructure\Framework\Serializer\SerializerException;
use App\Infrastructure\Framework\Serializer\SerializerInterface;
use App\Infrastructure\Keycloak\Dto\AccessTokenResponse;
use App\Infrastructure\Keycloak\Dto\CredentialRepresentation;
use App\Infrastructure\Keycloak\Dto\GroupMemberRepresentation;
use App\Infrastructure\Keycloak\Dto\GroupRepresentation;
use App\Infrastructure\Keycloak\Dto\ImportCredentialRepresentation;
use App\Infrastructure\Keycloak\Dto\RoleRepresentation;
use App\Infrastructure\Keycloak\Dto\UserRepresentation;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface as HttpClientExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class KeycloakAdmin implements KeycloakAdminInterface
{
    private ?string $accessToken = null;
    private ?int $accessTokenExpiresAt = null;

    public function __construct(
        private readonly HttpClientInterface $keycloakClient,

        #[Autowire('%env(KEYCLOAK_CLIENT_ID)%')]
        private readonly string $keycloakClientId,

        #[Autowire('%env(KEYCLOAK_CLIENT_SECRET)%')]
        private readonly string $keycloakClientSecret,

        #[Autowire('%env(KEYCLOAK_REALM)%')]
        private readonly string $keycloakRealm,

        private readonly LoggerInterface $logger,

        private readonly SerializerInterface $serializer,
    ) {
    }

    public function userExists(string $username): bool
    {
        $this->logger->debug('Checking if user exists in Keycloak.', ['username' => $username]);

        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);

        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: '/admin/realms/'.$this->keycloakRealm.'/users',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                    'query' => [
                        'username' => $username,
                        'exact' => 'true',
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to check if user exists in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to check if user exists in Keycloak.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to check if user exists in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to check if user exists in Keycloak.', code: 0, previous: $e);
        }

        try {
            $userList = $this->serializer->deserializeIntoArrayOfObjects(
                data: $responseContent,
                className: UserRepresentation::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak user response.',
                ['exception' => $e, 'response' => $responseContent],
            );

            throw new \RuntimeException(message: 'Failed to deserialize Keycloak user response.', code: 0, previous: $e);
        }

        return array_any(array: $userList, callback: fn ($user): bool => $user->username === $username && null !== $user->id);
    }

    /**
     * @param array<string>|null $requiredActions
     */
    public function createUser(
        string $username,
        string $firstName,
        string $lastName,
        string $password,
        array $groups,
        ?string $email = null,
        ?array $requiredActions = null,
    ): void {
        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);

        $this->logger->debug('Creating user in Keycloak.', ['username' => $username]);

        $userDto = new UserRepresentation(
            firstName: $firstName,
            lastName: $lastName,
            enabled: true,
            username: $username,
            credentials: [
                new CredentialRepresentation(value: $password),
            ],
            groups: $groups,
            email: $email,
            requiredActions: $requiredActions,
        );

        try {
            $requestBody = $this->serializer->serialize(
                $userDto,
                'json',
                [AbstractObjectNormalizer::SKIP_NULL_VALUES => true],
            );
        } catch (SerializerException $e) {
            $this->logger->critical('Failed to serialize user DTO for Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to serialize user DTO for Keycloak.', code: 0, previous: $e);
        }

        try {
            $response = $this->keycloakClient->request(
                method: 'POST',
                url: '/admin/realms/'.$this->keycloakRealm.'/users',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'Content-Type' => 'application/json',
                    ],
                    'body' => $requestBody,
                ],
            );

            if (201 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to create user in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to create user in Keycloak.');
            }

            $this->logger->info('User created in Keycloak.', ['username' => $username]);
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to create user in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to create user in Keycloak.', code: 0, previous: $e);
        }
    }

    /**
     * @param array<string>|null $requiredActions
     */
    public function importUser(
        string $username,
        string $firstName,
        string $lastName,
        string $hashedPassword,
        array $groups,
        string $email,
        ?array $requiredActions = null,
    ): void {
        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);

        $this->logger->debug('Import user in Keycloak.', ['username' => $username]);
        $email = strtolower(string: $email);

        $decoded = base64_decode($hashedPassword);
        $withoutFirstByte = substr(string: $decoded, offset: 1);
        $salt = base64_encode(string: substr(string: $withoutFirstByte, offset: 0, length: 16));
        $password = base64_encode(string: substr(string: $withoutFirstByte, offset: 16));

        $credentialRepresentation = new ImportCredentialRepresentation(
            algorithm: 'pbkdf2',
            hashIterations: 1000,
            salt: $salt,
            hashedSaltedValue: $password,
            temporary: false,
        );

        $userDto = new UserRepresentation(
            firstName: $firstName,
            lastName: $lastName,
            enabled: true,
            username: $username,
            credentials: [
                $credentialRepresentation,
            ],
            groups: $groups,
            email: $email,
            requiredActions: $requiredActions,
        );

        try {
            $requestBody = $this->serializer->serialize(
                $userDto,
                'json',
                [AbstractObjectNormalizer::SKIP_NULL_VALUES => true],
            );
        } catch (SerializerException $e) {
            $this->logger->critical('Failed to serialize user DTO for Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to serialize user DTO for Keycloak.', code: 0, previous: $e);
        }

        try {
            $response = $this->keycloakClient->request(
                method: 'POST',
                url: '/admin/realms/'.$this->keycloakRealm.'/users',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'Content-Type' => 'application/json',
                    ],
                    'body' => $requestBody,
                ],
            );

            if (201 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to create user in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to create user in Keycloak.');
            }

            $this->logger->info('User created in Keycloak.', ['username' => $username]);
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to create user in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to create user in Keycloak.', code: 0, previous: $e);
        }
    }

    public function updateUser(
        string $userId,
        string $userName,
        string $firstName,
        string $lastName,
        ?string $email = null,
    ): void {
        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);

        $this->logger->debug('Updating user in Keycloak.', ['username' => $userName]);

        $userDto = new UserRepresentation(
            firstName: $firstName,
            lastName: $lastName,
            email: $email,
        );

        try {
            $requestBody = $this->serializer->serialize(
                $userDto,
                'json',
                [AbstractObjectNormalizer::SKIP_NULL_VALUES => true],
            );
        } catch (SerializerException $e) {
            $this->logger->critical('Failed to serialize user DTO for Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to serialize user DTO for Keycloak.', code: 0, previous: $e);
        }

        try {
            $response = $this->keycloakClient->request(
                method: 'PUT',
                url: '/admin/realms/'.$this->keycloakRealm.'/users/'.$userId,
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'Content-Type' => 'application/json',
                    ],
                    'body' => $requestBody,
                ],
            );

            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to update user in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to update user in Keycloak.');
            }

            $this->logger->info('User updated in Keycloak.', ['username' => $userName]);
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to update user in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to update user in Keycloak.', code: 0, previous: $e);
        }
    }

    public function deleteUser(string $userId, string $userName): void
    {
        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);

        $this->logger->debug('Deleting user in Keycloak.', ['username' => $userName]);

        try {
            $response = $this->keycloakClient->request(
                method: 'DELETE',
                url: '/admin/realms/'.$this->keycloakRealm.'/users/'.$userId,
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                ],
            );

            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to delete user in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to delete user in Keycloak.');
            }

            $this->logger->info('User deleted in Keycloak.', ['username' => $userName]);
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to delete user in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to delete user in Keycloak.', code: 0, previous: $e);
        }
    }

    public function getUserGroups(string $userId): array
    {
        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);
        $this->logger->debug('Getting user groups by userId in Keycloak.', ['userId' => $userId]);

        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: '/admin/realms/'.$this->keycloakRealm.'/users/'.$userId.'/groups',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get user groups by userId in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to get user groups by userId in Keycloak.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to get user groups by userId in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to get user groups by userId in Keycloak.', code: 0, previous: $e);
        }

        try {
            $groupList = $this->serializer->deserializeIntoArrayOfObjects(
                data: $responseContent,
                className: GroupRepresentation::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak user group response.',
                ['exception' => $e, 'response' => $responseContent],
            );

            throw new \RuntimeException(message: 'Failed to deserialize Keycloak user group response.', code: 0, previous: $e);
        }

        return array_map(
            callback: fn (GroupRepresentation $group): string => $group->name,
            array: $groupList
        );
    }

    public function getUserRoles(string $userId): array
    {
        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);
        $this->logger->debug('Getting user roles by userId in Keycloak.', ['userId' => $userId]);

        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: '/admin/realms/'.$this->keycloakRealm.'/users/'.$userId.'/role-mappings/realm/composite',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get user roles by userId in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to get user roles by userId in Keycloak.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to get user roles by userId in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to get user roles by userId in Keycloak.', code: 0, previous: $e);
        }

        try {
            $roleList = $this->serializer->deserializeIntoArrayOfObjects(
                data: $responseContent,
                className: RoleRepresentation::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak user roles response.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException(message: 'Failed to deserialize Keycloak user roles response.', code: 0, previous: $e);
        }

        return array_map(
            callback: fn (RoleRepresentation $role): string => $role->name,
            array: $roleList
        );
    }

    public function getGroupMembers(string $groupId): array
    {
        $groupMembers = [];
        $first = 0;

        do {
            $members = $this->getPaginatedGroupMembers(groupId: $groupId, first: $first);
            $groupMembers = array_merge($groupMembers, $members);
            $first += 100;
        } while (count(value: $members) > 0);

        return $groupMembers;
    }

    /**
     * @return GroupMemberRepresentation[]
     */
    private function getPaginatedGroupMembers(string $groupId, int $first = 0): array
    {
        try {
            $this->authenticateWithKeycloakAdmin();
            assert(assertion: null !== $this->accessToken);

            $this->logger->debug(
                'Getting members of group in Keycloak.',
                ['groupId' => $groupId, 'first' => $first],
            );

            $response = $this->keycloakClient->request(
                method: 'GET',
                url: '/admin/realms/'.$this->keycloakRealm.'/groups/'.$groupId.'/members',
                options: [
                    'query' => [
                        'briefRepresentation' => 'true',
                        'first' => $first,
                        'max' => 100,
                    ],
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get groupMembers with Keycloak admin.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );
                throw new \RuntimeException(message: 'Failed to get groupMembers from Keycloak.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to get groupMembers with Keycloak admin.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to get group-members  with Keycloak admin.', code: 0, previous: $e);
        }

        try {
            return $this->serializer->deserializeIntoArrayOfObjects(
                data: $responseContent,
                className: GroupMemberRepresentation::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize group-member DTO from Keycloak.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException(message: 'Failed to deserialize group-member DTO from Keycloak.', code: 0, previous: $e);
        }
    }

    public function getGroups(): array
    {
        try {
            $this->authenticateWithKeycloakAdmin();
            assert(assertion: null !== $this->accessToken);

            $this->logger->debug('Getting groups from Keycloak.');

            $response = $this->keycloakClient->request(
                method: 'GET',
                url: '/admin/realms/'.$this->keycloakRealm.'/groups',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get groups from Keycloak admin.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );
                throw new \RuntimeException(message: 'Failed to get groups from Keycloak.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to get groups with Keycloak admin.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to get groups with Keycloak admin.', code: 0, previous: $e);
        }

        try {
            return $this->serializer->deserializeIntoArrayOfObjects(
                data: $responseContent,
                className: GroupRepresentation::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize group DTO from Keycloak.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException(message: 'Failed to deserialize group DTO from Keycloak.', code: 0, previous: $e);
        }
    }

    public function addGroupToUser(string $userId, string $groupId): void
    {
        try {
            $this->authenticateWithKeycloakAdmin();
            assert(assertion: null !== $this->accessToken);

            $this->logger->debug(
                'adding group to user in Keycloak.',
                ['groupId' => $groupId, 'userId' => $userId],
            );

            $response = $this->keycloakClient->request(
                method: 'PUT',
                url: '/admin/realms/'.$this->keycloakRealm.'/users/'.$userId.'/groups/'.$groupId,
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                ],
            );
            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to add group to user with Keycloak admin.',
                    [
                        'user' => $userId,
                        'group' => $groupId,
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );
                throw new \RuntimeException(message: 'Failed to add group to user from Keycloak.');
            }
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to add group to user with Keycloak admin.',
                ['exception' => $e, 'group' => $groupId, 'user' => $userId]
            );

            throw new \RuntimeException(message: 'Failed to add group to user with Keycloak admin.', code: 0, previous: $e);
        }
    }

    public function removeGroupFromUser(string $userId, string $groupId): void
    {
        try {
            $this->authenticateWithKeycloakAdmin();
            assert(assertion: null !== $this->accessToken);

            $this->logger->debug(
                'Removing group from user in Keycloak.',
                ['groupId' => $groupId, 'userId' => $userId],
            );

            $response = $this->keycloakClient->request(
                method: 'DELETE',
                url: '/admin/realms/'.$this->keycloakRealm.'/users/'.$userId.'/groups/'.$groupId,
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                ],
            );
            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to remove group from user with Keycloak admin.',
                    [
                        'user' => $userId,
                        'group' => $groupId,
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );
                throw new \RuntimeException(message: 'Failed to remove group from user from Keycloak.');
            }
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to remove group from user with Keycloak admin.',
                ['exception' => $e, 'group' => $groupId, 'user' => $userId]
            );

            throw new \RuntimeException(message: 'Failed to remove group from user with Keycloak admin.', code: 0, previous: $e);
        }
    }

    public function setPassword(string $userId, string $password): void
    {
        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);

        $this->logger->debug('Setting password for user in Keycloak.', ['userId' => $userId]);

        $credentialRepresentation = new CredentialRepresentation(value: $password);

        try {
            $requestBody = $this->serializer->serialize(
                $credentialRepresentation,
                'json',
                [AbstractObjectNormalizer::SKIP_NULL_VALUES => true],
            );
        } catch (SerializerException $e) {
            $this->logger->critical('Failed to serialize credential DTO for Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to serialize credential DTO for Keycloak.', code: 0, previous: $e);
        }

        try {
            $response = $this->keycloakClient->request(
                method: 'PUT',
                url: '/admin/realms/'.$this->keycloakRealm.'/users/'.$userId.'/reset-password',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                        'Content-Type' => 'application/json',
                    ],
                    'body' => $requestBody,
                ],
            );

            if (204 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to set password for user in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to set password for user in Keycloak.');
            }

            $this->logger->info('Password set for user in Keycloak.', ['userId' => $userId]);
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to set password for user in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to set password for user in Keycloak.', code: 0, previous: $e);
        }
    }

    public function getUserIdByUsername(string $username): string
    {
        $this->logger->debug('Getting user ID by username in Keycloak.', ['username' => $username]);

        $this->authenticateWithKeycloakAdmin();
        assert(assertion: null !== $this->accessToken);

        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: '/admin/realms/'.$this->keycloakRealm.'/users',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                    'query' => [
                        'username' => $username,
                        'exact' => true,
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get user ID by username in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to get user ID by username in Keycloak.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to get user ID by username in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to get user ID by username in Keycloak.', code: 0, previous: $e);
        }

        try {
            $userList = $this->serializer->deserializeIntoArrayOfObjects(
                data: $responseContent,
                className: UserRepresentation::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak user ID response.',
                ['exception' => $e, 'response' => $responseContent],
            );

            throw new \RuntimeException(message: 'Failed to deserialize Keycloak user ID response.', code: 0, previous: $e);
        }

        if (
            [] === $userList
            || !is_string(value: $userList[0]->id)
        ) {
            $this->logger->critical('Keycloak user ID response is missing required fields.', ['response' => $userList]);

            throw new \RuntimeException(message: 'Keycloak user ID response is missing required fields.');
        }

        return $userList[0]->id;
    }

    public function emailExists(string $email): bool
    {
        $this->logger->debug('count keycloak users with email', ['email' => $email]);

        $this->authenticateWithKeycloakAdmin();

        try {
            $response = $this->keycloakClient->request(
                method: 'GET',
                url: '/admin/realms/'.$this->keycloakRealm.'/users',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                    'query' => [
                        'email' => $email,
                        'exact' => true,
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to get user count from email field in Keycloak.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to get user count from email field in Keycloak.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to get user count from email field in Keycloak.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to get user count from email field in Keycloak.', code: 0, previous: $e);
        }

        try {
            $userList = $this->serializer->deserializeIntoArrayOfObjects(
                data: $responseContent,
                className: UserRepresentation::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak user count from email field response.',
                ['exception' => $e, 'response' => $responseContent],
            );

            throw new \RuntimeException(message: 'Failed to deserialize Keycloak user count from email field response.', code: 0, previous: $e);
        }

        return [] !== $userList;
    }

    public function getGroupIdByGroupName(string $groupName): string
    {
        try {
            $this->authenticateWithKeycloakAdmin();
            assert(assertion: null !== $this->accessToken);

            $this->logger->debug(
                'Getting id of group in Keycloak.',
                ['groupName' => $groupName],
            );

            $response = $this->keycloakClient->request(
                method: 'GET',
                url: '/admin/realms/'.$this->keycloakRealm.'/groups',
                options: [
                    'headers' => [
                        'Authorization' => 'Bearer '.$this->accessToken,
                    ],
                    'query' => [
                        'search' => $groupName,
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to authenticate with Keycloak admin.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );
                throw new \RuntimeException(message: 'Failed to get groupId from Keycloak.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to get groupId with Keycloak admin.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to get groupId with Keycloak admin.', code: 0, previous: $e);
        }

        try {
            $groupsList = $this->serializer->deserializeIntoArrayOfObjects(
                data: $responseContent,
                className: GroupRepresentation::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize group DTO from Keycloak.',
                ['exception' => $e, 'response' => $responseContent],
            );

            throw new \RuntimeException(message: 'Failed to deserialize group DTO from Keycloak.', code: 0, previous: $e);
        }

        foreach ($groupsList as $group) {
            if ($group->name === $groupName) {
                return $group->id;
            }
        }

        throw new \RuntimeException(message: 'Keycloak user ID response is missing required fields.');
    }

    private function authenticateWithKeycloakAdmin(): void
    {
        if (
            null !== $this->accessToken
            && null !== $this->accessTokenExpiresAt
            && $this->accessTokenExpiresAt > time() - 10
        ) {
            $this->logger->debug(
                'Using cached Keycloak admin access token.',
                ['expires_at' => $this->accessTokenExpiresAt]
            );

            return;
        }

        try {
            $this->logger->debug('Authenticating with Keycloak admin.');

            $response = $this->keycloakClient->request(
                method: 'POST',
                url: '/realms/'.$this->keycloakRealm.'/protocol/openid-connect/token',
                options: [
                    'body' => [
                        'client_id' => $this->keycloakClientId,
                        'client_secret' => $this->keycloakClientSecret,
                        'grant_type' => 'client_credentials',
                    ],
                ],
            );

            if (200 !== $response->getStatusCode()) {
                $this->logger->critical(
                    'Failed to authenticate with Keycloak admin.',
                    [
                        'status_code' => $response->getStatusCode(),
                        'response' => $response->getContent(throw: false),
                        'headers' => $response->getHeaders(throw: false),
                    ]
                );

                throw new \RuntimeException(message: 'Failed to authenticate with Keycloak admin.');
            }

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $this->logger->critical('Failed to authenticate with Keycloak admin.', ['exception' => $e]);

            throw new \RuntimeException(message: 'Failed to authenticate with Keycloak admin.', code: 0, previous: $e);
        }

        try {
            $accessTokenResponse = $this->serializer->deserializeIntoObject(
                data: $responseContent,
                className: AccessTokenResponse::class,
            );
        } catch (SerializerException $e) {
            $this->logger->critical(
                'Failed to deserialize Keycloak admin authentication response.',
                ['exception' => $e, 'response' => $responseContent]
            );

            throw new \RuntimeException(message: 'Failed to deserialize Keycloak admin authentication response.', code: 0, previous: $e);
        }

        $this->accessToken = $accessTokenResponse->accessToken;
        $this->accessTokenExpiresAt = time() + $accessTokenResponse->expiresIn;
    }
}
