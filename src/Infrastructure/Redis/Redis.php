<?php

declare(strict_types=1);

namespace App\Infrastructure\Redis;

use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class Redis implements RedisInterface
{
    public function __construct(
        #[Autowire('@snc_redis.default')]
        private \Redis|\RedisCluster $redis,
        #[Autowire('%kernel.environment%')]
        private string $applicationEnvironment,
    ) {
    }

    public function setString(string $key, string $value, ?int $expireInSeconds = null): void
    {
        $this->redis->set(key: $this->applicationEnvironment.'-'.$key, value: $value);

        if (null !== $expireInSeconds) {
            $this->redis->expire(key: $this->applicationEnvironment.'-'.$key, timeout: $expireInSeconds);
        }
    }

    public function getString(string $key): ?string
    {
        $value = $this->redis->get(key: $this->applicationEnvironment.'-'.$key);

        if (false === $value) {
            return null;
        }

        if (is_object(value: $value)) {
            throw new \RuntimeException(message: 'Redis value is an object. Are you in multi mode?');
        }

        assert(assertion: is_string(value: $value));

        return $value;
    }

    public function addObjectToSortedSet(string $key, mixed $value, float $score): void
    {
        $this->redis->zAdd(
            $this->applicationEnvironment.'-'.$key,
            $score,
            serialize(value: $value)
        );
    }

    public function setHashValue(string $key, string $index, string $value): void
    {
        $this->redis->hSet(key: $this->applicationEnvironment.'-'.$key, member: $index, value: $value);
    }

    public function getAllHashValues(string $key): array
    {
        /** @var array<string, string> $values */
        $values = $this->redis->hGetAll(key: $this->applicationEnvironment.'-'.$key);

        return $values;
    }

    public function deleteHashKey(string $key, string $index): void
    {
        $this->redis->hDel($this->applicationEnvironment.'-'.$key, $index);
    }

    public function delete(string $key): void
    {
        $this->redis->del(key: $this->applicationEnvironment.'-'.$key);
    }

    public function isConnected(): bool
    {
        if ($this->redis instanceof \RedisCluster) {
            return array_all(
                array: $this->redis->_masters(),
                callback: fn (array $master): bool => $this->redis->ping(key_or_address: [$master[0], $master[1]]) // @phpstan-ignore-line
            );
        }

        $status = $this->redis->ping();

        return false !== $status;
    }

    public function multi(): void
    {
        $this->redis->multi();
    }

    public function exec(): void
    {
        $this->redis->exec();
    }

    public function exists(string $key): bool
    {
        $exists = $this->redis->exists($this->applicationEnvironment.'-'.$key);

        if (is_bool(value: $exists)) {
            return $exists;
        }

        if (is_int(value: $exists)) {
            return 0 < $exists;
        }

        return false;
    }

    public function incr(string $key, int $by = 1): int
    {
        $result = $this->redis->incr(key: $key, by: $by);
        if (is_int(value: $result)) {
            return $result;
        }

        return 0;
    }

    public function addToSet(string $key, string $member): void
    {
        $this->redis->sAdd(key: $this->applicationEnvironment.'-'.$key, value: $member);
    }

    public function removeFromSet(string $key, string $member): void
    {
        $this->redis->sRem(key: $this->applicationEnvironment.'-'.$key, value: $member);
    }

    public function getSetCount(string $key): int
    {
        $count = $this->redis->sCard(key: $this->applicationEnvironment.'-'.$key);

        return is_int(value: $count) ? $count : 0;
    }

    public function refreshExpiration(string $key, int $expireInSeconds): void
    {
        $this->redis->expire(key: $this->applicationEnvironment.'-'.$key, timeout: $expireInSeconds);
    }
}
