<?php

declare(strict_types=1);

namespace App\Infrastructure\Redis;

interface RedisInterface
{
    /**
     * @throws \RedisException
     */
    public function setString(string $key, string $value, ?int $expireInSeconds = null): void;

    /**
     * @throws \RedisException
     */
    public function getString(string $key): ?string;

    /**
     * @throws \RedisException
     */
    public function addObjectToSortedSet(string $key, mixed $value, float $score): void;

    /**
     * @throws \RedisException
     */
    public function delete(string $key): void;

    /**
     * @throws \RedisException
     */
    public function setHashValue(string $key, string $index, string $value): void;

    /**
     * @return array<string, string>
     *
     * @throws \RedisException
     */
    public function getAllHashValues(string $key): array;

    /**
     * @throws \RedisException
     */
    public function deleteHashKey(string $key, string $index): void;

    /**
     * @throws \RedisException
     */
    public function isConnected(): bool;

    /**
     * @throws \RedisException
     */
    public function multi(): void;

    /**
     * @throws \RedisException
     */
    public function exec(): void;

    /**
     * @throws \RedisException
     */
    public function exists(string $key): bool;

    /**
     * @throws \RedisException
     */
    public function incr(string $key, int $by = 1): int;

    /**
     * Add a member to a Redis set.
     *
     * @throws \RedisException
     */
    public function addToSet(string $key, string $member): void;

    /**
     * Remove a member from a Redis set.
     *
     * @throws \RedisException
     */
    public function removeFromSet(string $key, string $member): void;

    /**
     * Get the count of members in a Redis set.
     *
     * @throws \RedisException
     */
    public function getSetCount(string $key): int;

    /**
     * Refresh the expiration time of a Redis key.
     *
     * @throws \RedisException
     */
    public function refreshExpiration(string $key, int $expireInSeconds): void;
}
