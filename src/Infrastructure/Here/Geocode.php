<?php

declare(strict_types=1);

namespace App\Infrastructure\Here;

final class Geocode
{
    public ?string $state = null;

    public static function fromResponse(string $response): self
    {
        $geocode = new self();

        /** @var array<string, array<int|string, array<string, string|array<string, string>>>> $data */
        $data = json_decode(json: $response, associative: true);

        if (isset($data['items'][0]['address']['state'])) {
            $geocode->state = $data['items'][0]['address']['state'];
        }

        return $geocode;
    }
}
