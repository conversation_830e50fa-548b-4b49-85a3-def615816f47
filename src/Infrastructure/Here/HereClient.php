<?php

declare(strict_types=1);

namespace App\Infrastructure\Here;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasAddress;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

final readonly class HereClient
{
    public function __construct(
        private HereClientConfig $config,
        private HttpClientInterface $httpClient,
        private LoggerInterface $logger,
    ) {
    }

    public function getGeocode(HasAddress $entity): string
    {
        if (!$entity instanceof EntityInterface) {
            return '';
        }

        $this->logger->debug(
            message: sprintf('Get geocode for %s with id: %s', $entity::class, $entity->getId()),
        );

        try {
            $response = $this->httpClient->request(
                method: 'GET',
                url: $this->config->getUrl(),
                options: [
                    'query' => [
                        'q' => $entity->getAddress(),
                        'lang' => $this->config->getLanguage(),
                        'apiKey' => $this->config->getApiKey(),
                    ],
                ],
            );

            if ($response->getStatusCode() >= 300) {
                $this->logger->error(
                    message: 'Something went wrong while getting geocode',
                    context: ['exception' => $response->getContent(false)],
                );

                return '';
            }

            return $response->getContent(false);
        } catch (TransportExceptionInterface
            |ClientExceptionInterface
            |ServerExceptionInterface
            |RedirectionExceptionInterface $exception
        ) {
            $this->logger->error(
                message: 'Error while getting geocode for entity with id: '.$entity->getId(),
                context: ['exception' => $exception],
            );
        }

        return '';
    }
}
