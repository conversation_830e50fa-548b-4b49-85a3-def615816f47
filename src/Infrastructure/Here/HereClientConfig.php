<?php

declare(strict_types=1);

namespace App\Infrastructure\Here;

use Symfony\Component\DependencyInjection\Attribute\Autowire;

final readonly class HereClientConfig
{
    public function __construct(
        #[Autowire(env: 'HERE_URL')]
        private string $url,
        #[Autowire(env: 'HERE_API_KEY')]
        private string $apiKey,
        private string $language = 'en-US',
    ) {
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function getApiKey(): string
    {
        return $this->apiKey;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }
}
