<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\VerificationDocument;
use App\Infrastructure\SapEurope\Service\VerificationDocumentService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class VerificationDocumentEndpoint
{
    public function __construct(
        private VerificationDocumentService $verificationDocumentService,
    ) {
    }

    public function create(VerificationDocument $verificationDocument): VerificationDocument
    {
        try {
            $this->verificationDocumentService->upsert(verificationDocument: $verificationDocument);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $verificationDocument;
    }

    public function update(VerificationDocument $verificationDocument): VerificationDocument
    {
        try {
            $this->verificationDocumentService->upsert(verificationDocument: $verificationDocument);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $verificationDocument;
    }
}
