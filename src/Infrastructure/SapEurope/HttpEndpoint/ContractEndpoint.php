<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\Contract;
use App\Infrastructure\SapEurope\Service\ContractService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ContractEndpoint
{
    public function __construct(
        private ContractService $contractService,
    ) {
    }

    public function create(Contract $contract): Contract
    {
        try {
            $this->contractService->upsert(contract: $contract);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $contract;
    }

    public function update(Contract $contract): Contract
    {
        try {
            $this->contractService->upsert(contract: $contract);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $contract;
    }
}
