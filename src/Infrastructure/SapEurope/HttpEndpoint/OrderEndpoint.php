<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\Order;
use App\Infrastructure\SapEurope\Service\OrderService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class OrderEndpoint
{
    public function __construct(
        private OrderService $orderService,
    ) {
    }

    public function create(Order $order): Order
    {
        try {
            $this->orderService->upsert(order: $order);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $order;
    }

    public function update(Order $order): Order
    {
        try {
            $this->orderService->upsert(order: $order);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $order;
    }
}
