<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\Invoice;
use App\Infrastructure\SapEurope\Service\InvoiceService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class InvoiceEndpoint
{
    public function __construct(
        private InvoiceService $invoiceService,
    ) {
    }

    public function create(Invoice $invoice): Invoice
    {
        try {
            $this->invoiceService->upsert(invoice: $invoice);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $invoice;
    }

    public function update(Invoice $invoice): Invoice
    {
        try {
            $this->invoiceService->upsert(invoice: $invoice);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $invoice;
    }
}
