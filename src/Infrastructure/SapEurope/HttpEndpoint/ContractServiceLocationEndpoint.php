<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\ContractServiceLocation;
use App\Infrastructure\SapEurope\Service\ContractServiceLocationService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ContractServiceLocationEndpoint
{
    public function __construct(
        private ContractServiceLocationService $contractServiceLocationService,
    ) {
    }

    public function create(ContractServiceLocation $contractServiceLocation): ContractServiceLocation
    {
        try {
            $this->contractServiceLocationService->upsert(contractServiceLocation: $contractServiceLocation);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $contractServiceLocation;
    }

    public function update(ContractServiceLocation $contractServiceLocation): ContractServiceLocation
    {
        try {
            $this->contractServiceLocationService->upsert(contractServiceLocation: $contractServiceLocation);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $contractServiceLocation;
    }
}
