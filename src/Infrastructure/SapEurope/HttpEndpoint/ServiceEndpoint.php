<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\Service;
use App\Infrastructure\SapEurope\Service\ServiceService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ServiceEndpoint
{
    public function __construct(
        private ServiceService $service,
    ) {
    }

    public function create(Service $service): Service
    {
        try {
            $this->service->upsert(service: $service);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $service;
    }

    public function update(Service $service): Service
    {
        try {
            $this->service->upsert(service: $service);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $service;
    }
}
