<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\NotFoundException;
use App\Infrastructure\SapEurope\Client\SapClientConfig;
use App\Infrastructure\SapEurope\Client\SapGetClient;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/internal')]
#[AsController]
readonly class ArchiveEndpoint
{
    public function __construct(
        #[Autowire('%env(bool:SAP_EUROPE_INTERNAL_API)%')]
        private bool $internalApi,
        private SapGetClient $getClient,
        private SapClientConfig $config,
    ) {
    }

    public function get(string $archiveId, string $documentId): Response
    {
        if (!$this->internalApi) {
            throw new NotFoundException();
        }

        $url = $this->config->getArchiveDocumentUrl(archiveId: $archiveId, documentId: $documentId);

        $fileContent = $this->getClient->request(url: $url);

        return new Response(
            content: $fileContent,
            status: Response::HTTP_OK,
            headers: [
                'Content-Type' => 'application/pdf',
            ],
        );
    }
}
