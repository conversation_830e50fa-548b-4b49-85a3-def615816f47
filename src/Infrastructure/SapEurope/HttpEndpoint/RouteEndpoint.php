<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\Route;
use App\Infrastructure\SapEurope\Service\RouteService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class RouteEndpoint
{
    public function __construct(
        private RouteService $routeService,
    ) {
    }

    public function create(Route $route): Route
    {
        try {
            $this->routeService->upsert(route: $route);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $route;
    }

    public function update(Route $route): Route
    {
        try {
            $this->routeService->upsert(route: $route);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $route;
    }
}
