<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\ServiceLocation;
use App\Infrastructure\SapEurope\Service\ServiceLocationService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ServiceLocationEndpoint
{
    public function __construct(
        private ServiceLocationService $serviceLocationService,
    ) {
    }

    public function create(ServiceLocation $serviceLocation): ServiceLocation
    {
        try {
            $this->serviceLocationService->upsert(serviceLocation: $serviceLocation);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $serviceLocation;
    }

    public function update(ServiceLocation $serviceLocation): ServiceLocation
    {
        try {
            $this->serviceLocationService->upsert(serviceLocation: $serviceLocation);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $serviceLocation;
    }
}
