<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\SupervisionDocument;
use App\Infrastructure\SapEurope\Service\SupervisionDocumentService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class SupervisionDocumentEndpoint
{
    public function __construct(
        private SupervisionDocumentService $supervisionDocumentService,
    ) {
    }

    public function create(SupervisionDocument $supervisionDocument): SupervisionDocument
    {
        try {
            $this->supervisionDocumentService->upsert(supervisionDocument: $supervisionDocument);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $supervisionDocument;
    }

    public function update(SupervisionDocument $supervisionDocument): SupervisionDocument
    {
        try {
            $this->supervisionDocumentService->upsert(supervisionDocument: $supervisionDocument);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $supervisionDocument;
    }
}
