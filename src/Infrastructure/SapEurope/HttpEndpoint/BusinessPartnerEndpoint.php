<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\HttpEndpoint;

use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\BusinessPartner;
use App\Infrastructure\SapEurope\Service\BusinessPartnerService;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class BusinessPartnerEndpoint
{
    public function __construct(
        private BusinessPartnerService $businessPartnerService,
    ) {
    }

    public function create(BusinessPartner $businessPartner): BusinessPartner
    {
        try {
            $this->businessPartnerService->upsert(businessPartner: $businessPartner);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $businessPartner;
    }

    public function update(BusinessPartner $businessPartner): BusinessPartner
    {
        try {
            $this->businessPartnerService->upsert(businessPartner: $businessPartner);
        } catch (\Throwable $exception) {
            throw new BadRequestException(message: $exception->getMessage());
        }

        return $businessPartner;
    }
}
