<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\EventListener;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\Tenant;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;

#[AsEventListener(priority: 9000)]
readonly class LoadTenantContextOnIngestRequest
{
    public function __construct(
        private TenantContext $tenantContext,
    ) {
    }

    public function __invoke(RequestEvent $event): void
    {
        $request = $event->getRequest();
        $requestPath = $request->getPathInfo();

        if (str_starts_with(haystack: $requestPath, needle: '/api/sap-europe/') && $event->isMainRequest()) {
            $this->tenantContext->setTenant(tenant: Tenant::EUROPE);
        }
    }
}
