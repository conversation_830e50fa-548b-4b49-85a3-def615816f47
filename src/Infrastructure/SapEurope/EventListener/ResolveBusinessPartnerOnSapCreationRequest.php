<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\EventListener;

use App\Domain\Event\BankAccountCreatedEvent;
use App\Domain\Event\ContactPersonCreatedEvent;
use App\Domain\Event\IdentificationElementCreatedEvent;
use App\Domain\Event\InvoiceCreatedEvent;
use App\Domain\Event\SalesOrganisationCreatedEvent;
use App\Domain\Event\SupervisionDocumentPartnerCreatedEvent;
use App\Domain\Event\VerificationDocumentPartnerCreatedEvent;
use App\Domain\Repository\BankAccountRepository;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\ContactPersonRepository;
use App\Domain\Repository\IdentificationElementRepository;
use App\Domain\Repository\InvoiceRepository;
use App\Domain\Repository\SalesOrganisationRepository;
use App\Domain\Repository\SupervisionDocumentPartnerRepository;
use App\Domain\Repository\VerificationDocumentPartnerRepository;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;

readonly class ResolveBusinessPartnerOnSapCreationRequest
{
    public function __construct(
        private BusinessPartnerRepository $businessPartnerRepository,
        private InvoiceRepository $invoiceRepository,
        private VerificationDocumentPartnerRepository $verificationDocumentPartnerRepository,
        private SupervisionDocumentPartnerRepository $supervisionDocumentPartnerRepository,
        private BankAccountRepository $bankAccountRepository,
        private SalesOrganisationRepository $salesOrganisationRepository,
        private ContactPersonRepository $contactPersonRepository,
        private IdentificationElementRepository $identificationElementRepository,
    ) {
    }

    #[AsEventListener(event: BankAccountCreatedEvent::NAME)]
    public function onBankAccountCreation(BankAccountCreatedEvent $event): void
    {
        $bankAccount = $event->getBankAccount();
        $this->resolveBusinessPartnerId(entity: $bankAccount);
        $this->bankAccountRepository->save(entity: $bankAccount);
    }

    #[AsEventListener(event: ContactPersonCreatedEvent::NAME)]
    public function onContactPersonCreation(ContactPersonCreatedEvent $event): void
    {
        $contactPerson = $event->getContactPerson();
        $this->resolveBusinessPartnerId(entity: $contactPerson);
        $this->contactPersonRepository->save(entity: $contactPerson);
    }

    #[AsEventListener(event: IdentificationElementCreatedEvent::NAME)]
    public function onIdentificationElementCreation(IdentificationElementCreatedEvent $event): void
    {
        $identificationElement = $event->getIdentificationElement();
        $this->resolveBusinessPartnerId(entity: $identificationElement);
        $this->identificationElementRepository->save(entity: $identificationElement);
    }

    #[AsEventListener(event: InvoiceCreatedEvent::NAME)]
    public function onInvoiceCreation(InvoiceCreatedEvent $event): void
    {
        $invoice = $event->getInvoice();
        $this->resolveBusinessPartnerId(entity: $invoice);
        $this->invoiceRepository->save(entity: $invoice);
    }

    #[AsEventListener(event: VerificationDocumentPartnerCreatedEvent::NAME)]
    public function onVerificationDocumentPartnerCreated(VerificationDocumentPartnerCreatedEvent $event): void
    {
        $verificationDocumentPartner = $event->getVerificationDocumentPartner();
        $this->resolveBusinessPartnerId(entity: $verificationDocumentPartner);
        $this->verificationDocumentPartnerRepository->save(entity: $verificationDocumentPartner);
    }

    #[AsEventListener(event: SalesOrganisationCreatedEvent::NAME)]
    public function onSalesOrganisationCreation(SalesOrganisationCreatedEvent $event): void
    {
        $salesOrganisation = $event->getSalesOrganisation();
        $this->resolveBusinessPartnerId(entity: $salesOrganisation);
        $this->salesOrganisationRepository->save(entity: $salesOrganisation);
    }

    #[AsEventListener(event: SupervisionDocumentPartnerCreatedEvent::NAME)]
    public function onSupervisionDocumentPartnerCreated(SupervisionDocumentPartnerCreatedEvent $event): void
    {
        $supervisionDocumentPartner = $event->getSupervisionDocumentPartner();
        $this->resolveBusinessPartnerId(entity: $supervisionDocumentPartner);
        $this->supervisionDocumentPartnerRepository->save(entity: $supervisionDocumentPartner);
    }

    private function resolveBusinessPartnerId(object $entity): void
    {
        if (
            !method_exists(object_or_class: $entity, method: 'setBusinessPartnerId')
            || !method_exists(object_or_class: $entity, method: 'setExtBusinessPartnerId')
            || !method_exists(object_or_class: $entity, method: 'getExtBusinessPartnerId')
        ) {
            return;
        }

        /** @var string $extBusinessPartnerId */
        $extBusinessPartnerId = $entity->getExtBusinessPartnerId();

        if (null == $extBusinessPartnerId) {
            $entity->setBusinessPartnerId(null);

            return;
        }

        $businessPartner = $this->businessPartnerRepository->findByUniqueConstraints(extId: $extBusinessPartnerId);

        if (null == $businessPartner) {
            $entity->setBusinessPartnerId(null);

            return;
        }

        $entity->setExtBusinessPartnerId($businessPartner->getExtId());
        $entity->setBusinessPartnerId($businessPartner->getId());
    }
}
