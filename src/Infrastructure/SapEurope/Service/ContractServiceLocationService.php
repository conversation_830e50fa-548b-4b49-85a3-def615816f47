<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\ContractServiceLocation as ContractServiceLocationEntity;
use App\Domain\Repository\ContractServiceLocationRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\ContractServiceLocation;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class ContractServiceLocationService
{
    public function __construct(
        private ContractServiceLocationRepository $contractServiceLocationRepository,
        private ValidatorInterface $validator,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(ContractServiceLocation $contractServiceLocation): ContractServiceLocationEntity
    {
        $this->validate(contractServiceLocation: $contractServiceLocation);

        $entity = $this->getEntityByResource(contractServiceLocation: $contractServiceLocation);

        $entity->setExtId(extId: (string) $contractServiceLocation->id);
        $entity->setExtPosId(extPosId: (string) $contractServiceLocation->posId);
        $entity->setValidTo(validTo: DatetimeUtil::convertToDate(
            date: $contractServiceLocation->validTo,
            time: $contractServiceLocation->validToTime
        ));
        $entity->setValidFrom(validFrom: DatetimeUtil::convertToDate(
            date: $contractServiceLocation->validFrom,
            time: $contractServiceLocation->validFromTime
        ));
        $entity->setExtContractId(extContractId: $contractServiceLocation->contractId);
        $entity->setExtContractPosId(extContractPosId: null === $contractServiceLocation->contractPosId ? (string) $contractServiceLocation->contractPosId : null);
        $entity->setEquipment(equipment: $contractServiceLocation->equipment);
        $entity->setSerialNumber(serialNumber: $contractServiceLocation->serialNumber);
        $entity->setExtContainerMaterialId(extContainerMaterialId: $contractServiceLocation->containerMaterialId);
        $entity->setExtServiceLocationId(extServiceLocationId: $contractServiceLocation->serviceLocationId);
        $entity->setExtBusinessPartnerId(extBusinessPartnerId: $contractServiceLocation->businessPartnerId);
        $entity->setTimeto(timeto: DatetimeUtil::convertToDateOrNull(date: $contractServiceLocation->timeto));
        $entity->setTimefr(timefr: DatetimeUtil::convertToDateOrNull(date: $contractServiceLocation->timefr));

        $this->contractServiceLocationRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(ContractServiceLocation $contractServiceLocation): ContractServiceLocationEntity
    {
        return $this->contractServiceLocationRepository->findByUniqueConstraints(
            extId: (string) $contractServiceLocation->id,
            extPosId: (string) $contractServiceLocation->posId,
            validTo: $contractServiceLocation->validTo,
            validToTime: $contractServiceLocation->validToTime,
        ) ?? new ContractServiceLocationEntity(id: $contractServiceLocation->uuid ?? null);
    }

    /**
     * @throws BadRequestException
     */
    private function validate(ContractServiceLocation $contractServiceLocation): void
    {
        $errors = $this->validator->validate($contractServiceLocation);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '. ', array: iterator_to_array(iterator: $errors)));
        }
    }
}
