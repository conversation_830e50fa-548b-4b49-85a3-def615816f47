<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\InvoiceArchiveDocument as InvoiceArchiveDocumentEntity;
use App\Domain\Repository\InvoiceArchiveDocumentRepository;
use App\Infrastructure\SapEurope\Resource\ArchiveDocument;

readonly class InvoiceArchiveDocumentService
{
    public function __construct(
        private InvoiceArchiveDocumentRepository $invoiceArchiveDocumentRepository,
    ) {
    }

    /**
     * @param ArchiveDocument[] $archiveDocuments
     *
     * @return InvoiceArchiveDocumentEntity[]
     */
    public function upsertMany(array $archiveDocuments, string $invoiceId): array
    {
        return array_map(callback: fn (ArchiveDocument $archiveDocument): InvoiceArchiveDocumentEntity => $this->upsert(archiveDocument: $archiveDocument, invoiceId: $invoiceId), array: $archiveDocuments);
    }

    public function upsert(ArchiveDocument $archiveDocument, string $invoiceId): InvoiceArchiveDocumentEntity
    {
        $entity = $this->getEntityByResource(invoiceId: $invoiceId, archiveDocument: $archiveDocument);
        $entity->setInvoiceId(invoiceId: $invoiceId);
        $entity->setExtDocumentId(extDocumentId: $archiveDocument->documentId);

        $this->invoiceArchiveDocumentRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(string $invoiceId, ArchiveDocument $archiveDocument): InvoiceArchiveDocumentEntity
    {
        return $this->invoiceArchiveDocumentRepository->findByUniqueConstraints(
            invoiceId: $invoiceId,
            extDocumentId: $archiveDocument->documentId,
        ) ?? new InvoiceArchiveDocumentEntity();
    }
}
