<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\BusinessPartner as BusinessPartnerEntity;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\BusinessPartner;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class BusinessPartnerService
{
    public function __construct(
        private BankAccountService $bankAccountService,
        private BusinessPartnerRepository $businessPartnerRepository,
        private ContactPersonService $contactPersonService,
        private IdentificationElementService $identificationFeatureService,
        private SalesOrganisationService $salesOrganisationService,
        private ValidatorInterface $validator,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(BusinessPartner $businessPartner): BusinessPartnerEntity
    {
        $this->validate(businessPartner: $businessPartner);

        $entity = $this->getEntityByResource(businessPartner: $businessPartner);

        $entity->setExtId(extId: $businessPartner->id);
        $entity->setType(type: $businessPartner->type);
        $entity->setIndustryCode(industryCode: $businessPartner->industryCode ?? 'TODO');
        $entity->setTitle(title: $businessPartner->title);
        $entity->setName(name: implode(separator: ' ', array: array_filter(array: [$businessPartner->name1, $businessPartner->name2, $businessPartner->name3])));
        $entity->setStreet(street: $businessPartner->street);
        $entity->setHouseNumber(houseNumber: $businessPartner->houseNumber);
        $entity->setPostalCode(postalCode: $businessPartner->postalCode);
        $entity->setCity(city: $businessPartner->city);
        $entity->setDistrict(district: $businessPartner->district ?? '');
        $entity->setCountry(country: $businessPartner->country);
        $entity->setEmail(email: $businessPartner->email);
        $entity->setTelephone(telephone: $businessPartner->telephone);
        $entity->setMobilePhone(mobilePhone: $businessPartner->mobilePhone);
        $entity->setTaxNumber(taxNumber: $businessPartner->taxNumber);
        $entity->setDeleted(deleted: (bool) $businessPartner->deleted);

        $this->contactPersonService->upsertMany(contactPersons: $businessPartner->contactPersons);
        $this->bankAccountService->upsertMany(bankAccounts: $businessPartner->bankAccounts, extBusinessPartnerId: $businessPartner->id);
        $this->salesOrganisationService->upsertMany(salesOrganisations: $businessPartner->salesOrganisations);
        $this->identificationFeatureService->upsertMany(identificationFeatures: $businessPartner->identIds ?? []);

        $this->businessPartnerRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(BusinessPartner $businessPartner): BusinessPartnerEntity
    {
        return $this->businessPartnerRepository->findByUniqueConstraints(
            extId: $businessPartner->id,
        ) ?? new BusinessPartnerEntity(id: $businessPartner->uuid ?? null);
    }

    /**
     * @throws BadRequestException
     */
    private function validate(BusinessPartner $businessPartner): void
    {
        $errors = $this->validator->validate($businessPartner);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '.', array: iterator_to_array(iterator: $errors)));
        }
    }
}
