<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\ServiceLocation as ServiceLocationEntity;
use App\Domain\MessageQueue\ServiceLocation\ServiceLocationCreated;
use App\Domain\Repository\ServiceLocationRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\ServiceLocation;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class ServiceLocationService
{
    public function __construct(
        private LoggerInterface $logger,
        private MessageBusInterface $bus,
        private ServiceLocationRepository $serviceLocationRepository,
        private ValidatorInterface $validator,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(ServiceLocation $serviceLocation): ServiceLocationEntity
    {
        $this->validate(serviceLocation: $serviceLocation);

        $entity = $this->getEntityByResource(serviceLocation: $serviceLocation);

        $entity->setExtId(extId: $serviceLocation->id);
        $entity->setName(name: implode(separator: ' ', array: array_filter(array: [$serviceLocation->name1, $serviceLocation->name2])));
        $entity->setStreet(street: $serviceLocation->street);
        $entity->setHouseNumber(houseNumber: $serviceLocation->houseNumber);
        $entity->setPostalCode(postalCode: $serviceLocation->postalCode);
        $entity->setCity(city: $serviceLocation->city);
        $entity->setDistrict(district: $serviceLocation->district);
        $entity->setCountry(country: $serviceLocation->country);
        if (null !== $serviceLocation->additionalInformation) {
            $entity->setAdditionalInformation(additionalInformation: $serviceLocation->additionalInformation);
        }

        $this->serviceLocationRepository->save(entity: $entity);

        if (null === $entity->getFederalState()) {
            $this->dispatchServiceLocationCreated(entity: $entity);
        }

        return $entity;
    }

    private function getEntityByResource(ServiceLocation $serviceLocation): ServiceLocationEntity
    {
        return $this->serviceLocationRepository->findByUniqueConstraint(
            extId: $serviceLocation->id
        ) ?? new ServiceLocationEntity(id: $serviceLocation->uuid ?? null);
    }

    /**
     * @throws BadRequestException
     */
    private function validate(ServiceLocation $serviceLocation): void
    {
        $errors = $this->validator->validate($serviceLocation);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '. ', array: iterator_to_array(iterator: $errors)));
        }
    }

    private function dispatchServiceLocationCreated(ServiceLocationEntity $entity): void
    {
        try {
            $this->bus->dispatch(new ServiceLocationCreated(id: $entity->getId(), tenant: $entity->getTenant()));
        } catch (ExceptionInterface $exception) {
            $this->logger->error(
                message: 'Something went wrong while trying to get a state for service location',
                context: ['exception' => $exception],
            );
        }
    }
}
