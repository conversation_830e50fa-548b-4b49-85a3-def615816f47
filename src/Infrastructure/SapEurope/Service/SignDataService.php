<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\SignData as SignDataEntity;
use App\Domain\Repository\SignDataRepository;
use App\Infrastructure\SapEurope\Resource\SignData;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;

readonly class SignDataService
{
    public function __construct(
        private SignDataRepository $signDataRepository,
    ) {
    }

    /**
     * @param SignData[] $signData
     *
     * @return SignDataEntity[]
     */
    public function upsertMany(array $signData, ?string $supervisionDocumentId = null, ?string $verificationDocumentId = null): array
    {
        return array_map(callback: fn (SignData $signData): SignDataEntity => $this->upsert(signData: $signData, supervisionDocumentId: $supervisionDocumentId, verificationDocumentId: $verificationDocumentId), array: $signData);
    }

    private function upsert(SignData $signData, ?string $supervisionDocumentId = null, ?string $verificationDocumentId = null): SignDataEntity
    {
        $entity = $this->getEntityByResource(signData: $signData);

        $entity->setExtId(extId: $signData->id);
        $entity->setExtDocId(extDocId: $signData->docId ?? '');
        $entity->setSupervisionDocumentId(supervisionDocumentId: is_null(value: $supervisionDocumentId) ? null : $supervisionDocumentId);
        $entity->setVerificationDocumentId(verificationDocumentId: is_null(value: $verificationDocumentId) ? null : $verificationDocumentId);
        $entity->setSequence(sequence: $signData->sequence);
        $entity->setCount(count: $signData->count);
        $entity->setRole(role: $signData->role);
        $entity->setSignDateTime(signDateTime: DatetimeUtil::convertToDate(date: $signData->signDate, time: $signData->signTime));
        $entity->setSigner(signer: $signData->signer);
        $entity->setStatus(status: $signData->status);
        $entity->setStatusDescription(statusDescription: $signData->statusText);
        $entity->setExtValid(extValid: (bool) $signData->valid);

        $this->signDataRepository->save(entity: $entity);

        return $entity;
    }

    public function getEntityByResource(SignData $signData): SignDataEntity
    {
        return $this->signDataRepository->findByUniqueConstraints(
            sequence: (string) $signData->sequence,
            extId: $signData->id,
            count: (string) $signData->count
        ) ?? new SignDataEntity();
    }
}
