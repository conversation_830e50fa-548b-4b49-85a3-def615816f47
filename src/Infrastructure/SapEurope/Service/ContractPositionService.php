<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\ContractPosition as ContractPositionEntity;
use App\Domain\Repository\ContractPositionRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\ContractPosition;

readonly class ContractPositionService
{
    public function __construct(
        private ContractPartnerService $contractPartnerService,
        private ContractPositionRepository $contractPositionRepository,
    ) {
    }

    /**
     * @param ContractPosition[] $contractPositions
     *
     * @return ContractPositionEntity[]
     */
    public function upsertMany(array $contractPositions, string $contractId): array
    {
        return array_map(
            callback: fn (ContractPosition $contractPosition): ContractPositionEntity => $this->upsert(contractPosition: $contractPosition, contractId: $contractId),
            array: $contractPositions
        );
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(ContractPosition $contractPosition, string $contractId): ContractPositionEntity
    {
        $entity = $this->getEntityByResource(contractPosition: $contractPosition);

        $entity->setExtId(extId: $contractPosition->id);
        $entity->setExtPosId(extPosId: (string) $contractPosition->posId);
        $entity->setExtParentPosId(extParentPosId: (string) $contractPosition->parentPosId);
        $entity->setContractId(contractId: $contractId);
        $entity->setQuantity(quantity: $contractPosition->quantity);
        $entity->setUnitOm(unitOm: $contractPosition->unitOm);
        $entity->setNetValue(netValue: $contractPosition->netValue);
        $entity->setCurrency(currency: $contractPosition->currency);
        $entity->setPriceIndex(priceIndex: $contractPosition->priceIndex);
        $entity->setMaterialType(materialType: $contractPosition->materialType);
        $entity->setExtMaterialId(extMaterialId: $contractPosition->materialId);
        $entity->setMaterialGroup(materialGroup: $contractPosition->materialGroup);
        $entity->setMaterialText(materialText: $contractPosition->materialText);
        $entity->setExtWasteDisposalSiteId(extWasteDisposalSiteId: $contractPosition->wasteDisposalFacilityId);
        $entity->setWasteDisposalSiteDescription(wasteDisposalSiteDescription: $contractPosition->wasteDisposalFacilityText);
        $entity->setRentType(rentType: $contractPosition->rentType);
        $entity->setCertificateNumber(certificateNumber: $contractPosition->certificateNumber);
        $entity->setCertificateType(certificateType: $contractPosition->certificateType);
        $entity->setConfigValue(configValue: $contractPosition->configValue);
        $entity->setDisplay(display: (bool) $contractPosition->display);

        $this->contractPartnerService->upsertMany(contractPartners: $contractPosition->contractPartners ?? [], contractId: $contractId, contractPositionId: $entity->getId());

        $this->contractPositionRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(ContractPosition $contractPosition): ContractPositionEntity
    {
        return $this->contractPositionRepository->findByUniqueConstraint(
            extId: $contractPosition->id,
            extPosId: (string) $contractPosition->posId
        ) ?? new ContractPositionEntity();
    }
}
