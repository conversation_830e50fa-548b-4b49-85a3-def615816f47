<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\SupervisionDocument as SupervisionDocumentEntity;
use App\Domain\Repository\SupervisionDocumentRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\SignData;
use App\Infrastructure\SapEurope\Resource\SupervisionDocument;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class SupervisionDocumentService
{
    public function __construct(
        private SignDataService $signDataService,
        private SupervisionDocumentPartnerService $partnerService,
        private SupervisionDocumentRepository $supervisionDocumentRepository,
        private SupervisionDocumentSignDataService $supervisionDocumentSignDataService,
        private ValidatorInterface $validator,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(SupervisionDocument $supervisionDocument): SupervisionDocumentEntity
    {
        $this->validate(supervisionDocument: $supervisionDocument);

        $entity = $this->getEntityByResource(supervisionDocument: $supervisionDocument);

        $entity->setExtId(extId: $supervisionDocument->id);
        $entity->setExtDocId(extDocId: $supervisionDocument->docId);
        $entity->setExtVerificationDocumentId(extVerificationDocumentId: (string) $supervisionDocument->verificationDocumentId);
        $entity->setExtVerificationDocumentVersion(extVerificationDocumentVersion: (string) $supervisionDocument->verificationDocumentVersion);
        $entity->setCertificateNumber(certificateNumber: $supervisionDocument->certificateNumber);
        $entity->setExtRouteId(extRouteId: $supervisionDocument->routeId);
        $entity->setDescription(description: $supervisionDocument->description);
        $entity->setAvvId(avvId: $supervisionDocument->avvId);
        $entity->setAvvDescription(avvDescription: $supervisionDocument->avvText);
        $entity->setExtWasteMaterialId(extWasteMaterialId: $supervisionDocument->wasteMaterialId);
        $entity->setAmount(amount: $supervisionDocument->amount);
        $entity->setAmountUnitOm(amountUnitOm: $supervisionDocument->amountUnitOm);
        $entity->setExtOrderObjectId(extOrderObjectId: $supervisionDocument->orderObjectId ?? ''); // TODO fix nullable
        $entity->setExtOrderId(extOrderId: $supervisionDocument->orderId);
        $entity->setExtOrderPosId(extOrderPosId: (string) $supervisionDocument->orderPosId);
        $entity->setOrderDate(orderDate: DatetimeUtil::convertToDate(date: $supervisionDocument->orderDate));
        $entity->setExtSalesOrganisationId(extSalesOrganisationId: $supervisionDocument->salesOrganisationId);
        $entity->setCreatedDate(createdDate: DatetimeUtil::convertToDate(date: $supervisionDocument->createdDate));
        $entity->setRole(role: $supervisionDocument->role);
        $entity->setStatus(status: $supervisionDocument->status);
        $entity->setDataStatus(dataStatus: $supervisionDocument->dataStatus);
        $entity->setValue(value: $supervisionDocument->value);
        $entity->setMimeType(mimeType: $supervisionDocument->mimeType);

        $this->partnerService->upsertMany(supervisionDocumentPartners: $supervisionDocument->supervisionDocumentPartner ?? [], supervisionDocumentId: $entity->getId());

        $this->signDataService->upsertMany($supervisionDocument->signData ?? [], supervisionDocumentId: $entity->getId());
        $this->addSignData(signData: $supervisionDocument->signData ?? [], supervisionDocumentId: $entity->getId());

        $this->supervisionDocumentRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(SupervisionDocument $supervisionDocument): SupervisionDocumentEntity
    {
        return $this->supervisionDocumentRepository->findByUniqueConstraints(
            extId: $supervisionDocument->id,
        ) ?? new SupervisionDocumentEntity(id: $supervisionDocument->uuid ?? null);
    }

    /**
     * @throws BadRequestException
     */
    private function validate(SupervisionDocument $supervisionDocument): void
    {
        if (!is_null(value: $supervisionDocument->supervisionDocumentPartner)) {
            foreach ($supervisionDocument->supervisionDocumentPartner as $idx => $supervisionDocumentPartner) {
                $supervisionDocument->supervisionDocumentPartner[$idx]->docId = $supervisionDocumentPartner->docId ?? $supervisionDocument->docId;
            }
        }

        $errors = $this->validator->validate($supervisionDocument);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '. ', array: iterator_to_array(iterator: $errors)));
        }
    }

    /**
     * @param SignData[] $signData
     */
    private function addSignData(array $signData, string $supervisionDocumentId): void
    {
        $this->supervisionDocumentSignDataService->upsertMany(signData: $signData, supervisionDocumentId: $supervisionDocumentId);
    }
}
