<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\BankAccount as BankAccountEntity;
use App\Domain\Event\DomainEvents;
use App\Domain\Repository\BankAccountRepository;
use App\Infrastructure\SapEurope\Resource\BankAccount;

readonly class BankAccountService
{
    public function __construct(
        private BankAccountRepository $bankAccountRepository,
        private DomainEvents $domainEvents,
    ) {
    }

    /**
     * @param BankAccount[] $bankAccounts
     *
     * @return BankAccountEntity[]
     */
    public function upsertMany(array $bankAccounts, string $extBusinessPartnerId): array
    {
        return array_map(callback: fn (BankAccount $bankAccount): BankAccountEntity => $this->upsert(bankAccount: $bankAccount, extBusinessPartnerId: $extBusinessPartnerId), array: $bankAccounts);
    }

    public function upsert(BankAccount $bankAccount, string $extBusinessPartnerId): BankAccountEntity
    {
        $entity = $this->getEntityByResource(bankAccount: $bankAccount, extBusinessPartnerId: $extBusinessPartnerId);

        $entity->setExtId(extId: $bankAccount->id);
        $entity->setExtBusinessPartnerId(extBusinessPartnerId: $extBusinessPartnerId);
        $entity->setAccountHolder(accountHolder: $bankAccount->accountHolder);
        $entity->setIban(iban: $bankAccount->iban ?? '');
        $entity->setBic(bic: $bankAccount->bic ?? '');
        $entity->setBankCountry(bankCountry: $bankAccount->bankCountry);
        $entity->setSepaFlag(sepaFlag: (bool) $bankAccount->sepaFlag);

        $this->domainEvents->dispatchBankAccountCreated(bankAccount: $entity);
        $this->bankAccountRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(BankAccount $bankAccount, string $extBusinessPartnerId): BankAccountEntity
    {
        return $this->bankAccountRepository->findByUniqueConstraints(
            extBusinessPartnerId: $extBusinessPartnerId,
            iban: $bankAccount->iban ?? '',
        ) ?? new BankAccountEntity();
    }
}
