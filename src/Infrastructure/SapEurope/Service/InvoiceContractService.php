<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\Invoice as InvoiceEntity;
use App\Domain\Entity\InvoiceContract as InvoiceContractEntity;
use App\Domain\Repository\InvoiceContractRepository;
use App\Infrastructure\SapEurope\Resource\InvoiceContract;

readonly class InvoiceContractService
{
    public function __construct(
        private InvoiceContractRepository $invoiceContractRepository,
    ) {
    }

    /**
     * @param InvoiceContract[] $invoiceContracts
     *
     * @return InvoiceContractEntity[]
     */
    public function upsertMany(array $invoiceContracts, InvoiceEntity $invoice, string $invoiceId): array
    {
        return array_map(callback: fn (InvoiceContract $invoiceContract): InvoiceContractEntity => $this->upsert(invoiceContract: $invoiceContract, invoice: $invoice, invoiceId: $invoiceId), array: $invoiceContracts);
    }

    public function upsert(InvoiceContract $invoiceContract, InvoiceEntity $invoice, string $invoiceId): InvoiceContractEntity
    {
        $entity = $this->getEntityByResource(invoiceContract: $invoiceContract, invoice: $invoice);

        $entity->setExtInvoiceId(extInvoiceId: $invoice->getExtId());
        $entity->setExtId(extId: $invoiceContract->id);
        $entity->setInvoiceId(invoiceId: $invoiceId);

        $this->invoiceContractRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(InvoiceContract $invoiceContract, InvoiceEntity $invoice): InvoiceContractEntity
    {
        return $this->invoiceContractRepository->findByUniqueContraints(
            extId: $invoiceContract->id,
            extInvoiceId: $invoice->getExtId()
        ) ?? new InvoiceContractEntity();
    }
}
