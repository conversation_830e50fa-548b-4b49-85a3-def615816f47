<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\ContactPerson as ContactPersonEntity;
use App\Domain\Entity\Enum\PartnerRole;
use App\Domain\Event\DomainEvents;
use App\Domain\Repository\ContactPersonRepository;
use App\Infrastructure\SapEurope\Resource\ContactPerson;

readonly class ContactPersonService
{
    public function __construct(
        private ContactPersonRepository $contactPersonRepository,
        private DomainEvents $domainEvents,
    ) {
    }

    /**
     * @param ContactPerson[] $contactPersons
     *
     * @return ContactPersonEntity[]
     */
    public function upsertMany(array $contactPersons): array
    {
        return array_map(callback: fn (ContactPerson $contactPerson): ContactPersonEntity => $this->upsert(contactPerson: $contactPerson), array: $contactPersons);
    }

    public function upsert(ContactPerson $contactPerson): ContactPersonEntity
    {
        $entity = $this->getEntityByResource(contactPerson: $contactPerson);

        $entity->setExtId(extId: (string) $contactPerson->id);
        $entity->setExtBusinessPartnerId(extBusinessPartnerId: $contactPerson->businessPartnerId ?? '');
        $entity->setExtSalesOrganisationId(extSalesOrganisationId: $contactPerson->salesOrganisationId);
        $entity->setFunction(function: $contactPerson->function);
        $entity->setRole(role: PartnerRole::from(value: $contactPerson->role)->value);
        $entity->setTitle(title: $contactPerson->title);
        $entity->setLastName(lastName: $contactPerson->lastName);
        $entity->setFirstName(firstName: $contactPerson->firstName);
        $entity->setCountry(country: $contactPerson->country);
        $entity->setEmail(email: $contactPerson->email);
        $entity->setTelephone(telephone: $contactPerson->telephone);
        $entity->setMobilePhone(mobilePhone: $contactPerson->mobilePhone);

        $this->domainEvents->dispatchContactPersonCreated(contactPerson: $entity);
        $this->contactPersonRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(ContactPerson $contactPerson): ContactPersonEntity
    {
        return $this->contactPersonRepository->findByUniqueConstraint(
            extId: (string) $contactPerson->id,
            extBusinessPartnerId: $contactPerson->businessPartnerId ?? '',
            extSalesOrganisationId: $contactPerson->salesOrganisationId,
            role: $contactPerson->role,
        ) ?? new ContactPersonEntity();
    }
}
