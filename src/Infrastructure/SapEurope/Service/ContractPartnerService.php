<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\ContractPartner as ContractPartnerEntity;
use App\Domain\Entity\Enum\PartnerRole;
use App\Domain\MessageQueue\ContractPartner\ContractPositionCreated;
use App\Domain\Repository\ContractPartnerRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\ContractPartner;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;

readonly class ContractPartnerService
{
    public function __construct(
        private ContractPartnerRepository $contractPartnerRepository,
        private LoggerInterface $logger,
        private MessageBusInterface $messageBus,
    ) {
    }

    /**
     * @param ContractPartner[] $contractPartners
     *
     * @return ContractPartnerEntity[]
     */
    public function upsertMany(array $contractPartners, string $contractId, ?string $contractPositionId = null, ?int $extContractPositionPos = null): array
    {
        return array_map(
            callback: fn (ContractPartner $contractPartner): ContractPartnerEntity => $this->upsert(contractPartner: $contractPartner, contractId: $contractId, contractPositionId: $contractPositionId, extContractPositionPos: $extContractPositionPos),
            array: $contractPartners,
        );
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(
        ContractPartner $contractPartner,
        string $contractId,
        ?string $contractPositionId,
        ?int $extContractPositionPos = null,
    ): ContractPartnerEntity {
        $contractPartner->contractPosId = $extContractPositionPos ?? $contractPartner->contractPosId;
        $entity = $this->getEntityByResource(contractPartner: $contractPartner);

        $entity->setExtId(extId: $contractPartner->id ?? '');
        $entity->setExtContractId(extContractId: $contractPartner->contractId);
        $entity->setExtContractPosId(extContractPosId: is_null(value: $contractPartner->contractPosId) ? null : (string) $contractPartner->contractPosId);
        $entity->setContractId(contractId: $contractId);
        $entity->setContractPosId(contractPosId: is_null(value: $contractPositionId) ? null : $contractPositionId);
        $entity->setRole(role: PartnerRole::from(value: $contractPartner->role)->value);
        $entity->setTitle(title: $contractPartner->title);
        $entity->setName(name: implode(separator: ' ', array: array_filter(array: [$contractPartner->name1, $contractPartner->name2])));
        $entity->setStreet(street: $contractPartner->street);
        $entity->setHouseNumber(houseNumber: $contractPartner->houseNumber);
        $entity->setPostalCode(postalCode: $contractPartner->postalCode);
        $entity->setDistrict(district: $contractPartner->district);
        $entity->setCity(city: $contractPartner->city);
        $entity->setCountry(country: $contractPartner->country);

        $this->contractPartnerRepository->save(entity: $entity);
        $this->dispatch(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(
        ContractPartner $contractPartner,
    ): ContractPartnerEntity {
        $entity = $this->contractPartnerRepository->findByUniqueConstraint(
            extId: $contractPartner->id,
            extContractId: $contractPartner->contractId,
            extContractPosId: is_null(value: $contractPartner->contractPosId) ? null : (string) $contractPartner->contractPosId,
            role: $contractPartner->role,
        );

        return $entity ?? new ContractPartnerEntity();
    }

    private function dispatch(ContractPartnerEntity $entity): void
    {
        // TODO Should we dispatch this event even though the business partner is not null?
        // The business partner for orders resolved here as well.

        try {
            $this->messageBus->dispatch(new ContractPositionCreated(
                extId: $entity->getExtId(),
                extContractId: $entity->getExtContractId(),
                extContractPosId: $entity->getExtContractPosId(),
                role: $entity->getRole(),
                tenant: $entity->getTenant(),
            ));
        } catch (ExceptionInterface $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);
        }
    }
}
