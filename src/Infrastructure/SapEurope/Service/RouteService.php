<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\Route as RouteEntity;
use App\Domain\Repository\RouteRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\Route;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class RouteService
{
    public function __construct(
        private RouteRepository $routeRepository,
        private ServiceFrequencyService $serviceFrequencyService,
        private ValidatorInterface $validator,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(Route $route): RouteEntity
    {
        $this->validate(route: $route);

        $entity = $this->getEntityByResource(route: $route);

        $entity->setExtId(extId: $route->id);
        $entity->setDescription(description: $route->text);
        $entity->setStartDate(startDate: DatetimeUtil::convertToDate(date: $route->startDate));

        $this->serviceFrequencyService->upsertMany(serviceFrequencies: $route->serviceFrequency, extRouteId: $route->id, routeId: $entity->getId());

        $this->routeRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(Route $route): RouteEntity
    {
        return $this->routeRepository->findByUniqueConstraint(
            extId: $route->id
        ) ?? new RouteEntity(id: $route->uuid ?? null);
    }

    /**
     * @throws BadRequestException
     */
    private function validate(Route $route): void
    {
        $errors = $this->validator->validate($route);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '. ', array: iterator_to_array(iterator: $errors)));
        }
    }
}
