<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\ArchiveDocument as ArchiveDocumentEntity;
use App\Domain\Repository\ArchiveDocumentRepository;
use App\Infrastructure\SapEurope\Resource\ArchiveDocument;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;

readonly class ArchiveDocumentService
{
    public function __construct(
        private ArchiveDocumentRepository $archiveDocumentRepository,
    ) {
    }

    /**
     * @param ArchiveDocument[] $archiveDocuments
     *
     * @return ArchiveDocumentEntity[]
     */
    public function upsertMany(array $archiveDocuments, ?string $orderId = null, ?string $invoiceId = null): array
    {
        return array_map(callback: fn (ArchiveDocument $archiveDocument): ArchiveDocumentEntity => $this->upsert(archiveDocument: $archiveDocument, orderId: $orderId, invoiceId: $invoiceId), array: $archiveDocuments);
    }

    private function upsert(ArchiveDocument $archiveDocument, ?string $orderId = null, ?string $invoiceId = null): ArchiveDocumentEntity
    {
        $entity = $this->getEntityByResource(archiveDocument: $archiveDocument);

        $entity->setExtId(extId: $archiveDocument->id);
        $entity->setExtDocumentId(extDocumentId: $archiveDocument->documentId);
        $entity->setOrderId(orderId: is_null(value: $orderId) ? null : $orderId);
        $entity->setInvoiceId(invoiceId: is_null(value: $invoiceId) ? null : $invoiceId);
        $entity->setDocumentType(documentType: $archiveDocument->documentType);
        $entity->setStorageDate(storageDate: DatetimeUtil::convertToDate(date: $archiveDocument->storageDate));
        $entity->setExpiryDate(expiryDate: DatetimeUtil::convertToDateOrNull(date: $archiveDocument->expiryDate));
        $entity->setExtObject(extObject: $archiveDocument->object);
        $entity->setExtObjectId(extObjectId: $archiveDocument->objectId);
        $entity->setValue(value: $archiveDocument->value ?? '');
        $entity->setMimeType(mimeType: $archiveDocument->mimeType);

        $this->archiveDocumentRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(ArchiveDocument $archiveDocument): ArchiveDocumentEntity
    {
        return $this->archiveDocumentRepository->findByUniqueConstraints(
            extId: $archiveDocument->id,
            extObject: $archiveDocument->object,
            extObjectId: $archiveDocument->objectId,
        ) ?? new ArchiveDocumentEntity();
    }
}
