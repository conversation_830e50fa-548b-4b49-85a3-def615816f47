<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\BusinessPartner as BusinessPartnerEntity;
use App\Domain\Entity\WasteStatistic as WasteStatisticEntity;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\WasteStatisticRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\WasteStatistic;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class WasteStatisticService
{
    public function __construct(
        private WasteStatisticRepository $repository,
        private WasteStatisticLocationService $wasteStatisticLocationService,
        private BusinessPartnerRepository $businessPartnerRepository,
        private ValidatorInterface $validator,
    ) {
    }

    /**
     * @param array<WasteStatistic> $wasteStatistics
     *
     * @return array<WasteStatisticEntity>
     *
     * @throws \DateMalformedStringException
     */
    public function upsertMany(array $wasteStatistics, string $businessPartnerId): array
    {
        return array_map(
            callback: fn (WasteStatistic $wasteStatistic): WasteStatisticEntity => $this->upsert(wasteStatistic: $wasteStatistic, businessPartnerId: $businessPartnerId),
            array: $wasteStatistics
        );
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function upsert(WasteStatistic $wasteStatistic, string $businessPartnerId): WasteStatisticEntity
    {
        $entity = $this->getEntityByResource(wasteStatistic: $wasteStatistic);

        /** @var BusinessPartnerEntity|null $businessPartner */
        $businessPartner = $this->businessPartnerRepository->findByUniqueConstraints(extId: $wasteStatistic->businessPartnerId);
        if (null !== $businessPartner) {
            $entity->setExtBusinessPartnerId(extBusinessPartnerId: $businessPartner->getExtId());
        }

        // TODO what should happen if Business Partner doesnt exist in our DB?
        $entity->setExtBusinessPartnerId(extBusinessPartnerId: $wasteStatistic->businessPartnerId);

        $entity->setExtWasteStatisticLocationId(extWasteStatisticLocationId: $wasteStatistic->serviceLocationId ?? null);
        $entity->setExtContractId(extContractId: $wasteStatistic->contractId ?? null);
        $entity->setAvvText(avvText: $wasteStatistic->avvText);
        $entity->setAvvId(avvId: $wasteStatistic->avvId);
        $entity->setOrderDate(orderDate: new \DateTimeImmutable(datetime: $wasteStatistic->orderDate));
        $entity->setMaterialText(materialText: $wasteStatistic->wasteMaterialText);
        $entity->setExtMaterialId(extMaterialId: $wasteStatistic->wasteMaterialId);
        $entity->setAmount(amount: (float) $wasteStatistic->amount);
        $entity->setUnitOm(unitOm: $wasteStatistic->amountUnitOm);
        $entity->setNet(net: (float) $wasteStatistic->netValue);
        $entity->setCurrency(currency: $wasteStatistic->currency);

        $entityLocation = $this->wasteStatisticLocationService->upsert(wasteStatistic: $wasteStatistic);
        $entity->setWasteStatisticLocationId(wasteStatisticLocationId: $entityLocation->getId());

        $this->validate(wasteStatistic: $entity);
        $this->repository->save(entity: $entity);

        return $entity;
    }

    public function getEntityByResource(WasteStatistic $wasteStatistic): WasteStatisticEntity
    {
        return $this->repository->findByUniqueConstraints(
            extBusinessPartnerId: $wasteStatistic->businessPartnerId,
            extWasteStatisticLocationId: $wasteStatistic->serviceLocationId ?? null,
            extContractId: $wasteStatistic->contractId ?? null,
        ) ?? new WasteStatisticEntity();
    }

    /**
     * @throws BadRequestException
     */
    private function validate(WasteStatisticEntity $wasteStatistic): void
    {
        $errors = $this->validator->validate($wasteStatistic);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '.', array: iterator_to_array(iterator: $errors)));
        }
    }
}
