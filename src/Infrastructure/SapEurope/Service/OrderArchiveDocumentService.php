<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\OrderArchiveDocument as OrderArchiveDocumentEntity;
use App\Domain\Repository\OrderArchiveDocumentRepository;
use App\Infrastructure\SapEurope\Resource\ArchiveDocument;

readonly class OrderArchiveDocumentService
{
    public function __construct(
        private OrderArchiveDocumentRepository $orderArchiveDocumentRepository,
    ) {
    }

    /**
     * @param ArchiveDocument[] $archiveDocuments
     *
     * @return OrderArchiveDocumentEntity[]
     */
    public function upsertMany(array $archiveDocuments, string $orderId): array
    {
        return array_map(callback: fn (ArchiveDocument $archiveDocument): OrderArchiveDocumentEntity => $this->upsert(archiveDocument: $archiveDocument, orderId: $orderId), array: $archiveDocuments);
    }

    public function upsert(ArchiveDocument $archiveDocument, string $orderId): OrderArchiveDocumentEntity
    {
        $entity = $this->getEntityByResource(archiveDocument: $archiveDocument, orderId: $orderId);
        $entity->setOrderId(orderId: $orderId);
        $entity->setExtDocumentId(extDocumentId: $archiveDocument->documentId);

        $this->orderArchiveDocumentRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(ArchiveDocument $archiveDocument, string $orderId): OrderArchiveDocumentEntity
    {
        return $this->orderArchiveDocumentRepository->findByUniqueConstraints(
            orderId: $orderId,
            extDocumentId: $archiveDocument->documentId,
        ) ?? new OrderArchiveDocumentEntity();
    }
}
