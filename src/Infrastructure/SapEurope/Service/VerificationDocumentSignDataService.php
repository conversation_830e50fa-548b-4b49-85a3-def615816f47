<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\VerificationDocumentSignData as VerificationDocumentSignDataEntity;
use App\Domain\Repository\VerificationDocumentSignDataRepository;
use App\Infrastructure\SapEurope\Resource\SignData;

readonly class VerificationDocumentSignDataService
{
    public function __construct(
        private VerificationDocumentSignDataRepository $verificationDocumentSignDataRepository,
    ) {
    }

    /**
     * @param SignData[] $signData
     *
     * @return VerificationDocumentSignDataEntity[]
     */
    public function upsertMany(array $signData, string $verificationDocumentId): array
    {
        return array_map(
            callback: fn (SignData $signData): VerificationDocumentSignDataEntity => $this->upsert(signData: $signData, verificationDocumentId: $verificationDocumentId),
            array: $signData,
        );
    }

    private function upsert(SignData $signData, string $verificationDocumentId): VerificationDocumentSignDataEntity
    {
        $entity = $this->getEntityByResource(verificationDocumentId: $verificationDocumentId, signData: $signData);

        $entity->setVerificationDocumentId(verificationDocumentId: $verificationDocumentId);
        $entity->setExtSignDataId(extSignDataId: $signData->id);

        $this->verificationDocumentSignDataRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(string $verificationDocumentId, SignData $signData): VerificationDocumentSignDataEntity
    {
        return $this->verificationDocumentSignDataRepository->findByUniqueConstraints(
            verificationDocumentId: $verificationDocumentId,
            extSignDataId: $signData->id
        ) ?? new VerificationDocumentSignDataEntity();
    }
}
