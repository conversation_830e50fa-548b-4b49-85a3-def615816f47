<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\VerificationDocument as VerificationDocumentEntity;
use App\Domain\Repository\VerificationDocumentRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\SignData;
use App\Infrastructure\SapEurope\Resource\VerificationDocument;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class VerificationDocumentService
{
    public function __construct(
        private SignDataService $signDataService,
        private ValidatorInterface $validator,
        private VerificationDocumentPartnerService $verificationDocumentPartnerService,
        private VerificationDocumentRepository $verificationDocumentRepository,
        private VerificationDocumentSignDataService $verificationDocumentSignDataService,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(VerificationDocument $verificationDocument): VerificationDocumentEntity
    {
        $this->validate(verificationDocument: $verificationDocument);

        $entity = $this->getEntityByResource(verificationDocument: $verificationDocument);

        $confirmationAuthority = $verificationDocument->confirmationAuthority ? DatetimeUtil::convertToDate(date: $verificationDocument->confirmationAuthority) : new \DateTimeImmutable(); // TODO fix nullabel

        $entity->setExtId(extId: (string) $verificationDocument->id);
        $entity->setExtVersion(extVersion: (string) $verificationDocument->version);
        $entity->setCertificateNumber(certificateNumber: $verificationDocument->certificateNumber);
        $entity->setCertificateType(certificateType: $verificationDocument->certificateType);
        $entity->setExtBusinessPartnerId(extBusinessPartnerId: $verificationDocument->businessPartnerId);
        $entity->setExtSalesOrganisationId(extSalesOrganisationId: $verificationDocument->salesOrganisationId);
        $entity->setExtContractId(extContractId: $verificationDocument->contractId);
        $entity->setAvvId(avvId: $verificationDocument->avvId);
        $entity->setAvvDescription(avvDescription: $verificationDocument->avvText);
        $entity->setExtServiceLocationId(extServiceLocationId: $verificationDocument->serviceLocationId);
        $entity->setApprovedAmountDocument(approvedAmountDocument: $verificationDocument->approvedAmountDocument);
        $entity->setApprovedAmountYear(approvedAmountYear: $verificationDocument->approvedAmountYear);
        $entity->setApprovedRemAmountDocument(approvedRemAmountDocument: $verificationDocument->approvedRemAmountDocument);
        $entity->setApprovedRemAmountYear(approvedRemAmountYear: $verificationDocument->approvedRemAmountYear);
        $entity->setAmountUnitOm(amountUnitOm: $verificationDocument->amountUnitOm);
        $entity->setDeletionFlag(deletionFlag: (bool) $verificationDocument->deletionFlag);
        $entity->setApplicationDate(applicationDate: DatetimeUtil::convertToDate(date: $verificationDocument->applicationDate));
        $entity->setDateCustomer(dateCustomer: DatetimeUtil::convertToDate(date: $verificationDocument->dateCustomer));
        $entity->setDateDisposer(dateDisposer: DatetimeUtil::convertToDate(date: $verificationDocument->dateDisposer));
        $entity->setConfirmationAuthority(confirmationAuthority: $confirmationAuthority);
        $entity->setApproval(approval: DatetimeUtil::convertToDate(date: $verificationDocument->approval));
        $entity->setEndDate(endDate: DatetimeUtil::convertToDate(date: $verificationDocument->endDate));
        $entity->setLockedDate(lockedDate: DatetimeUtil::convertToDateOrNull(date: $verificationDocument->lockedDate));
        $entity->setExtObjectId(extObjectId: $verificationDocument->objectId);
        $entity->setDescription(description: $verificationDocument->description);
        $entity->setExtDocId(extDocId: $verificationDocument->docId);
        $entity->setStatus(status: $verificationDocument->status);
        $entity->setStatusDescription(statusDescription: $verificationDocument->statusText);
        $entity->setDataStatus(dataStatus: $verificationDocument->dataStatus);
        $entity->setValue(value: $verificationDocument->value);
        $entity->setMimeType(mimeType: $verificationDocument->mimeType);

        $this->verificationDocumentPartnerService->upsertMany(
            verificationDocumentPartners: $verificationDocument->verificationDocumentPartner ?? [],
            extVerificationDocumentId: $verificationDocument->id,
            verificationDocumentId: $entity->getId(),
            extVerificationDocumentVersion: $verificationDocument->version,
        );

        $this->signDataService->upsertMany($verificationDocument->signData ?? [], verificationDocumentId: $entity->getId());
        $this->addSignData(signData: $verificationDocument->signData ?? [], verificationDocumentId: $entity->getId());

        $this->verificationDocumentRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(
        VerificationDocument $verificationDocument,
    ): VerificationDocumentEntity {
        $entity = $this->verificationDocumentRepository->findByUniqueConstraint(
            extId: (string) $verificationDocument->id,
            extVersion: (string) $verificationDocument->version,
        );

        return $entity ?? new VerificationDocumentEntity(id: $verificationDocument->uuid ?? null);
    }

    /**
     * @throws BadRequestException
     */
    private function validate(VerificationDocument $verificationDocument): void
    {
        if (!is_null(value: $verificationDocument->verificationDocumentPartner)) {
            foreach ($verificationDocument->verificationDocumentPartner as $idx => $verificationDocumentPartner) {
                $verificationDocument->verificationDocumentPartner[$idx]->docId = $verificationDocumentPartner->docId ?? $verificationDocument->docId;
                $verificationDocument->verificationDocumentPartner[$idx]->verificationDocumentId = (string) ($verificationDocumentPartner->verificationDocumentId ?? $verificationDocument->id);
                $verificationDocument->verificationDocumentPartner[$idx]->verificationDocumentVersion = (string) ($verificationDocumentPartner->verificationDocumentVersion ?? $verificationDocument->version);
            }
        }

        $errors = $this->validator->validate($verificationDocument);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '. ', array: iterator_to_array(iterator: $errors)));
        }
    }

    /**
     * @param SignData[] $signData
     */
    private function addSignData(array $signData, string $verificationDocumentId): void
    {
        $this->verificationDocumentSignDataService->upsertMany(
            signData: $signData,
            verificationDocumentId: $verificationDocumentId,
        );
    }
}
