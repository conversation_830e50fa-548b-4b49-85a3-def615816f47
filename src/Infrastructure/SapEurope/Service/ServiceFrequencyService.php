<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\ServiceFrequency as ServiceFrequencyEntity;
use App\Domain\Repository\ServiceFrequencyRepository;
use App\Infrastructure\SapEurope\Resource\ServiceFrequency;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;

readonly class ServiceFrequencyService
{
    public function __construct(
        private ServiceFrequencyRepository $serviceFrequencyRepository,
    ) {
    }

    /**
     * @param ServiceFrequency[] $serviceFrequencies
     *
     * @return ServiceFrequencyEntity[]
     */
    public function upsertMany(array $serviceFrequencies, string $extRouteId, string $routeId): array
    {
        return array_map(
            callback: fn (ServiceFrequency $serviceFrequency): ServiceFrequencyEntity => $this->upsert(serviceFrequency: $serviceFrequency, extRouteId: $extRouteId, routeId: $routeId),
            array: $serviceFrequencies
        );
    }

    private function upsert(ServiceFrequency $serviceFrequency, string $extRouteId, string $routeId): ServiceFrequencyEntity
    {
        $entity = $this->getEntityByResource(serviceFrequency: $serviceFrequency, extRouteId: $extRouteId);

        $entity->setExtId(extId: $extRouteId);
        $entity->setExtPosId(extPosId: (string) $serviceFrequency->posId);
        $entity->setRouteId(routeId: $routeId);
        $entity->setStartDate(startDate: DatetimeUtil::convertToDate(date: $serviceFrequency->startDate));
        $entity->setEndDate(endDate: DatetimeUtil::convertToDate(date: $serviceFrequency->endDate));
        $entity->setServiceType(serviceType: $serviceFrequency->serviceType);
        $entity->setServiceFrequency(serviceFrequency: $serviceFrequency->serviceFrequency);
        $entity->setDailyFrequency(dailyFrequency: $serviceFrequency->dailyFrequency);
        $entity->setweeklyFrequency(weeklyFrequency: $serviceFrequency->weeklyFrequency);
        $entity->setmonthlyFrequency(monthlyFrequency: $serviceFrequency->monthlyFrequency);
        $entity->setWeekday(weekday: $serviceFrequency->weekday ?? '');

        $this->serviceFrequencyRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(ServiceFrequency $serviceFrequency, string $extRouteId): ServiceFrequencyEntity
    {
        return $this->serviceFrequencyRepository->findByUniqueConstraints(
            extId: $extRouteId,
            extPosId: (string) $serviceFrequency->posId
        ) ?? new ServiceFrequencyEntity();
    }
}
