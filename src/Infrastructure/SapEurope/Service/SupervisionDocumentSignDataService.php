<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\SupervisionDocumentSignData as SupervisionDocumentSignDataEntity;
use App\Domain\Repository\SupervisionDocumentSignDataRepository;
use App\Infrastructure\SapEurope\Resource\SignData;

readonly class SupervisionDocumentSignDataService
{
    public function __construct(
        private SupervisionDocumentSignDataRepository $supervisionDocumentSignDataRepository,
    ) {
    }

    /**
     * @param SignData[] $signData
     *
     * @return SupervisionDocumentSignDataEntity[]
     */
    public function upsertMany(array $signData, string $supervisionDocumentId): array
    {
        return array_map(
            callback: fn (SignData $signData): SupervisionDocumentSignDataEntity => $this->upsert(signData: $signData, supervisionDocumentId: $supervisionDocumentId),
            array: $signData,
        );
    }

    private function upsert(SignData $signData, string $supervisionDocumentId): SupervisionDocumentSignDataEntity
    {
        $entity = $this->getEntityByResource(supervisionDocumentId: $supervisionDocumentId, signData: $signData);

        $entity->setSupervisionDocumentId(supervisionDocumentId: $supervisionDocumentId);
        $entity->setExtSignDataId(extSignDataId: $signData->id);

        $this->supervisionDocumentSignDataRepository->save(entity: $entity, extSignDataId: $signData->id);

        return $entity;
    }

    private function getEntityByResource(string $supervisionDocumentId, SignData $signData): SupervisionDocumentSignDataEntity
    {
        return $this->supervisionDocumentSignDataRepository->findByUniqueConstraints(
            supervisionDocumentId: $supervisionDocumentId,
            extSignDataId: $signData->id
        ) ?? new SupervisionDocumentSignDataEntity();
    }
}
