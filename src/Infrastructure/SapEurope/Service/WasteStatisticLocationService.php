<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\WasteStatisticLocation as WasteStatisticLocationEntity;
use App\Domain\Repository\WasteStatisticLocationRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\WasteStatistic;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class WasteStatisticLocationService
{
    public function __construct(
        private WasteStatisticLocationRepository $repository,
        private ValidatorInterface $validator,
    ) {
    }

    public function upsert(WasteStatistic $wasteStatistic): WasteStatisticLocationEntity
    {
        $entity = $this->getEntityByResource(wasteStatistic: $wasteStatistic);

        $entity->setExtId(extId: $wasteStatistic->serviceLocationId ?? null);
        $entity->setAddress(address: $wasteStatistic->serviceLocationAddress ?? null);
        $entity->setInfo(info: $wasteStatistic->serviceLocationInfo ?? null);
        $entity->setName(name: $entity->getAddress() ? explode(separator: '/', string: $entity->getAddress(), limit: 2)[0] : null);

        $this->validate(wasteStatisticLocation: $entity);
        $this->repository->save(entity: $entity);

        return $entity;
    }

    public function getEntityByResource(WasteStatistic $wasteStatistic): WasteStatisticLocationEntity
    {
        return $this->repository->findByUniqueConstraints(
            extId: $wasteStatistic->serviceLocationId ?? '',
        ) ?? new WasteStatisticLocationEntity();
    }

    /**
     * @throws BadRequestException
     */
    private function validate(WasteStatisticLocationEntity $wasteStatisticLocation): void
    {
        $errors = $this->validator->validate($wasteStatisticLocation);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '.', array: iterator_to_array(iterator: $errors)));
        }
    }
}
