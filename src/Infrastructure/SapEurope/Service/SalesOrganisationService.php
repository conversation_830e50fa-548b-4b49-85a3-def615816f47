<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\SalesOrganisation as SalesOrganisationEntity;
use App\Domain\Event\DomainEvents;
use App\Domain\Repository\SalesOrganisationRepository;
use App\Infrastructure\SapEurope\Resource\SalesOrganisation;

readonly class SalesOrganisationService
{
    public function __construct(
        private SalesOrganisationRepository $salesOrganisationRepository,
        private DomainEvents $domainEvents,
    ) {
    }

    /**
     * @param SalesOrganisation[] $salesOrganisations
     *
     * @return SalesOrganisationEntity[]
     */
    public function upsertMany(array $salesOrganisations): array
    {
        return array_map(callback: fn (SalesOrganisation $salesOrganisation): SalesOrganisationEntity => $this->upsert(salesOrganisation: $salesOrganisation), array: $salesOrganisations);
    }

    private function upsert(SalesOrganisation $salesOrganisation): SalesOrganisationEntity
    {
        $entity = $this->getEntityByResource(salesOrganisation: $salesOrganisation);

        $entity->setExtId(extId: $salesOrganisation->id);
        $entity->setExtBusinessPartnerId(extBusinessPartnerId: $salesOrganisation->businessPartnerId);

        $this->domainEvents->dispatchSalesOrganisationCreated(salesOrganisation: $entity);
        $this->salesOrganisationRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(SalesOrganisation $salesOrganisation): SalesOrganisationEntity
    {
        return $this->salesOrganisationRepository->findByUniqueConstraint(
            extId: $salesOrganisation->id,
            extBusinessPartnerId: $salesOrganisation->businessPartnerId
        ) ?? new SalesOrganisationEntity();
    }
}
