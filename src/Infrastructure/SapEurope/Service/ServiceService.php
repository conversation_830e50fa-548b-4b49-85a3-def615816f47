<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\Service as ServiceEntity;
use App\Domain\Repository\ServiceRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\Service;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Symfony\Component\Validator\Validator\ValidatorInterface;

// TODO: Find better name
readonly class ServiceService
{
    public function __construct(
        private ServiceRepository $serviceRepository,
        private ValidatorInterface $validator,
        private NoticeTextService $noticeTextService,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(Service $service): ServiceEntity
    {
        $this->validate(service: $service);

        $entity = $this->getEntityByResource(service: $service);

        $entity->setExtId(extId: $service->id);
        $entity->setExtPosId(extPosId: (string) $service->posId);
        $entity->setStartDate(startDate: DatetimeUtil::convertToDate(date: $service->startDate));
        $entity->setEndDate(endDate: DatetimeUtil::convertToDate(date: $service->endDate));
        $entity->setPeriod(period: $service->period);
        $entity->setExtContractId(extContractId: $service->contractId);
        $entity->setExtContractPosId(extContractPosId: (string) $service->contractPosId);
        $entity->setServiceType(serviceType: $service->serviceType);
        $entity->setExtContainerMaterialId(extContainerMaterialId: $service->containerMaterialId);
        $entity->setExtWasteMaterialId(extWasteMaterialId: $service->wasteMaterialId ?? '');
        $entity->setExtServiceLocationId(extServiceLocationId: $service->serviceLocationId);
        $entity->setContainerCount(containerCount: $service->containerCount ?? 0);
        $entity->setEquipment(equipment: $service->equipment);
        $entity->setExtRouteId(extRouteId: (string) $service->routeId);
        $entity->setServiceFrequency(serviceFrequency: $service->serviceFrequency);
        $entity->setDailyFrequency(dailyFrequency: $service->dailyFrequency ?? 'D'); // TODO nullable
        $entity->setWeeklyFrequency(weeklyFrequency: $service->weeklyFrequency);
        $entity->setMonthlyFrequency(monthlyFrequency: $service->monthlyFrequency);
        $entity->setMonthlyFrequencyDay(monthlyFrequencyDay: $service->monthlyFrequencyDay);
        $entity->setWeekday(weekday: $service->weekday);
        $entity->setLink(link: $service->link);
        $entity->setMimeType(mimeType: $service->mimeType);
        $entity->setTimeto(timeto: DatetimeUtil::convertToDateOrNull(date: $service->timeto));
        $entity->setTimefr(timefr: DatetimeUtil::convertToDateOrNull(date: $service->timfr));

        if (null !== $service->noticeTexts) {
            $this->noticeTextService->upsertMany(noticeTexts: $service->noticeTexts, serviceId: $entity->getId());
        }

        $this->serviceRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(Service $service): ServiceEntity
    {
        return $this->serviceRepository->findByUniqueConstraints(
            extId: $service->id,
            extPosId: (string) $service->posId,
            endDate: $service->endDate
        ) ?? new ServiceEntity(id: $service->uuid ?? null);
    }

    /**
     * @throws BadRequestException
     */
    private function validate(Service $service): void
    {
        $errors = $this->validator->validate($service);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '. ', array: iterator_to_array(iterator: $errors)));
        }
    }
}
