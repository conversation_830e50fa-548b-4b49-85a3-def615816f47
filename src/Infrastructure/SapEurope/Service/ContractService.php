<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\Contract as ContractEntity;
use App\Domain\Repository\ContractRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\Contract;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class ContractService
{
    public function __construct(
        private ContractPartnerService $contractPartnerService,
        private ContractPositionService $contractPositionService,
        private ContractRepository $contractRepository,
        private ValidatorInterface $validator,
    ) {
    }

    /**
     * @throws BadRequestException
     */
    public function upsert(Contract $contract): ContractEntity
    {
        $this->validate(contract: $contract);

        $entity = $this->getEntityByResource(contract: $contract);

        $entity->setExtId(extId: $contract->id);
        $entity->setExtSalesOrganisationId(extSalesOrganisationId: $contract->salesOrganisationId);
        $entity->setExtRouteId(extRouteId: $contract->routeId ?? '');
        $entity->setShippingPoint(shippingPoint: $contract->shippingPoint);
        $entity->setContractStartDate(contractStartDate: DatetimeUtil::convertToDate(date: $contract->contractStartDate));
        $entity->setContractEndDate(contractEndDate: DatetimeUtil::convertToDate(date: $contract->contractEndDate));
        $entity->setContractType(contractType: $contract->contractType);
        $entity->setDescription(description: $contract->description);
        $entity->setExtServiceLocationId(extServiceLocationId: $contract->serviceLocationId);
        $entity->setPaymentTerm(paymentTerm: $contract->paymentTerm);
        $entity->setPaymentMethod(paymentMethod: $contract->paymentMethod);
        $entity->setExtTransactionId(extTransactionId: $contract->transactionId);
        $entity->setExtCustomerPurchaseOrderId(extCustomerPurchaseOrderId: $contract->customerPurchaseOrderId);
        $entity->setCustomerPurchaseOrderType(customerPurchaseOrderType: $contract->customerPurchaseOrderType);
        $entity->setDiscount(discount: $contract->discount);
        $entity->setNtRelevance(ntRelevance: (bool) $contract->ntRelevance);
        $entity->setCustomerGroup(customerGroup: $contract->customerGroup ?? '');
        $entity->setExtSubcontractorId(extSubcontractorId: $contract->subcontractorId);
        $entity->setPriceTransportSub(priceTransportSub: $contract->priceTransportSub);
        $entity->setPriceRecyclingSub(priceRecyclingSub: $contract->priceRecyclingSub);
        $entity->setPriceRentSub(priceRentSub: $contract->priceRentSub);
        $entity->setContractDocType(contractDocType: $contract->contractDocType ?? '');
        $entity->setCollectionPointType(collectionPointType: $contract->collectionPointType);
        $entity->setCollectionPointText(collectionPointText: $contract->collectionPointText);

        $this->contractPartnerService->upsertMany(contractPartners: $contract->contractPartners, contractId: $entity->getId());
        $this->contractPositionService->upsertMany(contractPositions: $contract->contractPositions, contractId: $entity->getId());

        $this->contractRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(Contract $contract): ContractEntity
    {
        return $this->contractRepository->findByUniqueConstraint(
            extId: $contract->id,
        ) ?? new ContractEntity(id: $contract->uuid ?? null);
    }

    /**
     * @throws BadRequestException
     */
    private function validate(Contract $contract): void
    {
        $errors = $this->validator->validate($contract);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '. ', array: iterator_to_array(iterator: $errors)));
        }
    }
}
