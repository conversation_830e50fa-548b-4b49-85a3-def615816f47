<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\NoticeText as NoticeTextEntity;
use App\Domain\Repository\NoticeTextRepository;
use App\Infrastructure\SapEurope\Resource\NoticeText;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;

readonly class NoticeTextService
{
    public function __construct(
        private NoticeTextRepository $noticeTextRepository,
    ) {
    }

    /**
     * @param NoticeText[] $noticeTexts
     *
     * @return NoticeTextEntity[]
     */
    public function upsertMany(array $noticeTexts, string $serviceId): array
    {
        return array_map(callback: fn (NoticeText $noticeText): NoticeTextEntity => $this->upsert(noticeText: $noticeText, serviceId: $serviceId), array: $noticeTexts);
    }

    public function upsert(NoticeText $noticeText, string $serviceId): NoticeTextEntity
    {
        $entity = $this->getEntityByResource(noticeText: $noticeText);

        $entity->setExtId(extId: $noticeText->id);
        $entity->setExtPosId(extPosId: $noticeText->posId);
        $entity->setServiceId(serviceId: $serviceId);
        $entity->setEndDate(endDate: DatetimeUtil::convertToDate(date: $noticeText->endDate));
        $entity->setType(type: $noticeText->type);
        $entity->setContent(content: $noticeText->content);

        $this->noticeTextRepository->save(entity: $entity);

        return $entity;
    }

    public function getEntityByResource(NoticeText $noticeText): NoticeTextEntity
    {
        return $this->noticeTextRepository->findByUniqueConstraints(
            extId: $noticeText->id,
            extPosId: $noticeText->posId,
            endDate: $noticeText->endDate,
            type: $noticeText->type
        ) ?? new NoticeTextEntity();
    }
}
