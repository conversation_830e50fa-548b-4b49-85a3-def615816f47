<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\IdentificationElement as IdentificationElementEntity;
use App\Domain\Event\DomainEvents;
use App\Domain\Repository\IdentificationElementRepository;
use App\Infrastructure\SapEurope\Resource\IdentificationElement;

readonly class IdentificationElementService
{
    public function __construct(
        private IdentificationElementRepository $identificationElementRepository,
        private DomainEvents $domainEvents,
    ) {
    }

    /**
     * @param IdentificationElement[] $identificationFeatures
     *
     * @return IdentificationElementEntity[]
     */
    public function upsertMany(array $identificationFeatures): array
    {
        return array_map(callback: fn (IdentificationElement $identificationFeature): IdentificationElementEntity => $this->upsert(identificationFeature: $identificationFeature), array: $identificationFeatures);
    }

    public function upsert(IdentificationElement $identificationFeature): IdentificationElementEntity
    {
        $entity = $this->getEntityByResource(identificationElement: $identificationFeature);

        $entity->setExtBusinessPartnerId(extBusinessPartnerId: $identificationFeature->partner ?? '');
        $entity->setExtIdentificationCategory(extIdentificationCategory: $identificationFeature->identificationCategory ?? '');
        $entity->setExtIdentificationNumber(extIdentificationNumber: $identificationFeature->identificationNumber ?? '');

        $this->domainEvents->dispatchIdentificationElementCreated(identificationElement: $entity);
        $this->identificationElementRepository->save(entity: $entity);

        return $entity;
    }

    private function getEntityByResource(IdentificationElement $identificationElement): IdentificationElementEntity
    {
        return $this->identificationElementRepository->findByUniqueConstraints(
            extBusinessPartnerId: $identificationElement->partner ?? '',
            extIdentificationCategory: $identificationElement->identificationCategory ?? '',
            extIdentificationNumber: $identificationElement->identificationNumber ?? ''
        ) ?? new IdentificationElementEntity();
    }
}
