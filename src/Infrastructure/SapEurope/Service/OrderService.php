<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Service;

use App\Domain\Entity\Enum\OrderStatus;
use App\Domain\Entity\Order as OrderEntity;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\OrderRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\SapEurope\Resource\ArchiveDocument;
use App\Infrastructure\SapEurope\Resource\Order;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class OrderService
{
    public function __construct(
        private ArchiveDocumentService $archiveDocumentService,
        private BusinessPartnerRepository $businessPartnerRepository,
        private OrderArchiveDocumentService $orderArchiveDocumentService,
        private OrderRepository $orderRepository,
        private ValidatorInterface $validator,
    ) {
    }

    /**
     * @throws BadRequestException
     * @throws \ValueError
     */
    public function upsert(Order $order): OrderEntity
    {
        $this->validate(order: $order);
        $entity = $this->getEntityByResource(order: $order);

        $entity->setExtId(extId: $order->id);
        $entity->setExtPosId(extPosId: (string) $order->posId);
        $entity->setExtServiceId(extServiceId: $order->serviceId);
        $entity->setExtServicePosId(extServicePosId: (string) $order->servicePosId);
        $entity->setExtObjectId(extObjectId: $order->objectId);
        if ($order->orderDate) {
            $entity->setOrderDate(orderDate: DatetimeUtil::convertToDate(date: $order->orderDate));
        }
        $entity->setOrderStatus(orderStatus: OrderStatus::from(value: $order->orderStatus)->value);
        $entity->setCertificateNumberInternal(certificateNumberInternal: $order->certificateNumberInternal);
        $entity->setCertificateNumber(certificateNumber: $order->certificateNumber);
        $entity->setDisposalDocumentUns(disposalDocumentUns: $order->disposalDocumentUns);
        $entity->setDisposalDocumentBgs(disposalDocumentBgs: $order->disposalDocumentBgs);

        $businessPartner = $this->getBusinessPartnerId(entity: $entity);
        $entity->setBusinessPartnerId(businessPartnerId: $businessPartner);

        $this->archiveDocumentService->upsertMany($order->archiveDocuments ?? [], orderId: $entity->getId());

        $this->addOrderArchiveDocuments(archiveDocuments: $order->archiveDocuments ?? [], orderId: $entity->getId());

        $this->orderRepository->save(entity: $entity);

        return $entity;
    }

    /**
     * @throws BadRequestException
     */
    private function validate(Order $order): void
    {
        $errors = $this->validator->validate($order);

        if (count(value: $errors) > 0) {
            throw new BadRequestException(message: implode(separator: '. ', array: iterator_to_array(iterator: $errors)));
        }
    }

    private function getEntityByResource(Order $order): OrderEntity
    {
        return $this->orderRepository->findByUniqueConstraints(
            extObjectId: $order->objectId
        ) ?? new OrderEntity(id: $order->uuid ?? null);
    }

    /**
     * @param ArchiveDocument[] $archiveDocuments
     */
    private function addOrderArchiveDocuments(array $archiveDocuments, string $orderId): void
    {
        $this->orderArchiveDocumentService->upsertMany(archiveDocuments: $archiveDocuments, orderId: $orderId);
    }

    private function getBusinessPartnerId(OrderEntity $entity): ?string
    {
        if (null !== $entity->getBusinessPartnerId()) {
            return $entity->getBusinessPartnerId();
        }

        if (null === $entity->getExtServiceId() || null === $entity->getExtServicePosId()) {
            return null;
        }

        $businessPartner = $this->businessPartnerRepository->findByExtServiceIdAndPos(
            extServiceId: $entity->getExtServiceId(),
            extServicePosId: $entity->getExtServicePosId(),
        );

        if (null === $businessPartner) {
            return null;
        }

        return $businessPartner->getId();
    }
}
