<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Util;

use App\Exception\BadRequestException;

readonly class DatetimeUtil
{
    public const string SAP_DATE_FORMAT = 'Ymd';
    public const string SAP_DATE_TIME_FORMAT = 'YmdHis';

    public static function convertToDateOrNull(?string $date): ?\DateTimeImmutable
    {
        if (null === $date || '' === $date) {
            return null;
        }

        return self::convertToDate($date);
    }

    public static function convertToDate(string $date, ?string $time = '000000'): \DateTimeImmutable
    {
        if (strlen(string: $date) > 8) {
            $formattedDate = \DateTimeImmutable::createFromFormat(format: 'Y-m-d', datetime: $date);
        } elseif (null === $time) {
            $formattedDate = \DateTimeImmutable::createFromFormat(format: self::SAP_DATE_FORMAT, datetime: $date);
        } else {
            $time = str_replace(search: ':', replace: '', subject: $time);
            $formattedDate = \DateTimeImmutable::createFromFormat(format: self::SAP_DATE_TIME_FORMAT, datetime: $date.$time);
        }

        if (false === $formattedDate) {
            throw new BadRequestException(message: 'Datetime format not valid');
        }

        return $formattedDate;
    }
}
