<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\ServiceEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON>k<PERSON>\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [ServiceEndpoint::class, 'create'],
            name: 'service-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [ServiceEndpoint::class, 'update'],
            name: 'service-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [ServiceEndpoint::class, 'create'],
            name: 'service-create-old',
            uriTemplate: '/eventServiceCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [ServiceEndpoint::class, 'update'],
            name: 'service-update-old',
            uriTemplate: '/eventServiceChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'Service',
    security: 'is_granted("ROLE_INPUT")',
)]
class Service
{
    public const string SERVICE_SAP_CREATE = 'service_sap:create';

    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Service Id', examples: ['2601375']))]
    public string $id;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Service Position Id', examples: ['1']))]
    public string|int $posId;

    #[Assert\Length(min: 0, max: 50)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Webshop: External ID', examples: ['']))]
    public ?string $extServiceId = null;

    #[Assert\Length(min: 0, max: 8)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'StartDate', examples: ['20241119']))]
    public string $startDate;

    #[Assert\Length(min: 0, max: 8)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'EndDate', examples: ['99991231']))]
    public string $endDate;

    #[Assert\Length(min: 0, max: 1)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Order Timeframe', examples: ['1']))]
    public string $period;

    #[Assert\Length(min: 0, max: 10)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'External Contract Id', examples: ['41234567']))]
    public string $contractId;

    #[Assert\Length(min: 0, max: 6)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'External Contract Position Id', examples: ['10']))]
    public string|int $contractPosId;

    #[Assert\Length(min: 0, max: 2)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Service Type', examples: ['10']))]
    public string $serviceType;

    #[Assert\Length(min: 0, max: 40)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'External Container Id', examples: ['751226']))]
    public string $containerMaterialId;

    #[Assert\Length(min: 0, max: 40)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'External Material Id', examples: ['702627']))]
    public ?string $wasteMaterialId = null;

    #[Assert\Length(min: 0, max: 30)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'External Service Location Id', examples: ['1234567']))]
    public string $serviceLocationId;

    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Container Count', examples: ['1']))]
    public ?int $containerCount = null;

    #[Assert\Length(min: 0, max: 18)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Equipment', examples: ['2721065']))]
    public string $equipment;

    #[Assert\Length(min: 0, max: 10)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'External Route Id', examples: ['40881']))]
    public ?string $routeId = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Service Frequency', examples: ['D']))]
    public string $serviceFrequency;

    #[Assert\Length(min: 0, max: 1)]
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Type of Daily Service (single/periodic)', examples: ['1']))]
    public ?string $dailyFrequency = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Weekly Service Interval', examples: ['']))]
    public ?string $weeklyFrequency = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Monthly Service Interval', examples: ['']))]
    public ?string $monthlyFrequency = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Day of the Month for Service', examples: ['']))]
    public ?string $monthlyFrequencyDay = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Pickup Day in Service Rhythm', examples: ['']))]
    public ?string $weekday = null;

    #[Assert\Length(min: 0, max: 255)]
    #[Property(new Schema(description: 'URL of the File to be Transferred', examples: ['']))]
    public ?string $link = null;

    #[Assert\Length(min: 0, max: 5)]
    #[Property(new Schema(description: 'Type of Document to be Transferred', examples: ['']))]
    public ?string $mimeType = null;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'End Time', examples: ['']))]
    public ?string $timeto = null;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'Start Time', examples: ['']))]
    public ?string $timfr = null;

    /**
     * @var NoticeText[]|null
     */
    #[Groups([self::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Notice Texts', examples: ['']))]
    public ?array $noticeTexts = null;
}
