<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\VerificationDocumentEndpoint;
use <PERSON>k<PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use <PERSON><PERSON><PERSON>\OpenApi\Schema\Type;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [VerificationDocumentEndpoint::class, 'create'],
            name: 'verification-document-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [VerificationDocumentEndpoint::class, 'update'],
            name: 'verification-document-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [VerificationDocumentEndpoint::class, 'create'],
            name: 'verification-document-create-old',
            uriTemplate: '/eventVerificationDocumentCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [VerificationDocumentEndpoint::class, 'update'],
            name: 'verification-document-update-old',
            uriTemplate: '/eventVerificationDocumentChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'VerificationDocument',
    security: 'is_granted("ROLE_INPUT")',
)]
#[Assert\Cascade]
class VerificationDocument
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Property(new Schema(type: Type::INTEGER, description: 'External Internal Verification Document Id', examples: ['8123']))]
    public int $id;

    #[Property(new Schema(type: Type::INTEGER, description: 'External Version Id', examples: ['1']))]
    public int $version;

    #[Assert\Length(min: 0, max: 12)]
    #[Property(new Schema(description: 'Certificate Number', examples: ['ENC212345678']))]
    public string $certificateNumber;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Type of Verification Procedure', examples: ['EN']))]
    public string $certificateType;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['2000001']))]
    public string $businessPartnerId;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'External Sales Organisation Id', examples: ['0104']))]
    public string $salesOrganisationId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Contract Id', examples: ['41234567']))]
    public string $contractId;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'AVV Number', examples: ['120109']))]
    public string $avvId;

    #[Assert\Length(min: 0, max: 255)]
    #[Property(new Schema(description: 'AVV Description', examples: ['halogenfreie Bearbeitungsemulsionen und -lösungen']))]
    public string $avvText;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'External Service Location Id', examples: ['1234567']))]
    public string $serviceLocationId;

    #[Property(new Schema(description: 'Approved Amount', examples: ['500']))]
    public float $approvedAmountDocument;

    #[Property(new Schema(description: 'Approved Year', examples: ['']))]
    public ?float $approvedAmountYear = null;

    #[Property(new Schema(description: 'Approved Remaining Amount', examples: ['438.311']))]
    public float $approvedRemAmountDocument;

    #[Property(new Schema(description: 'Approved Remaing Amout Year', examples: ['-7.546']))]
    public float $approvedRemAmountYear;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Amount Unit', examples: ['TO']))]
    public string $amountUnitOm;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Deletion Flag', examples: ['']))]
    public ?string $deletionFlag = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Application Date', examples: ['2021-06-28']))]
    public string $applicationDate;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Customer Receipt Date', examples: ['2021-07-05']))]
    public string $dateCustomer;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Disposal Site Receipt Date', examples: ['2021-07-06']))]
    public string $dateDisposer;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Authority Confirmation', examples: ['2021-07-06']))]
    public ?string $confirmationAuthority = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Approval Date', examples: ['2021-07-06']))]
    public string $approval;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Valid to', examples: ['2026-06-30']))]
    public string $endDate;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Lock Date', examples: ['']))]
    public ?string $lockedDate = null;

    #[Assert\Length(min: 0, max: 22)]
    #[Property(new Schema(description: 'External Object Id', examples: ['ZE00000000000061234567']))]
    public string $objectId;

    #[Assert\Length(min: 0, max: 100)]
    #[Property(new Schema(description: 'description', examples: ['Test 120109 - ABC']))]
    public string $description;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'External Document Id', examples: ['1baa1d74-2b94-43cd-9f6f-546023674345']))]
    public string $docId;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Status', examples: ['EGEN']))]
    public string $status;

    #[Assert\Length(min: 0, max: 100)]
    #[Property(new Schema(description: 'Status Description', examples: ['EN genehmigt']))]
    public string $statusText;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'Data Status', examples: ['']))]
    public ?string $dataStatus = null;

    #[Property(new Schema(description: 'Value', examples: ['']))]
    public ?string $value = null;

    #[Assert\Length(min: 0, max: 100)]
    #[Property(new Schema(description: 'Mime Type', examples: ['']))]
    public ?string $mimeType = null;

    /**
     * @var VerificationDocumentPartner[]
     */
    #[Property(new Schema(description: 'VerificationDocument Partners', examples: ['']))]
    public ?array $verificationDocumentPartner = [];

    /**
     * @var SignData[]
     */
    #[Property(new Schema(description: 'SignDatas', examples: ['']))]
    public ?array $signData = [];
}
