<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\InvoiceEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use PreZ<PERSON>\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [InvoiceEndpoint::class, 'create'],
            name: 'invoice-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [InvoiceEndpoint::class, 'update'],
            name: 'invoice-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [InvoiceEndpoint::class, 'create'],
            name: 'invoice-create-old',
            uriTemplate: '/eventInvoiceCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [InvoiceEndpoint::class, 'update'],
            name: 'invoice-update-old',
            uriTemplate: '/eventInvoiceChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'Invoice',
    security: 'is_granted("ROLE_INPUT")',
)]
#[Assert\Cascade]
class Invoice
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Invoice Id', examples: ['123445678']))]
    public string $id;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Billing Date', examples: ['2024-11-19']))]
    public string $billingDate;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'Billing Type', examples: ['ZF2']))]
    public string $type;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Cancelled Invoice Id', examples: ['']))]
    public ?string $cancelledBillingId;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'External Sales Organization', examples: ['0104']))]
    public string $salesOrganisationId;

    #[Property(new Schema(description: 'Net Value in Document Currency', examples: ['0.23']))]
    public float $netValue;

    #[Assert\Length(min: 0, max: 5)]
    #[Property(new Schema(description: 'Currency', examples: ['EUR']))]
    public string $currency;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Payer Id', examples: ['2000001']))]
    public string $payerId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['2000001']))]
    public string $businessPartnerId;

    /**
     * @var InvoiceContract[]
     */
    #[Property(new Schema(description: 'Invoice Contracts', examples: ['']))]
    public array $contracts;

    /**
     * @var ArchiveDocument[]
     */
    #[Property(new Schema(description: 'Archive Documents', examples: ['']))]
    public ?array $archiveDocuments;
}
