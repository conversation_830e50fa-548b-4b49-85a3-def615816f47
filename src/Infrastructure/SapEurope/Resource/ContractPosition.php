<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use <PERSON>k<PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Do<PERSON><PERSON>\OpenApi\Schema\Type;
use Symfony\Component\Validator\Constraints as Assert;

#[Assert\Cascade]
class ContractPosition
{
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Contract Id', examples: ['41234567']))]
    public string $id;

    #[Property(new Schema(type: Type::INTEGER, description: 'External Contract Position Id', examples: ['10']))]
    public int $posId;

    #[Property(new Schema(type: Type::INTEGER, description: 'External Contract Parent Position Id', examples: ['0']))]
    public int $parentPosId;

    #[Property(new Schema(description: 'Quantity', examples: ['1']))]
    public float $quantity;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Quantity Unit', examples: ['ST']))]
    public string $unitOm;

    #[Property(new Schema(description: 'Net Value', examples: ['-35']))]
    public ?float $netValue = null;

    #[Assert\Length(min: 0, max: 5)]
    #[Property(new Schema(description: 'Currency', examples: ['EUR']))]
    public string $currency;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Price index', examples: ['']))]
    public ?string $priceIndex = null;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'Material Type', examples: ['SERV']))]
    public string $materialType;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'External Material Id', examples: ['650616']))]
    public string $materialId;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'Material Group', examples: ['SERV']))]
    public string $materialGroup;

    #[Assert\Length(min: 0, max: 200)]
    #[Property(new Schema(description: 'Material Description', examples: ['PRITSCHE-BRS-SERV']))]
    public string $materialText;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'External Disposal Site Id', examples: ['22050']))]
    public string $wasteDisposalFacilityId;

    #[Assert\Length(min: 0, max: 254)]
    #[Property(new Schema(description: 'Disposal Site Description', examples: ['ESA extern SUB']))]
    public string $wasteDisposalFacilityText;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Rent Type', examples: ['']))]
    public ?string $rentType = null;

    #[Assert\Length(min: 0, max: 12)]
    #[Property(new Schema(description: 'Certificate Number', examples: ['']))]
    public ?string $certificateNumber = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Certificate Type', examples: ['']))]
    public ?string $certificateType = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Configuration Name', examples: ['']))]
    public ?string $configName = null;

    #[Assert\Length(min: 0, max: 70)]
    #[Property(new Schema(description: 'Configuration Value', examples: ['']))]
    public ?string $configValue = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Display Position', examples: ['1']))]
    public string $display;

    /**
     * @var ContractPartner[]
     */
    #[Property(new Schema(description: 'Contract Partners', examples: ['']))]
    public array $contractPartners = [];
}
