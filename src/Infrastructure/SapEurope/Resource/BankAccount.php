<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use Dok<PERSON>\Attribute\Property;
use <PERSON>kky\OpenApi\Schema;
use Symfony\Component\Validator\Constraints as Assert;

#[Assert\Cascade]
class BankAccount
{
    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'External Bank Connection ID', examples: ['']))]
    public ?string $id = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['']))]
    public ?string $businessPartnerId = null;

    #[Assert\Length(min: 0, max: 60)]
    #[Property(new Schema(description: 'Name of the Account Holder', examples: ['']))]
    public ?string $accountHolder = null;

    #[Assert\Length(min: 0, max: 34)]
    #[Assert\Iban]
    #[Property(new Schema(description: 'IBAN (International Bank Account Number)', examples: ['**********************']))]
    public ?string $iban;

    #[Assert\Length(min: 0, max: 11)]
    #[Property(new Schema(description: 'SWIFT/BIC for International Payments', examples: ['COBADEFFXXX']))]
    public ?string $bic;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Country', examples: ['']))]
    public ?string $bankCountry = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Flag if there is a SEPA', examples: ['']))]
    public ?string $sepaFlag = null;
}
