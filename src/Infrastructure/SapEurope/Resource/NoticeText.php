<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use Dok<PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;

class NoticeText
{
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Service Id', examples: ['']))]
    public ?string $id;

    #[Assert\Length(min: 0, max: 5)]
    #[Property(new Schema(description: 'External Service Position Id', examples: ['']))]
    public ?string $posId;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Enddate', examples: ['']))]
    public string $endDate;

    #[Assert\Length(min: 0, max: 5)]
    #[Groups([Service::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Type', examples: ['']))]
    public string $type;

    #[Assert\Length(min: 0, max: 50)]
    #[Groups([Service::SERVICE_SAP_CREATE])]
    #[Property(new Schema(description: 'Content', examples: ['']))]
    public string $content;
}
