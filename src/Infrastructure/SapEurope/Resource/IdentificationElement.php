<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use Dokky\Attribute\Property;
use Dokky\OpenApi\Schema;
use Symfony\Component\Validator\Constraints as Assert;

#[Assert\Cascade]
class IdentificationElement
{
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['']))]
    public ?string $partner;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'External Identification Type', examples: ['']))]
    public ?string $identificationCategory;

    #[Assert\Length(min: 0, max: 60)]
    #[Property(new Schema(description: 'External Identification Number', examples: ['']))]
    public ?string $identificationNumber;
}
