<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use <PERSON>k<PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Dok<PERSON>\OpenApi\Schema\Type;
use Symfony\Component\Validator\Constraints as Assert;

class ServiceFrequency
{
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Service frequency Id', examples: ['2802204']))]
    public string $id;

    #[Property(new Schema(type: Type::INTEGER, description: 'External Position Id of Service frequency', examples: ['3']))]
    public int $posId;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Start date of service frequency', examples: ['20241220']))]
    public string $startDate;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'End date of service frequency', examples: ['20241220']))]
    public string $endDate;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Service Type', examples: ['13']))]
    public string $serviceType;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Service frequency Indicator', examples: ['D']))]
    public string $serviceFrequency;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Type of Daily Service (single/periodic)', examples: ['2']))]
    public ?string $dailyFrequency = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Weekly Service Interval', examples: ['']))]
    public ?string $weeklyFrequency = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Monthly Service Interval', examples: ['']))]
    public ?string $monthlyFrequency = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Day of the Month for Service', examples: ['']))]
    public ?string $monthlyFrequencyDay = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Pickup Day in Service Rhythm', examples: ['1']))]
    public ?string $weekday;
}
