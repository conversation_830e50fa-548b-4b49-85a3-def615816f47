<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\BusinessPartnerEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON>k<PERSON>\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [BusinessPartnerEndpoint::class, 'create'],
            name: 'business-partner-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [BusinessPartnerEndpoint::class, 'update'],
            name: 'business-partner-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [BusinessPartnerEndpoint::class, 'create'],
            name: 'business-partner-create-old',
            uriTemplate: '/eventBusinessPartnerCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new <PERSON>(
            controller: [BusinessPartnerEndpoint::class, 'update'],
            name: 'business-partner-update-old',
            uriTemplate: '/eventBusinessPartnerChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'BusinessPartner',
    security: 'is_granted("ROLE_INPUT")',
)]
#[Assert\Cascade]
class BusinessPartner
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['2000001']))]
    public string $id;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'Webshop: External ID', examples: ['1734cb20-a999-439a-87d8-5db675f162cb']))]
    public ?string $extBusinessPartnerId = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Partner Type', examples: ['B']))]
    public string $type;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'IndustryCode', examples: ['O']))]
    public ?string $industryCode = null;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'Salutation', examples: ['Firma']))]
    public ?string $title = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Name of the Organization', examples: ['GreenCompany GmbH & Co. KG']))]
    public string $name1;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Name 2 of the Organization', examples: ['']))]
    public ?string $name2 = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Name 3 of the Organization', examples: ['']))]
    public ?string $name3 = null;

    #[Assert\Length(min: 0, max: 60)]
    #[Property(new Schema(description: 'Street', examples: ['Hauptstr.']))]
    public string $street;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'House Number', examples: ['1']))]
    public string $houseNumber;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Postal Code', examples: ['10827']))]
    public string $postalCode;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'City', examples: ['Berlin']))]
    public string $city;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'District', examples: ['Friedenau']))]
    public ?string $district = null;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Country', examples: ['DE']))]
    public string $country;

    #[Assert\Length(min: 0, max: 241)]
    #[Assert\Email]
    #[Property(new Schema(description: 'Email Address', examples: ['<EMAIL>']))]
    public ?string $email = null;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Telephone Number', examples: ['0123 456789']))]
    public string $telephone;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Mobile Phone Number', examples: ['']))]
    public ?string $mobilePhone = null;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'Tax Number', examples: ['DE123456789']))]
    public ?string $taxNumber = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Deletion Flag', examples: ['']))]
    public ?string $deleted = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Order Block Customer', examples: ['']))]
    public ?string $orderBlock = null;

    /**
     * @var ContactPerson[]
     */
    #[Property(new Schema(description: 'Contact Persons', examples: ['']))]
    public array $contactPersons = [];

    /**
     * @var BankAccount[]
     */
    #[Property(new Schema(description: 'Bank Accounts', examples: ['']))]
    public array $bankAccounts = [];

    /**
     * @var SalesOrganisation[]
     */
    #[Property(new Schema(description: 'Sales Organizations', examples: ['']))]
    public array $salesOrganisations = [];

    /**
     * @var IdentificationElement[]|null
     */
    #[Property(new Schema(description: 'Identification Elements', examples: ['']))]
    public ?array $identIds = [];
}
