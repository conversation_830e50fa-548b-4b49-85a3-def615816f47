<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use <PERSON>k<PERSON>\Attribute\Property;
use Dokky\OpenApi\Schema;
use Symfony\Component\Validator\Constraints as Assert;

#[Assert\Cascade]
class WasteStatistic
{
    public ?string $uuid = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['2000001']))]
    public string $businessPartnerId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Contract Id', examples: ['41234567']))]
    public ?string $contractId;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'External Service Location Id', examples: ['1234567']))]
    public ?string $serviceLocationId;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'External Service Location Address', examples: ['1234567']))]
    public ?string $serviceLocationAddress;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'External Service Location Info', examples: ['1234567']))]
    public ?string $serviceLocationInfo;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'External Material Id', examples: ['702627']))]
    public string $wasteMaterialId;

    #[Assert\Length(min: 0, max: 250)]
    #[Property(new Schema(description: 'External Material Text', examples: ['702627']))]
    public string $wasteMaterialText;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'AVV Number', examples: ['120109']))]
    public string $avvId;

    #[Assert\Length(min: 0, max: 255)]
    #[Property(new Schema(description: 'AVV Description', examples: ['halogenfreie Bearbeitungsemulsionen und -lösungen']))]
    public string $avvText;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Order Date', examples: ['2023-01-13']))]
    public string $orderDate;

    #[Property(new Schema(description: 'Amount', examples: ['2569']))]
    public string $amount;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Unit', examples: ['TO']))]
    public string $amountUnitOm;

    #[Property(new Schema(description: 'Net Value', examples: ['-35']))]
    public string $netValue;

    #[Assert\Length(min: 0, max: 5)]
    #[Property(new Schema(description: 'Currency', examples: ['EUR']))]
    public string $currency;
}
