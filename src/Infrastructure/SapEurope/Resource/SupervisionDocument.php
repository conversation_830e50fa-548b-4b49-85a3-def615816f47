<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\SupervisionDocumentEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Pre<PERSON>ero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [SupervisionDocumentEndpoint::class, 'create'],
            name: 'supervision-document-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [SupervisionDocumentEndpoint::class, 'update'],
            name: 'supervision-document-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [SupervisionDocumentEndpoint::class, 'create'],
            name: 'supervision-document-create-old',
            uriTemplate: '/eventSupervisionDocumentCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [SupervisionDocumentEndpoint::class, 'update'],
            name: 'supervision-document-update-old',
            uriTemplate: '/eventSupervisionDocumentChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'SupervisionDocument',
    security: 'is_granted("ROLE_INPUT")',
)]
#[Assert\Cascade]
class SupervisionDocument
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 0, max: 15)]
    #[Property(new Schema(description: 'External SuperVision Id', examples: ['16912345678901']))]
    public string $id;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'External Document Id', examples: ['abc1232d-6d5d-4846-ae8f-ed1e4607ac12']))]
    public string $docId;

    #[Property(new Schema(description: 'External Internal Verification Number', examples: ['8123']))]
    public int $verificationDocumentId;

    #[Property(new Schema(description: 'External Verification Document Version', examples: ['1']))]
    public int $verificationDocumentVersion;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'Certificate Number', examples: ['ENC212345678']))]
    public string $certificateNumber;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Route Id', examples: ['40881']))]
    public string $routeId;

    #[Assert\Length(min: 0, max: 254)]
    #[Property(new Schema(description: 'Description', examples: ['"CompanyDesc Test123"']))]
    public string $description;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'AVV Number', examples: ['120109']))]
    public string $avvId;

    #[Assert\Length(min: 0, max: 255)]
    #[Property(new Schema(description: 'AVV Description', examples: ['halogenfreie Bearbeitungsemulsionen und -lösungen']))]
    public string $avvText;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'External Waste Material Id', examples: ['']))]
    public ?string $wasteMaterialId = null;

    #[Property(new Schema(description: 'Amount', examples: ['2569']))]
    public float $amount;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Amount Unit', examples: ['KG']))]
    public string $amountUnitOm;

    #[Assert\Length(min: 0, max: 22)]
    #[Property(new Schema(description: 'External Order Object Id', examples: ['WE00000000000012345678']))]
    public ?string $orderObjectId = null;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'External Order id', examples: ['7512389']))]
    public string $orderId;

    #[Property(new Schema(description: 'External Order Position Id', examples: ['2']))]
    public int $orderPosId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Order Date', examples: ['2024-11-22']))]
    public string $orderDate;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'External Sales Organisation Id', examples: ['0104']))]
    public string $salesOrganisationId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Created Date', examples: ['2024-11-21']))]
    public string $createdDate;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'Role in the Verification Procedure', examples: ['ENT']))]
    public string $role;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Status', examples: ['SIGNED']))]
    public string $status;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'Date Status', examples: ['']))]
    public ?string $dataStatus = null;

    #[Property(new Schema(description: 'Value', examples: ['']))]
    public ?string $value = null;

    #[Assert\Length(min: 0, max: 100)]
    #[Property(new Schema(description: 'Mime Type', examples: ['']))]
    public ?string $mimeType = null;

    /**
     * @var SupervisionDocumentPartner[]
     */
    #[Property(new Schema(description: 'Supervision Document Partners', examples: ['']))]
    public ?array $supervisionDocumentPartner = [];

    /**
     * @var SignData[]
     */
    #[Property(new Schema(description: 'Sign Datas', examples: ['']))]
    public ?array $signData = [];
}
