<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use <PERSON>k<PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Do<PERSON><PERSON>\OpenApi\Schema\Type;
use Symfony\Component\Validator\Constraints as Assert;

#[Assert\Cascade]
class ContractPartner
{
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Contract Partner Id', examples: ['2000001']))]
    public ?string $id = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Contract Id', examples: ['41234567']))]
    public string $contractId;

    #[Property(new Schema(type: Type::INTEGER, description: 'External Contract Position Id', examples: ['10']))]
    public ?int $contractPosId = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Partner Role', examples: ['AG']))]
    public string $role;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'Salutation', examples: ['']))]
    public ?string $title = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Name', examples: ['GreenCompany GmbH & Co. KG']))]
    public ?string $name1 = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'First Name', examples: ['']))]
    public ?string $name2 = null;

    #[Assert\Length(min: 0, max: 60)]
    #[Property(new Schema(description: 'Street', examples: ['Hauptstr.']))]
    public ?string $street = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'House Number', examples: ['1']))]
    public ?string $houseNumber = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Postal Code', examples: ['10827']))]
    public ?string $postalCode = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'City', examples: ['Berlin']))]
    public ?string $city = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'District', examples: ['Friedenau']))]
    public ?string $district = null;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Country', examples: ['DE']))]
    public ?string $country = null;
}
