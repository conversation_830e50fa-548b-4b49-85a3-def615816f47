<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\OrderEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON>k<PERSON>\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [OrderEndpoint::class, 'create'],
            name: 'order-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [OrderEndpoint::class, 'update'],
            name: 'order-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [OrderEndpoint::class, 'create'],
            name: 'order-create-old',
            uriTemplate: '/eventOrderCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [OrderEndpoint::class, 'update'],
            name: 'order-update-old',
            uriTemplate: '/eventOrderChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'Order',
    security: 'is_granted("ROLE_INPUT")',
)]
class Order
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'External Order Id', examples: ['7512389']))]
    public string $id;

    #[Assert\NotBlank]
    #[Property(new Schema(description: 'External Order Position Id', examples: ['2']))]
    public int $posId;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'Webshop: External ID', examples: ['']))]
    public ?string $extOrderId = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Service Id', examples: ['2601375']))]
    public string $serviceId;

    #[Assert\NotBlank]
    #[Property(new Schema(description: 'External Service Position id', examples: ['1']))]
    public int $servicePosId;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'Webshop: External ID', examples: ['']))]
    public ?string $extServiceId = null;

    #[Assert\Length(min: 0, max: 22)]
    #[Property(new Schema(description: 'External Object Id', examples: ['WE00000000000012345678']))]
    public string $objectId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Order Date', examples: ['2023-01-13']))]
    public ?string $orderDate;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Order Status', examples: ['4']))]
    public string $orderStatus;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Certificate Number internal', examples: ['']))]
    public ?string $certificateNumberInternal = null;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'Certificate Number', examples: ['']))]
    public ?string $certificateNumber = null;

    #[Assert\Length(min: 0, max: 15)]
    #[Property(new Schema(description: 'UNS Number', examples: ['']))]
    public ?string $disposalDocumentUns = null;

    #[Assert\Length(min: 0, max: 15)]
    #[Property(new Schema(description: 'BGS / SuperVisionDocument Number', examples: ['']))]
    public ?string $disposalDocumentBgs = null;

    /**
     * @var array<ArchiveDocument>|null
     */
    #[Property(new Schema(description: 'Archive Documents', examples: ['']))]
    public ?array $archiveDocuments = [];
}
