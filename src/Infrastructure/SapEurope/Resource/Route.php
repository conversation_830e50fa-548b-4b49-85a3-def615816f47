<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\RouteEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON>k<PERSON>\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [RouteEndpoint::class, 'create'],
            name: 'route-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [RouteEndpoint::class, 'update'],
            name: 'route-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [RouteEndpoint::class, 'create'],
            name: 'route-create-old',
            uriTemplate: '/eventRouteCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [RouteEndpoint::class, 'update'],
            name: 'route-update-old',
            uriTemplate: '/eventRouteChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'Route',
    security: 'is_granted("ROLE_INPUT")',
)]
#[Assert\Cascade]
class Route
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External route Id', examples: ['40881']))]
    public string $id;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Description of Route', examples: ['HN-XX 1234 AZV MONTAG']))]
    public string $text;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Start date of Route', examples: ['20240129']))]
    public string $startDate;

    /**
     * @var ServiceFrequency[]
     */
    #[Assert\Valid]
    #[Property(new Schema(description: 'Service Frequencies', examples: ['']))]
    public array $serviceFrequency;
}
