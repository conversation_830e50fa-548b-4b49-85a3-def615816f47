<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\ArchiveEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON>k<PERSON>\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Get(
            controller: [ArchiveEndpoint::class, 'get'],
            uriTemplate: '/internal/archives/{archiveId}/{documentId}',
            pathParameters: [
                new PathParameter(name: 'archiveId', type: 'string', description: 'Archive ID', constraint: '[a-zA-Z0-9_\-\.]+'),
                new PathParameter(name: 'documentId', type: 'string', description: 'Document ID', constraint: '[a-zA-Z0-9_\-\.]+'),
            ],
            requestDescription: 'Binary file contents',
            responseType: ContentType::BINARY,
        ),
    ],
    tag: 'File',
    security: 'is_granted("ROLE_INPUT")',
)]
class ArchiveDocument
{
    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'External Content Repository Identification', examples: ['T0']))]
    public string $id;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'External Document Id', examples: ['125056B40A031EDFA9CC51692FED1C49']))]
    public string $documentId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Document Type', examples: ['Z070']))]
    public string $documentType;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Storage Date', examples: ['20241119']))]
    public string $storageDate;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Expiry Date', examples: ['']))]
    public ?string $expiryDate;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Externa Object', examples: ['VBRK']))]
    public string $object;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'External Object Id', examples: ['0123445678                   0009123456']))]
    public string $objectId;

    #[Property(new Schema(description: 'Value', examples: ['']))]
    public ?string $value;

    #[Assert\Length(min: 0, max: 100)]
    #[Property(new Schema(description: 'Mime Type', examples: ['application/pdf']))]
    public string $mimeType;
}
