<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use Dokky\Attribute\Property;
use Dokky\OpenApi\Schema;
use Symfony\Component\Validator\Constraints as Assert;

class InvoiceContract
{
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Contract Id', examples: ['41234567']))]
    public string $id;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Invoice Id', examples: ['123445678']))]
    public string $invoiceId;
}
