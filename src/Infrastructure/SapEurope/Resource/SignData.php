<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use Dok<PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Symfony\Component\Validator\Constraints as Assert;

class SignData
{
    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'External Document Id', examples: ['']))]
    public ?string $docId = null;

    #[Property(new Schema(description: 'Running Number', examples: ['1']))]
    public int $sequence;

    #[Assert\Length(min: 0, max: 70)]
    #[Property(new Schema(description: 'External SignData id', examples: ['ENT-ab83cc91-66dd-4f2c-9bfc-6e670f7b5189']))]
    public string $id;

    #[Property(new Schema(description: 'Running Number', examples: ['1']))]
    public int $count;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'Role in the Verification Procedure', examples: ['ENT']))]
    public string $role;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Digital Signature: Date', examples: ['2024-11-27']))]
    public string $signDate;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Digital Signature: Time', examples: ['12:32:06']))]
    public string $signTime;

    #[Assert\Length(min: 0, max: 70)]
    #[Property(new Schema(description: 'Signer', examples: ['"Mustermann, Max"']))]
    public string $signer;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Status', examples: ['STA_OK']))]
    public string $status;

    #[Assert\Length(min: 0, max: 60)]
    #[Property(new Schema(description: 'Status Description', examples: ['Signaturstatus gut']))]
    public string $statusText;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Valid status', examples: ['X']))]
    public string $valid;
}
