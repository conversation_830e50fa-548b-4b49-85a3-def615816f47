<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use Dokky\Attribute\Property;
use Dokky\OpenApi\Schema;
use Symfony\Component\Validator\Constraints as Assert;

#[Assert\Cascade]
class SalesOrganisation
{
    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'Sales Organization', examples: ['0104']))]
    public string $id;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Business Partner Number', examples: ['2000001']))]
    public string $businessPartnerId;
}
