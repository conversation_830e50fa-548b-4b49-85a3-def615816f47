<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\ContractEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON>k<PERSON>\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [ContractEndpoint::class, 'create'],
            name: 'contract-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [ContractEndpoint::class, 'update'],
            name: 'contract-update',
            uriTemplate: '/contracts',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [ContractEndpoint::class, 'create'],
            name: 'contract-create-old',
            uriTemplate: '/eventContractCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [ContractEndpoint::class, 'update'],
            name: 'contract-update-old',
            uriTemplate: '/eventContractChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'Contract',
    security: 'is_granted("ROLE_INPUT")',
)]
#[Assert\Cascade]
class Contract
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Contract Id', examples: ['41234567']))]
    public string $id;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'Webshop: External ID', examples: ['']))]
    public ?string $extContractId = null;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'Webshop: External ID', examples: ['']))]
    public ?string $extWebshopId = null;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'External Sales Organization Id', examples: ['0104']))]
    public string $salesOrganisationId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Route Id', examples: ['40881']))]
    public ?string $routeId = null;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'Assigned Sales Organization', examples: ['']))]
    public ?string $shippingPoint = null;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Start DateTime', examples: ['20241119']))]
    public string $contractStartDate;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'End DateTime', examples: ['30231130']))]
    public string $contractEndDate;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Contract Type', examples: ['B']))]
    public string $contractType;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Description', examples: ['Test Contract']))]
    public string $description;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'External Service Location Id', examples: ['1234567']))]
    public ?string $serviceLocationId = null;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'Payment Terms', examples: ['0001']))]
    public string $paymentTerm;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'Payment Method', examples: ['']))]
    public ?string $paymentMethod = null;

    #[Assert\Length(min: 0, max: 24)]
    #[Property(new Schema(description: 'External Transaction ID', examples: ['']))]
    public ?string $transactionId = null;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'External Customer Reference', examples: ['Reference']))]
    public ?string $customerPurchaseOrderId = null;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'Customer Order Type', examples: ['WEB']))]
    public string $customerPurchaseOrderType;

    #[Assert\Length(min: 0, max: 12)]
    #[Property(new Schema(description: 'Discount code', examples: ['']))]
    public ?string $discount = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Relevance', examples: ['']))]
    public ?string $ntRelevance = null;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Customer Group', examples: ['BRS']))]
    public ?string $customerGroup = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Subcontractor Id', examples: ['']))]
    public ?string $subcontractorId = null;

    #[Property(new Schema(description: 'Subconractor Transport Price', examples: ['']))]
    public ?float $priceTransportSub = null;

    #[Property(new Schema(description: 'Subconractor Recycling Price', examples: ['']))]
    public ?float $priceRecyclingSub = null;

    #[Property(new Schema(description: 'Subconractor Rent Sub', examples: ['']))]
    public ?float $priceRentSub = null;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'Contract Document Type', examples: ['ZSTR']))]
    public ?string $contractDocType = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'CollectionPoint Type', examples: ['']))]
    public ?string $collectionPointType = null;

    #[Assert\Length(min: 0, max: 60)]
    #[Property(new Schema(description: 'CollectionPoint Description', examples: ['']))]
    public ?string $collectionPointText = null;

    /**
     * @var ContractPartner[]
     */
    #[Property(new Schema(description: 'Contract Partners', examples: ['']))]
    public array $contractPartners;

    /**
     * @var ContractPosition[]
     */
    #[Property(new Schema(description: 'Contract Positions', examples: ['']))]
    public array $contractPositions;
}
