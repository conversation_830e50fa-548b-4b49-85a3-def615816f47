<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\ServiceLocationEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [ServiceLocationEndpoint::class, 'create'],
            name: 'service-location-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [ServiceLocationEndpoint::class, 'update'],
            name: 'service-location-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [ServiceLocationEndpoint::class, 'create'],
            name: 'service-location-create-old',
            uriTemplate: '/eventServiceLocationCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [ServiceLocationEndpoint::class, 'update'],
            name: 'service-location-update-old',
            uriTemplate: '/eventServiceLocationChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'ServiceLocation',
    security: 'is_granted("ROLE_INPUT")',
)]
class ServiceLocation
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Location of the Container', examples: ['1234567']))]
    public string $id;

    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'Webshop: External ID', examples: ['']))]
    public ?string $extServiceLocationId = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Name', examples: ['GreenCompany GmbH & Co. KG']))]
    #[Assert\NotBlank]
    public string $name1;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Name 2', examples: ['']))]
    public ?string $name2 = null;

    #[Assert\Length(min: 0, max: 60)]
    #[Property(new Schema(description: 'Street', examples: ['Hauptstr.']))]
    #[Assert\NotBlank]
    public string $street;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'House Number', examples: ['2']))]
    #[Assert\NotBlank]
    public string $houseNumber;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Postal Code', examples: ['10827']))]
    #[Assert\NotBlank]
    public string $postalCode;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'City', examples: ['Berlin']))]
    #[Assert\NotBlank]
    public string $city;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'District', examples: ['Friedenau']))]
    public ?string $district = null;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Country', examples: ['DE']))]
    #[Assert\NotBlank]
    public string $country;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Additional Information', examples: ['More Information']))]
    public ?string $additionalInformation = null;
}
