<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use App\Infrastructure\SapEurope\HttpEndpoint\ContractServiceLocationEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Pre<PERSON>ero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'sap_europe',
    operations: [
        new Post(
            controller: [ContractServiceLocationEndpoint::class, 'create'],
            name: 'contract-service-location-create',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Patch(
            controller: [ContractServiceLocationEndpoint::class, 'update'],
            name: 'contract-service-location-update',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [ContractServiceLocationEndpoint::class, 'create'],
            name: 'contract-service-location-create-old',
            uriTemplate: '/eventContractServiceLocationCreated',
            pathParameters: [],
            normalizationContext: [],
        ),
        new Post(
            controller: [ContractServiceLocationEndpoint::class, 'update'],
            name: 'contract-service-location-update-old',
            uriTemplate: '/eventContractServiceLocationChanged',
            pathParameters: [],
            normalizationContext: [],
        ),
    ],
    tag: 'ContractServiceLocation',
    security: 'is_granted("ROLE_INPUT")',
)]
#[Assert\Cascade]
class ContractServiceLocation
{
    public ?string $uuid = null;

    #[Assert\NotBlank]
    #[Property(new Schema(description: 'External Service Id', examples: ['2463173']))]
    public int $id;

    #[Property(new Schema(description: 'External Service Position Id', examples: ['1']))]
    public int $posId;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Valid to', examples: ['30231130']))]
    public string $validTo;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Webshop Date', examples: ['000000']))]
    public string $validToTime;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Valid from', examples: ['20241119']))]
    public string $validFrom;

    #[Assert\Length(min: 0, max: 8)]
    #[Property(new Schema(description: 'Webshop Date', examples: ['000000']))]
    public string $validFromTime;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Contract Id', examples: ['41234567']))]
    public string $contractId;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'External Contract Position Id', examples: ['']))]
    public ?string $contractPosId = null;

    #[Assert\Length(min: 0, max: 18)]
    #[Property(new Schema(description: 'Equipment', examples: ['000000000002721065']))]
    public string $equipment;

    #[Assert\Length(min: 0, max: 18)]
    #[Property(new Schema(description: 'Serial Number', examples: ['000000000000000001']))]
    public string $serialNumber;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'External Container Id', examples: ['751226']))]
    public string $containerMaterialId;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'External Service Location Id', examples: ['1234567']))]
    public string $serviceLocationId;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['2000001']))]
    public string $businessPartnerId;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'End Time', examples: ['']))]
    public ?string $timeto = null;

    #[Assert\Length(min: 0, max: 6)]
    #[Property(new Schema(description: 'Start Time', examples: ['']))]
    public ?string $timefr = null;
}
