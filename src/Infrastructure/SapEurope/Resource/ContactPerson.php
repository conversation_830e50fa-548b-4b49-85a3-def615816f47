<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use Dok<PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Symfony\Component\Validator\Constraints as Assert;

#[Assert\Cascade]
class ContactPerson
{
    #[Assert\NotBlank]
    #[Property(new Schema(description: 'External Contact Person Id', examples: ['2000001']))]
    public string|int $id;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['2000001']))]
    public ?string $businessPartnerId;

    #[Assert\Length(min: 0, max: 4)]
    #[Property(new Schema(description: 'External Sales Organization', examples: ['0104']))]
    public string $salesOrganisationId;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Function of the Contact Person', examples: ['Z1']))]
    public ?string $function = null;

    #[Assert\Length(min: 0, max: 2)]
    #[Property(new Schema(description: 'Partner Role', examples: ['AG']))]
    public string $role;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'Salutation', examples: ['Herr']))]
    public ?string $title = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'Last Name', examples: ['Max']))]
    public ?string $lastName = null;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'First Name', examples: ['Mustermann']))]
    public ?string $firstName = null;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Country', examples: ['DE']))]
    public ?string $country = null;

    #[Assert\Length(min: 0, max: 241)]
    #[Assert\Email]
    #[Property(new Schema(description: 'Email Address', examples: ['<EMAIL>']))]
    public ?string $email = null;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Telephone Number', examples: ['0123 456789']))]
    public ?string $telephone = null;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Mobile Phone Number', examples: ['']))]
    public ?string $mobilePhone = null;
}
