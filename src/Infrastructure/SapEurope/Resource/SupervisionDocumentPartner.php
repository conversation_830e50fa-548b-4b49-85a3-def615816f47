<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Resource;

use <PERSON>k<PERSON>\Attribute\Property;
use <PERSON><PERSON><PERSON>\OpenApi\Schema;
use Symfony\Component\Validator\Constraints as Assert;

class SupervisionDocumentPartner
{
    #[Assert\Length(min: 0, max: 50)]
    #[Property(new Schema(description: 'External Document Id', examples: ['']))]
    public ?string $docId = null;

    #[Assert\Length(min: 0, max: 9)]
    #[Property(new Schema(description: 'External SuperVision Document Partner Id', examples: ['E71234567']))]
    public string $id;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'External Check id', examples: ['1']))]
    public string $checkId;

    #[Assert\Length(min: 0, max: 5)]
    #[Property(new Schema(description: 'Type / Role', examples: ['ERZ']))]
    public string $type;

    #[Assert\Length(min: 0, max: 20)]
    #[Property(new Schema(description: 'Layer Name in Accompanying Document', examples: ['BGSENTLayer']))]
    public string $layername;

    #[Assert\Length(min: 0, max: 15)]
    #[Property(new Schema(description: 'External SuperVision Document Id', examples: ['16912345678901']))]
    public string $supervisionDocumentId;

    #[Assert\Length(min: 0, max: 35)]
    #[Property(new Schema(description: 'Name', examples: ['GreenCompany GmbH & Co. KG']))]
    public string $name1;

    #[Assert\Length(min: 0, max: 35)]
    #[Property(new Schema(description: 'Name 2', examples: ['']))]
    public ?string $name2 = null;

    #[Assert\Length(min: 0, max: 60)]
    #[Property(new Schema(description: 'Street', examples: ['Hauptstr.']))]
    public string $street;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'House Number', examples: ['1']))]
    public ?string $houseNumber = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Postal Code', examples: ['10827']))]
    public string $postalCode;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'City', examples: ['Berlin']))]
    public string $city;

    #[Assert\Length(min: 0, max: 40)]
    #[Property(new Schema(description: 'District', examples: ['']))]
    public ?string $district = null;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Country', examples: ['DE']))]
    public string $country;

    #[Assert\Length(min: 0, max: 35)]
    #[Property(new Schema(description: 'Contact Person', examples: ['']))]
    public ?string $contactPerson = null;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Telephone Number', examples: ['']))]
    public ?string $telephone = null;

    #[Assert\Length(min: 0, max: 30)]
    #[Property(new Schema(description: 'Fax Number', examples: ['']))]
    public ?string $fax = null;

    #[Assert\Length(min: 0, max: 241)]
    #[Assert\Email]
    #[Property(new Schema(description: 'Email Address', examples: ['']))]
    public ?string $email = null;

    #[Assert\Length(min: 0, max: 15)]
    #[Property(new Schema(description: 'License Plate Number', examples: ['']))]
    public ?string $licencePlateNumber1 = null;

    #[Assert\Length(min: 0, max: 15)]
    #[Property(new Schema(description: 'License Plate Number', examples: ['']))]
    public ?string $licencePlateNumber2 = null;

    #[Property(new Schema(description: 'Quantity', examples: ['2569']))]
    public float $amount;

    #[Assert\Length(min: 0, max: 3)]
    #[Property(new Schema(description: 'Unit', examples: ['KG']))]
    public string $amountUnitOm;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'Takeover Date', examples: ['2024-11-22']))]
    public string $takeoverDate;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Receipt Flag', examples: ['']))]
    public ?bool $receiptFlag = null;

    #[Assert\Length(min: 0, max: 1)]
    #[Property(new Schema(description: 'Role Allowed', examples: ['']))]
    public ?bool $roleAllowed = null;

    #[Assert\Length(min: 0, max: 10)]
    #[Property(new Schema(description: 'External Business Partner Id', examples: ['2000001']))]
    public string $businessPartnerId;
}
