<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Client;

use App\Domain\Entity\Enum\Tenant;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class SapClientConfig
{
    public function __construct(
        #[Autowire(env: 'SAP_EUROPE_URL')]
        private string $url,
        #[Autowire(env: 'SAP_EUROPE_USERNAME')]
        private string $username,
        #[Autowire(env: 'SAP_EUROPE_PASSWORD')]
        private string $password,
    ) {
    }

    public function getLogName(): string
    {
        return 'SAP Europe';
    }

    public function getServiceUrl(): string
    {
        return $this->url.'/services';
    }

    public function getArchiveDocumentUrl(string $archiveId, string $documentId): string
    {
        return $this->url."/archives(id='$archiveId',documentId='$documentId')/\$value";
    }

    public function getWasteStatisticUrl(): string
    {
        return $this->url.'/wasteStatistics';
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function getTenantShortcut(): string
    {
        return Tenant::EUROPE->value;
    }
}
