<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Client;

use App\Infrastructure\Framework\Serializer\SerializerException;
use App\Infrastructure\Framework\Serializer\SerializerInterface;
use App\Infrastructure\SapEurope\Exception\SapCallException;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

readonly class SapPostClient
{
    public function __construct(
        private HttpClientInterface $httpClient,
        private LoggerInterface $logger,
        private SapClientConfig $config,
        private SerializerInterface $serializer,
    ) {
    }

    /**
     * @param array{
     *     json_decode_options?: int,
     *     groups?: array<string>,
     *     skip_null_values?: bool,
     *     datetime_format?: string,
     *     datetime_format_strict?: bool,
     *     datetime_target_timezone?: string
     * } $context
     */
    public function request(string $url, mixed $data, array $context = []): string
    {
        $response = $this->sendJsonDataToSap(url: $url, data: $data, context: $context);

        try {
            return $response->getContent(false);
        } catch (ExceptionInterface $e) {
            $this->logger->critical(
                message: sprintf('%s: Error while getting content from response.', $this->config->getLogName()),
                context: [
                    'exception' => $e,
                ],
            );
        }
        throw new \RuntimeException(message: 'Failed to get response content from SAP.');
    }

    /**
     * @param array{
     *     json_decode_options?: int,
     *     groups?: array<string>,
     *     skip_null_values?: bool,
     *     datetime_format?: string,
     *     datetime_format_strict?: bool,
     *     datetime_target_timezone?: string
     * } $context
     *
     * @return array<string, mixed>|array<mixed, array<string, mixed>>
     */
    public function requestJsonResponse(string $url, mixed $data, array $context = []): array
    {
        $response = $this->request(url: $url, data: $data, context: $context);

        /** @var array<mixed, array<string, mixed>> $jsonArray */
        $jsonArray = json_decode($response, associative: true);

        if (array_key_exists(key: 'd', array: $jsonArray)) {
            /** @var array<string, mixed> $jsonArray */
            $jsonArray = $jsonArray['d'];
        }

        return $jsonArray;
    }

    /**
     * @throws TransportExceptionInterface
     */
    private function sendJsonPostRequest(string $url, string $data): ResponseInterface
    {
        return $this->httpClient->request(
            method: 'POST',
            url: $url,
            options: [
                'headers' => [
                    'X-Requested-With' => 'JSONHttpRequest',
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'Accept-Language' => 'de-DE',
                ],
                'body' => $data,
            ],
        );
    }

    /**
     * @param array{
     *     json_decode_options?: int,
     *     groups?: array<string>,
     *     skip_null_values?: bool,
     *     datetime_format?: string,
     *     datetime_format_strict?: bool,
     *     datetime_target_timezone?: string
     * } $context
     */
    private function sendJsonDataToSap(string $url, mixed $data, array $context = []): ResponseInterface
    {
        try {
            $this->logger->debug(
                message: sprintf('%s: sending JSON data', $this->config->getLogName())
            );

            $jsonData = $this->serializer->serialize($data, 'json', $context);
            $response = $this->sendJsonPostRequest(url: $url, data: $jsonData);

            $this->logger->debug(
                message: sprintf('%s: Response received while sending JSON data',
                    $this->config->getLogName()
                ),
                context: [
                    'responseCode' => $response->getStatusCode(),
                    'responseHeaders' => $response->getHeaders(false),
                    'responseBody' => $response->getContent(false),
                ],
            );

            if ($response->getStatusCode() >= 300) {
                $this->logger->error(
                    message: sprintf('%s: Error received while sending JSON data',
                        $this->config->getLogName()
                    ),
                    context: [
                        'responseCode' => $response->getStatusCode(),
                        'responseHeaders' => $response->getHeaders(false),
                        'responseBody' => $response->getContent(false),
                    ],
                );
            }

            return $response;
        } catch (ExceptionInterface|SerializerException $e) {
            $this->logger->critical(
                message: sprintf('%s: Error while sending JSON data',
                    $this->config->getLogName()
                ),
                context: [
                    'exception' => $e,
                ],
            );
        }
        throw new SapCallException(
            message: sprintf(
                '%s: SAP-Client call failed with exception %s',
                $this->config->getLogName(),
                $e->getMessage(),
            )
        );
    }
}
