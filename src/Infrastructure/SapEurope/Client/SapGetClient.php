<?php

declare(strict_types=1);

namespace App\Infrastructure\SapEurope\Client;

use App\Infrastructure\SapEurope\Exception\SapCallException;
use Psr\Log\LoggerInterface;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

readonly class SapGetClient
{
    public function __construct(
        private HttpClientInterface $httpClient,
        private SapClientConfig $config,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @param array<string, string>|null $query
     */
    public function request(string $url, ?array $query = null): string
    {
        $response = $this->fetchDataFromSap(url: $url, query: $query);

        try {
            return $response->getContent(false);
        } catch (ExceptionInterface $e) {
            $this->logger->critical(
                message: sprintf('%s: Error while getting content from response.', $this->config->getLogName()),
                context: [
                    'exception' => $e,
                ],
            );
        }
        throw new \RuntimeException(message: 'Failed to get response content from SAP.');
    }

    /**
     * @param array<string, string>|null $query
     *
     * @return array<string, mixed>|array<mixed, array<string, mixed>>
     */
    public function requestJsonResponse(string $url, ?array $query = null): array
    {
        $response = $this->request(url: $url, query: $query);

        /** @var array<mixed, array<string, mixed>> $jsonArray */
        $jsonArray = json_decode($response, associative: true);

        if (array_key_exists(key: 'd', array: $jsonArray)) {
            /** @var array<string, mixed> $jsonArray */
            $jsonArray = $jsonArray['d'];
        }

        return $jsonArray;
    }

    /**
     * @throws TransportExceptionInterface
     */
    private function sendGetRequest(string $url): ResponseInterface
    {
        return $this->httpClient->request(
            method: 'GET',
            url: $url,
            options: [
                'auth_basic' => [$this->config->getUsername(), $this->config->getPassword()],
            ],
        );
    }

    /**
     * @param array<string, string> $query
     *
     * @throws TransportExceptionInterface
     */
    private function sendGetRequestWithQuery(string $url, array $query): ResponseInterface
    {
        return $this->httpClient->request(
            method: 'GET',
            url: $url,
            options: [
                'auth_basic' => [$this->config->getUsername(), $this->config->getPassword()],
                'query' => $query,
            ],
        );
    }

    /**
     * @param array<string, string>|null $query
     */
    private function fetchDataFromSap(string $url, ?array $query = null): ResponseInterface
    {
        try {
            $this->logger->debug(
                message: sprintf('%s: fetching data:', $this->config->getLogName())
            );

            if (null === $query) {
                $response = $this->sendGetRequest(url: $url);
            } else {
                $response = $this->sendGetRequestWithQuery(url: $url, query: $query);
            }

            $this->logger->debug(
                message: sprintf(
                    '%s: Response received while fetching data.',
                    $this->config->getLogName(),
                ),
                context: [
                    'responseCode' => $response->getStatusCode(),
                    'responseHeaders' => $response->getHeaders(false),
                    'responseBody' => $response->getContent(false),
                ],
            );

            if ($response->getStatusCode() >= 300) {
                $this->logger->error(
                    message: sprintf(
                        '%s: Error received while fetching data.',
                        $this->config->getLogName(),
                    ),
                    context: [
                        'responseCode' => $response->getStatusCode(),
                        'responseHeaders' => $response->getHeaders(false),
                        'responseBody' => $response->getContent(false),
                    ],
                );

                throw new SapCallException(
                    message: sprintf(
                        '%s: SAP-Client call failed with code %s',
                        $this->config->getLogName(),
                        $response->getStatusCode(),
                    )
                );
            }

            return $response;
        } catch (ExceptionInterface $e) {
            $this->logger->critical(
                message: sprintf('%s: Error while fetching data.', $this->config->getLogName()),
                context: [
                    'exception' => $e,
                ],
            );
            throw new SapCallException(
                message: sprintf(
                    '%s: SAP-Client call failed with exception %s',
                    $this->config->getLogName(),
                    $e->getMessage(),
                )
            );
        }
    }
}
