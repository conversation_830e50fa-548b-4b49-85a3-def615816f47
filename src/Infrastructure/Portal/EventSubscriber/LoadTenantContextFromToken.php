<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\EventSubscriber;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUser;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Http\Event\AuthenticationTokenCreatedEvent;

#[AsEventListener]
readonly class LoadTenantContextFromToken
{
    public function __construct(
        private TenantContext $tenantContext,
    ) {
    }

    public function __invoke(AuthenticationTokenCreatedEvent $event): void
    {
        $user = $event->getPassport()->getUser();
        if (!$user instanceof KeycloakUser || !in_array(needle: UserRole::ROLE_PORTAL->value, haystack: $user->getRoles())) {
            return;
        }

        $token = $event->getAuthenticatedToken();
        $this->resolveTenant(token: $token);
    }

    private function resolveTenant(TokenInterface $token): void
    {
        $user = $token->getUser();

        if ($user instanceof KeycloakUser) {
            $this->tenantContext->setTenant(tenant: $user->getTenant());
        }
    }
}
