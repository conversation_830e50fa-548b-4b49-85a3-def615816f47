<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\EventSubscriber;

use App\Domain\Context\TenantContext;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Security\Http\Event\LoginSuccessEvent;

#[AsEventListener(priority: 50)]
readonly class CheckTenantContextOnLogin
{
    public function __construct(
        private LoggerInterface $logger,
        private TenantContext $tenantContext,
    ) {
    }

    public function __invoke(LoginSuccessEvent $event): void
    {
        $requestPath = $event->getRequest()->getPathInfo();
        $user = $event->getUser();

        if (!str_starts_with(haystack: $requestPath, needle: '/api/portal/')) {
            return;
        }

        try {
            $this->tenantContext->getTenant();
        } catch (\RuntimeException) {
            $this->logger->critical(
                'Cannot resolve tenant for authenticated user.',
                [
                    'username' => $user->getUserIdentifier(),
                    'class' => $user::class,
                    'requestPath' => $requestPath,
                ],
            );
            throw new UnauthorizedHttpException(challenge: 'can not login user with no tenant');
        }
    }
}
