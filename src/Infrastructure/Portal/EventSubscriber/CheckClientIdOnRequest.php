<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\EventSubscriber;

use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Constraints\Uuid;
use Symfony\Component\Validator\Validation;

#[AsEventListener(event: RequestEvent::class, priority: 10)]
readonly class CheckClientIdOnRequest
{
    public function __invoke(RequestEvent $event): void
    {
        $request = $event->getRequest();
        $requestPath = $request->getPathInfo();

        if (!str_starts_with(haystack: $requestPath, needle: '/api/portal/')) {
            return;
        }
        if (str_contains(haystack: $requestPath, needle: '/business-partner/option') || str_contains(haystack: $requestPath, needle: '/translation/locale')) {
            return;
        }

        $clientId = $request->headers->get(key: 'x-current-user-context-id');

        if (null === $clientId) {
            throw new BadRequestHttpException(message: 'x-current-user-context-id header is mandatory');
        }

        $validator = Validation::createValidator();
        $violations = $validator->validate($clientId, new Uuid());

        if (count(value: $violations) > 0) {
            throw new BadRequestHttpException(message: 'x-current-user-context-id is not a valid uuid');
        }
    }
}
