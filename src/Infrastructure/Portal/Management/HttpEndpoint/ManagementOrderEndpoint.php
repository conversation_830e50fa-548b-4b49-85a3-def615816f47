<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\HttpEndpoint;

use App\Domain\Entity\BusinessPartner;
use App\Domain\Entity\ContractPartner;
use App\Domain\Entity\Enum\OrderStatus;
use App\Domain\Entity\Order as OrderEntity;
use App\Domain\Entity\OrderActivity;
use App\Domain\Entity\Service;
use App\Domain\Entity\ServiceLocation;
use App\Domain\Repository\ContractPartnerRepository;
use App\Domain\Repository\OrderRepository;
use App\Domain\Repository\ServiceLocationRepository;
use App\Domain\Service\OrderService;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Management\Resource\Dto\Activity;
use App\Infrastructure\Portal\Management\Resource\ManagementOrder;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class ManagementOrderEndpoint
{
    public function __construct(
        private ContractPartnerRepository $contractPartnerRepository,
        private LoggerInterface $logger,
        private OrderRepository $orderRepository,
        private OrderService $orderService,
        private ServiceLocationRepository $serviceLocationRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
    ) {
    }

    public function get(string $id): ManagementOrder
    {
        /** @var OrderEntity $entity */
        $entity = $this->orderRepository->find(id: $id);
        $businessPartner = $this->orderService->getBusinessPartner(order: $entity);
        $frontendStatus = OrderStatus::from(value: $entity->getOrderStatus())->getFrontendStatus();

        $order = new ManagementOrder();
        $order->id = $id;
        $order->serviceId = $entity->getExtServiceId();
        $order->orderDate = $entity->getOrderDate()->format(format: 'Y-m-d');
        $order->status = $frontendStatus;
        $order->statusText = sprintf('{{%s}}', $frontendStatus);
        $order->serviceLocation = ''; // TODO

        $service = $this->orderService->getService(order: $entity);
        if (null !== $service) {
            $contractPartner = $this->getContractPartner(service: $service, businessPartner: $businessPartner, order: $entity);

            $order->serviceLocationId = $service->getExtServiceLocationId();
            $order->contractNumber = $service->getExtContractId();
            $order->customerNumber = $contractPartner->getExtId();
        } else {
            $order->serviceLocationId = null;
            $order->contractNumber = null;
            $order->customerNumber = null;
        }

        /** @var array<int, Activity> $activities */
        $activities = array_map(
            callback: fn (OrderActivity $activity): Activity => $this->mapActivityToDto(orderActivity: $activity),
            array: $entity->getActivities()->toArray(),
        );

        $order->activities = $activities;

        return $order;
    }

    /**
     * @return PaginatedCollection<ManagementOrder>
     *
     * @throws QueryException
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $paginator = $this->orderRepository->findOrderOverviewByUserSearchCriteria(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartnerId,
        );

        $items = [];

        foreach ($paginator->getIterator() as $managementOrder) {
            try {
                $items[] = $this->mapToDto(order: $managementOrder);
            } catch (NotFoundException $exception) {
                $this->logger->error($exception->getMessage(), ['exception' => $exception]);
            }
        }

        return new PaginatedCollection(
            totalItems: count(value: $items),
            items: $items,
        );
    }

    /**
     * @throws NotFoundException
     */
    private function mapToDto(OrderEntity $order): ManagementOrder
    {
        $businessPartner = $this->orderService->getBusinessPartner(order: $order);
        $frontendStatus = OrderStatus::from(value: $order->getOrderStatus())->getFrontendStatus();

        $managementOrder = new ManagementOrder();
        $managementOrder->id = $order->getId();
        $managementOrder->serviceId = ''; // TODO
        $managementOrder->orderDate = $order->getOrderDate()->format(format: 'Y-m-d');
        $managementOrder->email = $order->getEmail() ?? null;
        $managementOrder->serviceLocationId = ''; // TODO
        $managementOrder->status = $order->getOrderStatus();
        $managementOrder->statusText = sprintf('{{%s}}', $frontendStatus);

        $service = $this->orderService->getService(order: $order);
        if (null !== $service) {
            $serviceLocation = $this->serviceLocationRepository->findOneByExtIdAndExtBusinessPartnerId(
                extId: $service->getExtServiceLocationId(),
                extBusinessPartnerId: $businessPartner->getExtId(),
            );

            $contractPartner = $this->getContractPartner(service: $service, businessPartner: $businessPartner, order: $order);

            $managementOrder->serviceType = $service->getServiceType();
            $managementOrder->contractNumber = $service->getExtContractId();
            $managementOrder->customerNumber = $contractPartner->getExtId();
            $managementOrder->serviceLocation = $this->getAddressWithAdditionalInformation(serviceLocation: $serviceLocation);
        } else {
            $managementOrder->serviceType = null;
            $managementOrder->contractNumber = null;
            $managementOrder->customerNumber = null;
            $managementOrder->serviceLocation = null;
        }

        return $managementOrder;
    }

    private function getAddressWithAdditionalInformation(?ServiceLocation $serviceLocation): string
    {
        if (null === $serviceLocation) {
            return '';
        }

        if (null === $serviceLocation->getAdditionalInformation()) {
            return $serviceLocation->getAddress();
        }

        return sprintf('%s - %s', $serviceLocation->getAddress(), $serviceLocation->getAdditionalInformation());
    }

    private function getContractPartner(
        Service $service,
        BusinessPartner $businessPartner,
        OrderEntity $order,
    ): ContractPartner {
        $contractPartner = $this->contractPartnerRepository->findOneByExtContractIdBusinessPartnerAndRole(
            extContractId: $service->getExtContractId(),
            businessPartnerId: $businessPartner->getId(),
        );

        if (null === $contractPartner) {
            throw new NotFoundHttpException(message: 'No contract partner found for order: '.$order->getId());
        }

        return $contractPartner;
    }

    private function mapActivityToDto(OrderActivity $orderActivity): Activity
    {
        $activity = new Activity();
        $activity->id = $orderActivity->getId();
        $activity->type = $orderActivity->getType();
        $activity->description = $orderActivity->getDescription();
        $activity->name = $orderActivity->getName();
        $activity->createdAt = $orderActivity->getCreatedAt()->format(format: 'Y-m-d H:i:s');

        return $activity;
    }
}
