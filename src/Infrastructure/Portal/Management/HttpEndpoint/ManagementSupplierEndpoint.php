<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\HttpEndpoint;

use App\Domain\Entity\Enum\OrderDelay;
use App\Domain\Entity\OrderConfirmation as OrderConfirmationEntity;
use App\Domain\Entity\Supplier;
use App\Domain\Entity\Supplier as SupplierEntity;
use App\Domain\MessageQueue\Supplier\SupplierCreated;
use App\Domain\Repository\SupplierRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\Dto\OrderConfirmation;
use App\Infrastructure\Portal\Management\Resource\ManagementSupplier;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsController]
readonly class ManagementSupplierEndpoint
{
    public function __construct(
        private LoggerInterface $logger,
        private MessageBusInterface $bus,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private SupplierRepository $supplierRepository,
    ) {
    }

    /**
     * @return PaginatedCollection<ManagementSupplier>
     *
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $paginator = $this->supplierRepository->findByUserSearchCriteria(userSearchCriteria: $userSearchCriteria);

        $items = array_map(
            callback: fn (SupplierEntity $supplier): ManagementSupplier => $this->mapToDto(supplier: $supplier),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    public function get(string $id): ManagementSupplier
    {
        $entity = $this->supplierRepository->find(id: $id);

        if (null === $entity) {
            throw new NotFoundException(message: "Supplier $id not found!");
        }

        return $this->mapToDto(supplier: $entity);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function create(ManagementSupplier $supplier): ManagementSupplier
    {
        $entity = new SupplierEntity();
        $entity->setRegion(region: $supplier->region);
        $entity->setLocation(location: $supplier->location);
        $entity->setDescription(description: $supplier->description);
        $entity->setNumber(number: $supplier->number);
        $entity->setName(name: $supplier->name);
        $entity->setVkorg(vkorg: $supplier->vkorg);

        if (isset($supplier->orderDelay)) {
            if (!OrderDelay::tryFrom(value: $supplier->orderDelay)) {
                throw new NotFoundException(message: "Supplier order delay $supplier->orderDelay not found!");
            }
            $entity->setOrderDelay(orderDelay: $supplier->orderDelay);
        }

        $entity->setCity(city: $supplier->city);
        $entity->setPostalCode(postalCode: $supplier->postalCode);
        $entity->setCountry(country: $supplier->country);
        $entity->setStreet(street: $supplier->street);
        $entity->setHouseNumber(houseNumber: $supplier->houseNumber);
        $entity->setEmail(email: $supplier->email);
        $entity->setPhone(phone: $supplier->phone);
        $entity->setOpeningHours(openingHours: $supplier->openingHours);
        $this->resolveOrderDelayDays(supplier: $supplier, entity: $entity);

        foreach ($supplier->orderConfirmation ?? [] as $orderConfirmation) {
            $orderConfirmationEntity = new OrderConfirmationEntity();
            $orderConfirmationEntity->setSupplier(supplier: $entity);
            $orderConfirmationEntity->setName(name: $orderConfirmation->name);
            $orderConfirmationEntity->setEmail(email: $orderConfirmation->email);

            $entity->addOrderConfirmation(orderConfirmation: $orderConfirmationEntity);
        }

        $this->supplierRepository->add(entity: $entity);

        $this->dispatchSupplierCreated(entity: $entity);

        return $this->mapToDto(supplier: $entity);
    }

    public function update(ManagementSupplier $supplier, string $id): ManagementSupplier
    {
        $entity = $this->supplierRepository->find(id: $id);

        if (null === $entity) {
            throw new NotFoundException(message: "Supplier $id not found");
        }

        if (isset($supplier->description)) {
            $entity->setDescription(description: $supplier->description);
        }
        if (isset($supplier->name)) {
            $entity->setName(name: $supplier->name);
        }
        if (isset($supplier->vkorg)) {
            $entity->setVkorg(vkorg: $supplier->vkorg);
        }
        if (isset($supplier->orderDelay)) {
            if (!OrderDelay::tryFrom(value: $supplier->orderDelay)) {
                throw new NotFoundException(message: "Supplier order delay $supplier->orderDelay not found!");
            }
            $entity->setOrderDelay(orderDelay: $supplier->orderDelay);
        }
        if (isset($supplier->street)) {
            $entity->setStreet(street: $supplier->street);
        }
        if (isset($supplier->houseNumber)) {
            $entity->setHouseNumber(houseNumber: $supplier->houseNumber);
        }
        if (isset($supplier->city)) {
            $entity->setCity(city: $supplier->city);
        }
        if (isset($supplier->postalCode)) {
            $entity->setPostalCode(postalCode: $supplier->postalCode);
        }
        if (isset($supplier->country)) {
            $entity->setCountry(country: $supplier->country);
        }
        if (isset($supplier->email)) {
            $entity->setEmail(email: $supplier->email);
        }
        if (isset($supplier->phone)) {
            $entity->setPhone(phone: $supplier->phone);
        }
        if (isset($supplier->openingHours)) {
            $entity->setOpeningHours(openingHours: $supplier->openingHours);
        }
        $this->resolveOrderDelayDays(supplier: $supplier, entity: $entity);

        if (isset($supplier->orderConfirmation)) {
            $this->updateOrderConfirmation(supplier: $supplier, entity: $entity);
        }

        $this->supplierRepository->add(entity: $entity);

        return $this->mapToDto(supplier: $entity);
    }

    /**
     * @return Collection<ManagementSupplier>
     *
     * @throws \Exception
     */
    public function getOptionsDelay(): Collection
    {
        $items = [];

        foreach (OrderDelay::cases() as $case) {
            $dto = new ManagementSupplier();
            $dto->id = $case->value;
            $dto->display = $case->toInterval()->format(format: '%h h');
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }

    /**
     * @return Collection<ManagementSupplier>
     *
     * @throws \Exception
     */
    public function getOptionsDailyDelay(): Collection
    {
        $startTime = new \DateTimeImmutable(datetime: '00:00');
        $endTime = new \DateTimeImmutable(datetime: '24:00');

        $items = [];
        while ($startTime < $endTime) {
            $dto = new ManagementSupplier();
            $dto->id = $startTime->format(format: 'Hi');
            $dto->display = $startTime->format(format: 'H:i');
            $items[] = $dto;

            $startTime = $startTime->add(interval: new \DateInterval(duration: 'PT30M'));
        }

        return new Collection(
            items: $items,
        );
    }

    private function validateOrderDelayTime(?string $time): void
    {
        if (!isset($time)) {
            return;
        }

        if (!\DateTimeImmutable::createFromFormat(format: 'H:i', datetime: $time)) {
            throw new \InvalidArgumentException(message: "Order delay $time must have 'H:i: format!");
        }
    }

    private function resolveOrderDelayDays(ManagementSupplier $supplier, SupplierEntity $entity): void
    {
        if (isset($supplier->orderDelayMonday)) {
            $this->validateOrderDelayTime(time: $supplier->orderDelayMonday);
            $entity->setOrderDelayMonday(orderDelayMonday: $supplier->orderDelayMonday);
        } else {
            $entity->setOrderDelayMonday(orderDelayMonday: null);
        }

        if (isset($supplier->orderDelayTuesday)) {
            $this->validateOrderDelayTime(time: $supplier->orderDelayTuesday);
            $entity->setOrderDelayTuesday(orderDelayTuesday: $supplier->orderDelayTuesday);
        } else {
            $entity->setOrderDelayTuesday(orderDelayTuesday: null);
        }

        if (isset($supplier->orderDelayWednesday)) {
            $this->validateOrderDelayTime(time: $supplier->orderDelayWednesday);
            $entity->setOrderDelayWednesday(orderDelayWednesday: $supplier->orderDelayWednesday);
        } else {
            $entity->setOrderDelayWednesday(orderDelayWednesday: null);
        }

        if (isset($supplier->orderDelayThursday)) {
            $this->validateOrderDelayTime(time: $supplier->orderDelayTuesday);
            $entity->setOrderDelayThursday(orderDelayThursday: $supplier->orderDelayThursday);
        } else {
            $entity->setOrderDelayThursday(orderDelayThursday: null);
        }

        if (isset($supplier->orderDelayFriday)) {
            $this->validateOrderDelayTime(time: $supplier->orderDelayFriday);
            $entity->setOrderDelayFriday(orderDelayFriday: $supplier->orderDelayFriday);
        } else {
            $entity->setOrderDelayFriday(orderDelayFriday: null);
        }
    }

    private function mapToDto(SupplierEntity $supplier): ManagementSupplier
    {
        $orderDelay = OrderDelay::tryFrom(value: (string) $supplier->getOrderDelay());
        $dto = new ManagementSupplier();
        $dto->id = $supplier->getId();
        $dto->region = $supplier->getRegion();
        $dto->location = $supplier->getLocation();
        $dto->description = $supplier->getDescription();
        $dto->number = $supplier->getNumber();
        $dto->name = $supplier->getName();
        $dto->vkorg = $supplier->getVkorg();
        $dto->orderDelay = $orderDelay?->toInterval()->format(format: '%h h') ?? '';
        $dto->street = $supplier->getStreet();
        $dto->houseNumber = $supplier->getHouseNumber();
        $dto->city = $supplier->getCity();
        $dto->postalCode = $supplier->getPostalCode();
        $dto->country = $supplier->getCountry();
        $dto->email = $supplier->getEmail();
        $dto->phone = $supplier->getPhone();
        $dto->openingHours = $supplier->getOpeningHours();
        $dto->orderDelayMonday = $supplier->getOrderDelayMonday() ?? '';
        $dto->orderDelayTuesday = $supplier->getOrderDelayTuesday() ?? '';
        $dto->orderDelayWednesday = $supplier->getOrderDelayWednesday() ?? '';
        $dto->orderDelayThursday = $supplier->getOrderDelayThursday() ?? '';
        $dto->orderDelayFriday = $supplier->getOrderDelayFriday() ?? '';

        $dto->orderConfirmation = $this->mapToOrderConfirmation(supplierEntity: $supplier);

        return $dto;
    }

    /**
     * @return OrderConfirmation[]
     */
    private function mapToOrderConfirmation(SupplierEntity $supplierEntity): array
    {
        /* @var $orderConfirmations array<OrderConfirmation> */
        $orderConfirmations = [];

        foreach ($supplierEntity->getOrderConfirmations() as $orderConfirmationEntity) {
            $orderConfirmation = new OrderConfirmation();
            $orderConfirmation->id = $orderConfirmationEntity->getId();
            $orderConfirmation->email = $orderConfirmationEntity->getEmail();
            $orderConfirmation->name = $orderConfirmationEntity->getName();

            $orderConfirmations[] = $orderConfirmation;
        }

        return $orderConfirmations;
    }

    private function dispatchSupplierCreated(SupplierEntity $entity): void
    {
        try {
            $this->bus->dispatch(new SupplierCreated(id: $entity->getId(), tenant: $entity->getTenant()));
        } catch (ExceptionInterface $exception) {
            $this->logger->error(
                message: 'Something went wrong while trying to get a state for service location',
                context: ['exception' => $exception],
            );
        }
    }

    private function updateOrderConfirmation(ManagementSupplier $supplier, Supplier $entity): void
    {
        $requestOrderConfirmations = $supplier->orderConfirmation ?? [];

        if ([] === $requestOrderConfirmations) {
            $entity->clearOrderConfirmations();

            return;
        }

        /** @var array<int, OrderConfirmation> $existingOrderConfirmations */
        $existingOrderConfirmations = array_filter(
            array: $supplier->orderConfirmation,
            callback: fn (OrderConfirmation $orderConfirmation): bool => isset($orderConfirmation->id),
        );

        $this->updateExistingOrderCreations(existingOrderConfirmations: $existingOrderConfirmations, entity: $entity);

        /** @var array<int, OrderConfirmation> $newOrderConfirmations */
        $newOrderConfirmations = array_filter(
            array: $supplier->orderConfirmation,
            callback: fn (OrderConfirmation $orderConfirmation): bool => !isset($orderConfirmation->id),
        );

        $this->addNewOrderConfirmations(newOrderConfirmations: $newOrderConfirmations, entity: $entity);
    }

    /**
     * @param array<int, OrderConfirmation> $existingOrderConfirmations
     */
    private function updateExistingOrderCreations(array $existingOrderConfirmations, SupplierEntity $entity): void
    {
        foreach ($entity->getOrderConfirmations()->toArray() as $orderConfirmation) {
            /** @var ?OrderConfirmation $matchingDto */
            $matchingDto = array_find(
                array: $existingOrderConfirmations,
                callback: fn (OrderConfirmation $dto): bool => $dto->id === $orderConfirmation->getId()
            );

            if (null === $matchingDto) {
                $entity->removeOrderConfirmation(orderConfirmation: $orderConfirmation);

                continue;
            }

            $orderConfirmation->setName(name: $matchingDto->name);
            $orderConfirmation->setEmail(email: $matchingDto->email);
        }
    }

    /**
     * @param array<int, OrderConfirmation> $newOrderConfirmations
     */
    private function addNewOrderConfirmations(array $newOrderConfirmations, SupplierEntity $entity): void
    {
        foreach ($newOrderConfirmations as $newOrderConfirmation) {
            $orderConfirmation = new OrderConfirmationEntity();
            $orderConfirmation->setSupplier(supplier: $entity);
            $orderConfirmation->setName(name: $newOrderConfirmation->name);
            $orderConfirmation->setEmail(email: $newOrderConfirmation->email);

            $entity->addOrderConfirmation(orderConfirmation: $orderConfirmation);
        }
    }
}
