<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\HttpEndpoint;

use App\Domain\Entity\Enum\Permission;
use App\Domain\Entity\User as UserEntity;
use App\Domain\Repository\UserRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Management\Resource\ManagementUser;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ManagementUserEndpoint
{
    public function __construct(
        private UserRepository $userRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
    ) {
    }

    public function get(string $id): ManagementUser
    {
        $entity = $this->userRepository->find(id: $id);

        if (null === $entity) {
            throw new NotFoundException();
        }

        $dto = new ManagementUser();
        $dto->id = $id;
        $dto->email = $entity->getUserIdentifier();
        $dto->firstname = $entity->getLastname();
        $dto->lastname = $entity->getLastname();

        foreach ($entity->getPermissions() as $permission) {
            $dto->permissions[] = $permission->value;
        }

        return $dto;
    }

    /**
     * @return PaginatedCollection<ManagementUser>
     *
     * @throws QueryException
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $paginator = $this->userRepository->findByUserSearchCriteriaForManagement(
            userSearchCriteria: $userSearchCriteria,
        );

        $items = array_map(
            callback: fn (UserEntity $entity): ManagementUser => $this->mapCollectionToDto(entity: $entity),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    /**
     * @return Collection<ManagementUser>
     */
    public function getPermissionOptions(): Collection
    {
        $permissions = [];

        foreach (Permission::cases() as $permission) {
            $userPermission = new ManagementUser();
            $userPermission->id = $permission->value;
            $userPermission->display = sprintf('{{%s}}', $permission->value);
            $userPermission->tooltip = sprintf('{{tooltip_%s}}', $userPermission->display);
            $permissions[] = $userPermission;
        }

        return new Collection(
            items: $permissions,
        );
    }

    public function create(ManagementUser $user): ManagementUser
    {
        $entity = new UserEntity();
        $entity->setUsername(username: $user->email);
        $entity->setFirstName(firstname: $user->firstname);
        $entity->setLastName(lastname: $user->lastname);

        foreach ($user->permissions as $permissionValue) {
            $permission = Permission::tryFrom(value: $permissionValue);

            if (null === $permission) {
                throw new NotFoundException(message: 'Permission '.$permissionValue.' not found!');
            }

            $entity->addPermission(permission: $permission);
        }

        $this->userRepository->add(entity: $entity);

        return $user;
    }

    public function update(string $id, ManagementUser $user): ManagementUser
    {
        $entity = $this->userRepository->find(id: $id);

        if (null === $entity) {
            throw new NotFoundException(message: 'User '.$id.' not found!');
        }

        $entity->setUsername(username: $user->email);
        $entity->setFirstname(firstname: $user->firstname);
        $entity->setLastname(lastname: $user->lastname);

        $entity->clearPermissions();

        foreach ($user->permissions as $permissionValue) {
            $permission = Permission::tryFrom(value: $permissionValue);

            if (null === $permission) {
                throw new NotFoundException(message: 'Permission '.$permissionValue.' not found!');
            }

            $entity->addPermission(permission: $permission);
        }

        $this->userRepository->add(entity: $entity);

        return $user;
    }

    public function delete(string $id): void
    {
        $entity = $this->userRepository->find(id: $id);

        if (null === $entity) {
            throw new NotFoundException(message: 'User '.$id.' not found!');
        }

        $entity->clearGroups();
        $entity->clearBusinessPartners();

        $this->userRepository->remove(entity: $entity);
    }

    private function mapCollectionToDto(UserEntity $entity): ManagementUser
    {
        $permissions = array_map(
            callback: fn (Permission $permission): string => $permission->value,
            array: $entity->getPermissions(),
        );

        $dto = new ManagementUser();
        $dto->id = $entity->getId();
        $dto->email = $entity->getUserIdentifier();
        $dto->name = $entity->getName();
        $dto->permissionsText = implode(separator: ', ', array: $permissions);

        return $dto;
    }
}
