<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\HttpEndpoint;

use App\Domain\Entity\ServiceProduct as ServiceProductEntity;
use App\Domain\Repository\ServiceProductRepository;
use App\Infrastructure\Portal\Management\Resource\ManagementServiceProduct;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class ManagementServiceProductEndpoint
{
    public function __construct(
        private ServiceProductRepository $serviceProductRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
    ) {
    }

    /**
     * @return PaginatedCollection<ManagementServiceProduct>
     *
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $paginator = $this->serviceProductRepository->findByUserSearchCriteria(userSearchCriteria: $userSearchCriteria);

        $items = array_map(
            callback: fn (ServiceProductEntity $serviceProduct): ManagementServiceProduct => $this->mapToDto(serviceProduct: $serviceProduct),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    public function create(ManagementServiceProduct $serviceProduct): ManagementServiceProduct
    {
        $entity = new ServiceProductEntity();
        $entity->setServiceProduct(serviceProduct: $serviceProduct->serviceProduct);
        $entity->setServiceType(serviceType: $serviceProduct->serviceType);
        $entity->setContainer(container: $serviceProduct->container);

        $this->serviceProductRepository->add(entity: $entity);

        return $this->mapToDto(serviceProduct: $entity);
    }

    public function update(ManagementServiceProduct $serviceProduct, string $id): ManagementServiceProduct
    {
        $entity = $this->serviceProductRepository->find(id: $id);

        if (null === $entity) {
            throw new NotFoundHttpException();
        }

        $entity->setServiceProduct(serviceProduct: $serviceProduct->serviceProduct);
        $entity->setServiceType(serviceType: $serviceProduct->serviceType);
        $entity->setContainer(container: $serviceProduct->container);

        $this->serviceProductRepository->add(entity: $entity);

        return $this->mapToDto(serviceProduct: $entity);
    }

    public function delete(string $id): void
    {
        $entity = $this->serviceProductRepository->find(id: $id);

        if (null === $entity) {
            throw new NotFoundHttpException();
        }

        $this->serviceProductRepository->remove(entity: $entity);
    }

    /**
     * @return Collection<ManagementServiceProduct>
     *
     * @throws \Exception
     */
    public function getOptions(): Collection
    {
        $serviceProducts = $this->serviceProductRepository->findAll();

        $serviceTypes = [];
        $items = [];
        foreach ($serviceProducts as $serviceProduct) {
            $serviceType = $serviceProduct->getServiceType();

            if (!in_array(needle: $serviceType, haystack: $serviceTypes)) {
                $serviceTypes[] = $serviceType;

                $dto = new ManagementServiceProduct();
                $dto->id = $serviceType;
                $dto->display = $serviceType;
                $items[] = $dto;
            }
        }
        sort(array: $items);

        return new Collection(
            items: $items,
        );
    }

    private function mapToDto(ServiceProductEntity $serviceProduct): ManagementServiceProduct
    {
        $dto = new ManagementServiceProduct();
        $dto->id = $serviceProduct->getId();
        $dto->serviceProduct = $serviceProduct->getServiceProduct();
        $dto->container = $serviceProduct->getContainer();
        $dto->serviceType = $serviceProduct->getServiceType();

        return $dto;
    }
}
