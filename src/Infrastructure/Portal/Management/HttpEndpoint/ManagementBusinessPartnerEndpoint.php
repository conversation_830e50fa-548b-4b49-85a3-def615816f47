<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\HttpEndpoint;

use App\Domain\Entity\BusinessPartner as BusinessPartnerEntity;
use App\Domain\Entity\Enum\Permission;
use App\Domain\Entity\Group as GroupEntity;
use App\Domain\Entity\User as UserEntity;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\GroupRepository;
use App\Domain\Repository\ServiceLocationRepository;
use App\Domain\Repository\UserRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\Dto\GroupSelected;
use App\Infrastructure\Portal\Management\Resource\ManagementBusinessPartner;
use App\Infrastructure\Portal\Management\Service\ManagementBusinessPartnerService;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ManagementBusinessPartnerEndpoint
{
    public function __construct(
        private BusinessPartnerRepository $businessPartnerRepository,
        private GroupRepository $groupRepository,
        private ManagementBusinessPartnerService $managementBusinessPartnerService,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private ServiceLocationRepository $serviceLocationRepository,
        private UserRepository $userRepository,
    ) {
    }

    /**
     * @return Collection<ManagementBusinessPartner>
     *
     * @throws \Exception
     */
    public function getCollection(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $paginator = $this->businessPartnerRepository->findByUserSearchCriteriaForManagement(
            userSearchCriteria: $userSearchCriteria,
        );

        $items = array_map(
            callback: fn (BusinessPartnerEntity $entity): ManagementBusinessPartner
                => $this->mapCollectionToDto(entity: $entity),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    public function get(string $id): ManagementBusinessPartner
    {
        $businessPartner = $this->businessPartnerRepository->find(id: $id);

        if (null === $businessPartner) {
            throw new NotFoundException(message: 'Business Partner '.$id.' not found.');
        }

        $resource = new ManagementBusinessPartner();
        $resource->id = $id;
        $resource->number = $businessPartner->getExtId();
        $resource->name = $businessPartner->getName();
        $resource->userCount = (string) $businessPartner->getUsers()->count();
        $resource->groupCount = (string) $businessPartner->getGroups()->count();
        $resource->lastUpdate = $businessPartner->getModifiedAt()->format(format: 'd.m.Y H:i');

        return $resource;
    }

    /**
     * @return Collection<ManagementBusinessPartner>
     *
     * @throws QueryException
     */
    public function getUserCollection(Request $request, string $id): Collection
    {
        $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $users = $this->userRepository->findByUserSearchCriteriaAndBusinessPartnerIdForPortal(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $id,
            searchInUser: true,
        );

        $items = [];
        foreach ($users as $user) {
            $dto = new ManagementBusinessPartner();
            $dto->id = $user->getId();
            $dto->email = $user->getUsername();

            $items[] = $dto;
        }

        return new PaginatedCollection(
            totalItems: $users->count(),
            items: $items,
        );
    }

    public function deleteUser(string $id, string $userId): void
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $user = $this->managementBusinessPartnerService->getUser(userId: $userId, businessPartner: $businessPartner);

        $user->clearBusinessPartners();
        $user->clearGroups();
        $this->userRepository->remove(entity: $user);
    }

    public function getUserItem(string $id, string $userId): ManagementBusinessPartner
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $user = $this->managementBusinessPartnerService->getUser(userId: $userId, businessPartner: $businessPartner);

        $dto = new ManagementBusinessPartner();
        $dto->id = $user->getId();
        $dto->email = $user->getUsername();
        $dto->firstname = $user->getFirstname() ?? '';
        $dto->lastname = $user->getLastname() ?? '';

        $dto->permissions = array_map(
            callback: fn (Permission $permission): string => $permission->value,
            array: $user->getPermissions(),
        );

        $selectedGroups = [];
        foreach ($user->getGroups() as $group) {
            $selectedGroup = new GroupSelected();
            $selectedGroup->id = $group->getId();

            $selectedGroups[] = $selectedGroup;
        }
        $dto->groups = $selectedGroups;

        return $dto;
    }

    public function updateUser(ManagementBusinessPartner $managementBusinessPartner, string $id, string $userId): ManagementBusinessPartner
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $user = $this->managementBusinessPartnerService->getUser(userId: $userId, businessPartner: $businessPartner);

        $user->setUsername(username: $managementBusinessPartner->email);
        $user->setFirstname(firstname: $managementBusinessPartner->firstname);
        $user->setLastname(lastname: $managementBusinessPartner->lastname);

        $user->clearPermissions();
        foreach ($managementBusinessPartner->permissions as $permissionValue) {
            $permission = Permission::tryFrom(value: $permissionValue);

            if (null !== $permission) {
                $user->addPermission(permission: $permission);
            }
        }

        $user->clearGroups();
        foreach ($this->managementBusinessPartnerService->findGroups(managementBusinessPartner: $managementBusinessPartner, businessPartner: $businessPartner) as $group) {
            $user->addGroup(group: $group);
        }

        $this->userRepository->add(entity: $user);

        return $managementBusinessPartner;
    }

    public function createUser(ManagementBusinessPartner $managementBusinessPartner, string $id): ManagementBusinessPartner
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);

        $user = new UserEntity();
        $user->setUsername(username: $managementBusinessPartner->email);
        $user->setFirstname(firstname: $managementBusinessPartner->firstname);
        $user->setLastname(lastname: $managementBusinessPartner->lastname);
        $user->addBusinessPartner(businessPartner: $businessPartner);

        foreach ($managementBusinessPartner->permissions as $permissionValue) {
            $permission = Permission::tryFrom(value: $permissionValue);

            if (null !== $permission) {
                $user->addPermission(permission: $permission);
            }
        }

        foreach ($this->managementBusinessPartnerService->findGroups(managementBusinessPartner: $managementBusinessPartner, businessPartner: $businessPartner) as $group) {
            $user->addGroup(group: $group);
        }

        $this->userRepository->add(entity: $user);

        return $managementBusinessPartner;
    }

    /**
     * @return Collection<ManagementBusinessPartner>
     */
    public function getPermissionOptions(): Collection
    {
        $permissions = [];

        foreach (Permission::cases() as $permission) {
            $userPermission = new ManagementBusinessPartner();
            $userPermission->id = $permission->value;
            $userPermission->display = sprintf('{{%s}}', $permission->value);
            $userPermission->tooltip = sprintf('{{tooltip_%s}}', $userPermission->display);
            $permissions[] = $userPermission;
        }

        return new Collection(
            items: $permissions,
        );
    }

    /**
     * @return Collection<ManagementBusinessPartner>
     */
    public function getGroupOptions(string $id): Collection
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);

        $groups = $this->groupRepository->findAllByBusinessPartner(businessPartner: $businessPartner);

        $resources = [];

        foreach ($groups as $group) {
            $resource = new ManagementBusinessPartner();
            $resource->id = $group->getId();
            $resource->display = $group->getName();
            $resources[] = $resource;
        }

        return new Collection(
            items: $resources,
        );
    }

    /**
     * @return Collection<ManagementBusinessPartner>
     */
    public function getGroupCollection(string $id): Collection
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $groups = $this->groupRepository->findAllByBusinessPartner(businessPartner: $businessPartner);

        $items = [];
        foreach ($groups as $group) {
            $dto = new ManagementBusinessPartner();
            $dto->id = $group->getId();
            $dto->name = $group->getName();
            $dto->userCount = (string) $group->getUsers()->count();

            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }

    /**
     * @return Collection<ManagementBusinessPartner>
     */
    public function getServiceLocationsCollection(string $id): Collection
    {
        $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $serviceLocations = $this->serviceLocationRepository->findAllByBusinessPartnerAndContract(businessPartnerId: $id);

        $items = [];
        foreach ($serviceLocations as $serviceLocation) {
            $dto = new ManagementBusinessPartner();
            $dto->id = $serviceLocation->getId();
            $dto->name = $serviceLocation->getStreet().' '.$serviceLocation->getHouseNumber();
            $dto->address = $serviceLocation->getPostalCode().', '.$serviceLocation->getCity();

            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }

    public function getGroupItem(string $id, string $groupId): ManagementBusinessPartner
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $group = $this->managementBusinessPartnerService->getGroup(groupId: $groupId, businessPartner: $businessPartner);

        $resource = new ManagementBusinessPartner();
        $resource->id = $group->getId();
        $resource->name = $group->getName();

        return $resource;
    }

    public function createGroup(ManagementBusinessPartner $managementBusinessPartner, string $id): ManagementBusinessPartner
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $entity = new GroupEntity();

        $entity->setName(name: $managementBusinessPartner->name);
        $entity->setBusinessPartner(businessPartner: $businessPartner);

        foreach ($managementBusinessPartner->selectedServiceLocations as $selectedServiceLocation) {
            $serviceLocation = $this->serviceLocationRepository->find(id: $selectedServiceLocation->id);

            if (null === $serviceLocation) {
                continue;
            }

            $entity->addServiceLocation(serviceLocation: $serviceLocation);
        }

        $this->groupRepository->add(group: $entity);

        return $managementBusinessPartner;
    }

    public function deleteGroup(string $id, string $groupId): void
    {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $group = $this->managementBusinessPartnerService->getGroup(groupId: $groupId, businessPartner: $businessPartner);

        $group->clearUsers();
        $group->clearServiceLocations();
        $this->groupRepository->remove(group: $group);
    }

    public function updateGroup(
        ManagementBusinessPartner $managementBusinessPartner,
        string $id,
        string $groupId,
    ): ManagementBusinessPartner {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $group = $this->managementBusinessPartnerService->getGroup(groupId: $groupId, businessPartner: $businessPartner);

        if (isset($managementBusinessPartner->name)) {
            $group->setName(name: $managementBusinessPartner->name);
        }

        $group->clearServiceLocations();
        if (isset($managementBusinessPartner->selectedServiceLocations)) {
            foreach ($managementBusinessPartner->selectedServiceLocations as $selectedServiceLocation) {
                $serviceLocation = $this->serviceLocationRepository->find(id: $selectedServiceLocation->id);

                if (null === $serviceLocation) {
                    continue;
                }

                $group->addServiceLocation(serviceLocation: $serviceLocation);
            }
        }

        $this->groupRepository->add(group: $group);

        return $managementBusinessPartner;
    }

    public function updateGroupUsers(
        ManagementBusinessPartner $managementBusinessPartner,
        string $id,
        string $groupId,
    ): ManagementBusinessPartner {
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $group = $this->managementBusinessPartnerService->getGroup(groupId: $groupId, businessPartner: $businessPartner);

        $group->clearUsers();
        if (isset($managementBusinessPartner->selectedUsers)) {
            foreach ($managementBusinessPartner->selectedUsers as $selectedUser) {
                $user = $this->userRepository->find(id: $selectedUser->id);

                if (null === $user) {
                    continue;
                }

                $group->addUser(user: $user);
            }
        }

        $this->groupRepository->add(group: $group);

        return $managementBusinessPartner;
    }

    /**
     * @return PaginatedCollection<ManagementBusinessPartner>
     *
     * @throws \Exception
     */
    public function getGroupServiceLocations(Request $request, string $id): PaginatedCollection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);

        /** @var string $groupId */
        $groupId = $userSearchCriteria->filters['groupId']->value ?? '';
        $group = $this->managementBusinessPartnerService->getGroup(groupId: $groupId, businessPartner: $businessPartner);

        $serviceLocations = $this->serviceLocationRepository->findAllByUserSearchCriteriaAndContractAndBusinessPartner(userSearchCriteria: $userSearchCriteria, businessPartnerId: $id);

        $items = [];
        foreach ($serviceLocations as $serviceLocation) {
            $dto = new ManagementBusinessPartner();
            $dto->id = $serviceLocation->getExtId();
            $dto->address = $serviceLocation->getAddress();
            $dto->information = $serviceLocation->getAdditionalInformation() ?? '';
            $dto->selected = $serviceLocation->getGroups()->contains($group);

            $items[] = $dto;
        }

        return new PaginatedCollection(
            totalItems: $serviceLocations->count(),
            items: $items,
        );
    }

    /**
     * @return PaginatedCollection<ManagementBusinessPartner>
     *
     * @throws \Exception
     */
    public function getGroupUsers(Request $request, string $id, string $groupId): PaginatedCollection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $businessPartner = $this->managementBusinessPartnerService->getBusinessPartner(businessPartnerId: $id);
        $group = $this->managementBusinessPartnerService->getGroup(groupId: $groupId, businessPartner: $businessPartner);

        $users = $this->userRepository->findByUserSearchCriteriaAndBusinessPartnerIdForPortal(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $id,
            searchInUser: true,
        );

        $items = [];
        foreach ($users as $user) {
            $dto = new ManagementBusinessPartner();
            $dto->id = $user->getId();
            $dto->email = $user->getUsername();
            $dto->selected = $user->getGroups()->contains($group);

            $items[] = $dto;
        }

        return new PaginatedCollection(
            totalItems: $users->count(),
            items: $items,
        );
    }

    private function mapCollectionToDto(BusinessPartnerEntity $entity): ManagementBusinessPartner
    {
        $dto = new ManagementBusinessPartner();
        $dto->id = $entity->getId();
        $dto->number = $entity->getExtId();
        $dto->name = $entity->getName();
        $dto->userCount = (string) $entity->getUsers()->count();

        return $dto;
    }
}
