<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Management\HttpEndpoint\ManagementUserEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ManagementUserEndpoint::class, 'getCollection'],
            uriTemplate: '/management/user',
            denormalizationContext: ['groups' => [self::USER_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for email, groups and roles.',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::USER_COLLECTION]],
            responseOpenApiSchemaName: 'ManagementUserListResponse'
        ),
        new Get(
            controller: [ManagementUserEndpoint::class, 'get'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/management/user/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_DETAIL]],
            normalizationContext: ['groups' => [self::USER_DETAIL]],
            responseOpenApiSchemaName: 'ManagementUserItemResponse',
        ),
        new Post(
            controller: [ManagementUserEndpoint::class, 'create'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/management/user',
            denormalizationContext: ['groups' => [self::USER_CREATE]],
            requestOpenApiSchemaName: 'ManagementUserCreateRequest',
            normalizationContext: ['groups' => [self::USER_CREATE]],
            responseOpenApiSchemaName: 'ManagementUserCreateResponse',
        ),
        new Patch(
            controller: [ManagementUserEndpoint::class, 'update'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/management/user/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_UPDATE]],
            requestOpenApiSchemaName: 'ManagementUserUpdateRequest',
            normalizationContext: ['groups' => [self::USER_UPDATE]],
            responseOpenApiSchemaName: 'ManagementUserUpdateResponse',
        ),
        new Delete(
            controller: [ManagementUserEndpoint::class, 'delete'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/management/user/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            responses: [
                new Response(httpCode: 204, description: 'User deleted'),
            ],
            responseOpenApiSchemaName: 'ManagementUserDeleteResponse',
        ),
        new GetCollection(
            controller: [ManagementUserEndpoint::class, 'getPermissionOptions'],
            uriTemplate: '/management/user/permissions-option',
            denormalizationContext: ['groups' => [self::USER_PERMISSION_OPTION]],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::USER_PERMISSION_OPTION]],
            responseOpenApiSchemaName: 'ManagementUserPermissionListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'ManagementUser',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ManagementUser
{
    private const string USER_COLLECTION = 'user:collection';
    private const string USER_DETAIL = 'user:detail';
    private const string USER_CREATE = 'user:create';
    private const string USER_UPDATE = 'user:update';
    private const string USER_PERMISSION_OPTION = 'user:permission-option';

    #[Groups([self::USER_COLLECTION, self::USER_DETAIL, self::USER_UPDATE, self::USER_PERMISSION_OPTION])]
    public string $id;

    #[Groups([self::USER_COLLECTION, self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public string $email;

    #[Groups([self::USER_COLLECTION])]
    public string $name;

    #[Groups([self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public ?string $firstname;

    #[Groups([self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public ?string $lastname;

    #[Groups([self::USER_COLLECTION])]
    public string $permissionsText;

    /**
     * @var string[]
     */
    #[Groups([self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public array $permissions = [];

    #[SerializedName('display')]
    #[Groups([self::USER_PERMISSION_OPTION])]
    public string $display;

    #[SerializedName('tooltip')]
    #[Groups([self::USER_PERMISSION_OPTION])]
    public string $tooltip;
}
