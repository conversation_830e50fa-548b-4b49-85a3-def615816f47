<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\Resource\Dto\GroupSelected;
use App\Infrastructure\Portal\Management\HttpEndpoint\ManagementBusinessPartnerEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getCollection'],
            uriTemplate: '/management/businesspartner',
            denormalizationContext: ['groups' => [self::BUSINESS_PARTNER_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for external id and name.',
                    parameterFormat: 'string',
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::BUSINESS_PARTNER_COLLECTION]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerListResponse',
        ),
        new Get(
            controller: [ManagementBusinessPartnerEndpoint::class, 'get'],
            uriTemplate: '/management/businesspartner/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::BUSINESS_PARTNER_DETAIL]],
            normalizationContext: ['groups' => [self::BUSINESS_PARTNER_DETAIL]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerItemResponse',
        ),
        new GetCollection(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getUserCollection'],
            uriTemplate: '/management/businesspartner/{id}/user',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for Users',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::USER_COLLECTION]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerUserListResponse',
        ),
        new Post(
            controller: [ManagementBusinessPartnerEndpoint::class, 'createUser'],
            uriTemplate: '/management/businesspartner/{id}/user',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_CREATE]],
            normalizationContext: ['groups' => [self::USER_CREATE]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerUserCreateResponse',
        ),
        new Patch(
            controller: [ManagementBusinessPartnerEndpoint::class, 'updateUser'],
            uriTemplate: '/management/businesspartner/{id}/user/{userId}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID of the businesspartner', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
                new PathParameter(name: 'userId', type: 'string', description: 'ID of the user', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_UPDATE]],
            normalizationContext: ['groups' => [self::USER_UPDATE]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerUserUpdateResponse',
        ),
        new Get(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getUserItem'],
            uriTemplate: '/management/businesspartner/{id}/user/{userId}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
                new PathParameter(name: 'userId', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_DETAIL]],
            normalizationContext: ['groups' => [self::USER_DETAIL]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerUserItemResponse',
        ),
        new Delete(
            controller: [ManagementBusinessPartnerEndpoint::class, 'deleteUser'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/management/businesspartner/{id}/user/{userId}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
                new PathParameter(name: 'userId', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            responses: [
                new Response(httpCode: 204, description: 'User deleted'),
            ],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerUserDeleteResponse',
        ),
        new GetCollection(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getPermissionOptions'],
            uriTemplate: '/management/businesspartner/{id}/user/permissions-option',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_PERMISSION_OPTION]],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::USER_PERMISSION_OPTION]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerUserPermissionListResponse',
        ),
        new GetCollection(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getGroupOptions'],
            uriTemplate: '/management/businesspartner/{id}/user/groups-option',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_GROUP_OPTION]],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::USER_GROUP_OPTION]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerUserGroupListResponse',
        ),
        new GetCollection(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getGroupCollection'],
            uriTemplate: '/management/businesspartner/{id}/group',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerGroupListResponse',
        ),
        new GetCollection(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getServiceLocationsCollection'],
            uriTemplate: '/management/businesspartner/{id}/service-location',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::SERVICE_LOCATION_COLLECTION]],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::SERVICE_LOCATION_COLLECTION]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerServiceLocationListResponse',
        ),
        new Get(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getGroupItem'],
            uriTemplate: '/management/businesspartner/{id}/group/{groupId}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
                new PathParameter(name: 'groupId', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_DETAIL]],
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerGroupItemResponse',
        ),
        new Post(
            controller: [ManagementBusinessPartnerEndpoint::class, 'createGroup'],
            uriTemplate: '/management/businesspartner/{id}/group',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            requestOpenApiSchemaName: 'ManagementBusinessPartnerGroupCreateRequest',
            normalizationContext: ['groups' => [self::GROUP_CREATE]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerGroupCreateResponse',
        ),
        new Patch(
            controller: [ManagementBusinessPartnerEndpoint::class, 'updateGroup'],
            uriTemplate: '/management/businesspartner/{id}/group/{groupId}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
                new PathParameter(name: 'groupId', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE]],
            requestOpenApiSchemaName: 'ManagementBusinessPartnerGroupUpdateRequest',
            normalizationContext: ['groups' => [self::GROUP_UPDATE]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerGroupUpdateResponse',
        ),
        new Delete(
            controller: [ManagementBusinessPartnerEndpoint::class, 'deleteGroup'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/management/businesspartner/{id}/group/{groupId}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
                new PathParameter(name: 'groupId', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            responses: [
                new Response(httpCode: 204, description: 'User deleted'),
            ],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerGroupDeleteResponse',
        ),
        new GetCollection(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getGroupServiceLocations'],
            uriTemplate: '/management/businesspartner/{id}/group/service-location',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_SERVICE_LOCATION_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'groupId',
                    parameterDescription: 'Id of the group to get the service locations for',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for Service Locations',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_SERVICE_LOCATION_COLLECTION]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerGroupServiceLocationListResponse'
        ),
        new GetCollection(
            controller: [ManagementBusinessPartnerEndpoint::class, 'getGroupUsers'],
            uriTemplate: '/management/businesspartner/{id}/group/{groupId}/user',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
                new PathParameter(name: 'groupId', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_USER_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for Users',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_USER_COLLECTION]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerGroupUserListResponse'
        ),
        new Patch(
            controller: [ManagementBusinessPartnerEndpoint::class, 'updateGroupUsers'],
            uriTemplate: '/management/businesspartner/{id}/group/{groupId}/user',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
                new PathParameter(name: 'groupId', type: 'string', description: 'ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_USER_UPDATE]],
            requestOpenApiSchemaName: 'ManagementBusinessPartnerGroupUpdateUserRequest',
            normalizationContext: ['groups' => [self::GROUP_USER_UPDATE]],
            responseOpenApiSchemaName: 'ManagementBusinessPartnerGroupUpdateUserResponse',
        ),
    ],
    identifier: 'id',
    tag: 'ManagementBusinessPartner',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ManagementBusinessPartner
{
    public const string BUSINESS_PARTNER_COLLECTION = 'business_partner:collection';
    public const string BUSINESS_PARTNER_DETAIL = 'business_partner:detail';
    public const string USER_COLLECTION = 'user:collection';
    public const string USER_DETAIL = 'user:detail';
    public const string USER_CREATE = 'user:create';
    public const string USER_UPDATE = 'user:update';
    public const string USER_PERMISSION_OPTION = 'user:permission-option';
    public const string USER_GROUP_OPTION = 'user:group-option';
    public const string GROUP_COLLECTION = 'group:collection';
    public const string GROUP_DETAIL = 'group:detail';
    public const string GROUP_CREATE = 'group:create';
    public const string GROUP_UPDATE = 'group:update';
    public const string GROUP_USER_COLLECTION = 'group_user:collection';
    public const string GROUP_USER_UPDATE = 'group_user:update';
    public const string SERVICE_LOCATION_COLLECTION = 'service-location:collection';
    public const string GROUP_SERVICE_LOCATION_COLLECTION = 'group-service-location:collection';

    #[Groups([self::BUSINESS_PARTNER_COLLECTION, self::BUSINESS_PARTNER_DETAIL, self::USER_COLLECTION,
        self::USER_DETAIL, self::USER_PERMISSION_OPTION, self::USER_GROUP_OPTION, self::GROUP_DETAIL,
        self::GROUP_COLLECTION, self::SERVICE_LOCATION_COLLECTION, self::GROUP_SERVICE_LOCATION_COLLECTION,
        self::GROUP_USER_COLLECTION,
    ])]
    public string $id;

    #[Groups([self::BUSINESS_PARTNER_COLLECTION, self::BUSINESS_PARTNER_DETAIL])]
    public string $number;

    #[Groups([self::BUSINESS_PARTNER_COLLECTION, self::BUSINESS_PARTNER_DETAIL, self::SERVICE_LOCATION_COLLECTION,
        self::GROUP_COLLECTION, self::GROUP_DETAIL, self::GROUP_CREATE, self::GROUP_UPDATE,
    ])]
    public string $name;

    #[Groups([self::BUSINESS_PARTNER_COLLECTION, self::BUSINESS_PARTNER_DETAIL, self::GROUP_COLLECTION])]
    public string $userCount;

    #[Groups([self::BUSINESS_PARTNER_DETAIL])]
    public string $groupCount;

    #[Groups([self::BUSINESS_PARTNER_DETAIL])]
    public string $lastUpdate;

    #[Groups([self::USER_COLLECTION, self::USER_DETAIL, self::USER_UPDATE, self::USER_CREATE,
        self::GROUP_USER_COLLECTION])]
    public string $email;

    #[Groups([self::USER_DETAIL, self::USER_UPDATE, self::USER_CREATE])]
    public string $firstname;

    #[Groups([self::USER_DETAIL, self::USER_UPDATE, self::USER_CREATE])]
    public string $lastname;

    /**
     * @var GroupSelected[]
     */
    #[Groups([self::USER_DETAIL, self::USER_UPDATE, self::USER_CREATE])]
    public array $groups;

    /**
     * @var string[]
     */
    #[Groups([self::USER_DETAIL, self::USER_UPDATE, self::USER_CREATE])]
    public array $permissions;

    #[SerializedName('display')]
    #[Groups([self::USER_PERMISSION_OPTION, self::USER_GROUP_OPTION])]
    public string $display;

    #[SerializedName('tooltip')]
    #[Groups([self::USER_PERMISSION_OPTION])]
    public string $tooltip;

    /**
     * @var GroupSelected[]
     */
    #[Groups([self::GROUP_UPDATE, self::GROUP_CREATE])]
    public array $selectedServiceLocations;

    #[Groups([self::GROUP_SERVICE_LOCATION_COLLECTION, self::SERVICE_LOCATION_COLLECTION])]
    public string $address;

    #[Groups([self::GROUP_SERVICE_LOCATION_COLLECTION])]
    public string $information;

    #[Groups([self::GROUP_SERVICE_LOCATION_COLLECTION, self::GROUP_USER_COLLECTION])]
    public bool $selected;

    /**
     * @var GroupSelected[]
     */
    #[Groups([self::GROUP_USER_UPDATE])]
    public array $selectedUsers;
}
