<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Management\HttpEndpoint\ManagementServiceProductEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ManagementServiceProductEndpoint::class, 'getCollection'],
            uriTemplate: '/management/service-product',
            filters: [
                new Filter(
                    parameterName: 'serviceType',
                    parameterDescription: 'Filter for service type.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'serviceProduct',
                    parameterDescription: 'Filter for service product.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for service.',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'ServiceProductListResponse',
        ),
        new Post(
            controller: [ManagementServiceProductEndpoint::class, 'create'],
            uriTemplate: '/management/service-product',
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            requestOpenApiSchemaName: 'ManagementServiceProductCreateRequest',
            responseOpenApiSchemaName: 'ManagementServiceProductCreateResponse',
        ),
        new Patch(
            controller: [ManagementServiceProductEndpoint::class, 'update'],
            uriTemplate: '/management/service-product/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'The service product ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE]],
            requestOpenApiSchemaName: 'ManagementServiceProductUpdateRequest',
            responseOpenApiSchemaName: 'ManagementServiceProductUpdateResponse',
        ),
        new Delete(
            controller: [ManagementServiceProductEndpoint::class, 'delete'],
            uriTemplate: '/management/service-product/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'The service product ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            responseOpenApiSchemaName: 'ManagementServiceProductDeleteResponse',
        ),
        new GetCollection(
            controller: [ManagementServiceProductEndpoint::class, 'getOptions'],
            uriTemplate: '/management/service-product/service-type/option',
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_OPTION]],
            responseOpenApiSchemaName: 'ManagementServiceProductOptionListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'ManagementServiceProduct',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ManagementServiceProduct
{
    private const string GROUP_CREATE = 'serviceProduct:create';
    private const string GROUP_COLLECTION = 'serviceProduct:collection';
    private const string GROUP_UPDATE = 'serviceProduct:updateServiceProduct';
    private const string GROUP_OPTION = 'serviceProduct:option';

    #[SerializedName('id')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_OPTION])]
    public string $id;

    #[SerializedName('display')]
    #[Groups([self::GROUP_OPTION])]
    public ?string $display = null;

    #[SerializedName('service_product')]
    #[Groups([self::GROUP_CREATE, self::GROUP_COLLECTION, self::GROUP_UPDATE])]
    public string $serviceProduct;

    #[SerializedName('container')]
    #[Groups([self::GROUP_CREATE, self::GROUP_COLLECTION, self::GROUP_UPDATE])]
    public string $container;

    #[SerializedName('service_type')]
    #[Groups([self::GROUP_CREATE, self::GROUP_COLLECTION, self::GROUP_UPDATE])]
    public string $serviceType;
}
