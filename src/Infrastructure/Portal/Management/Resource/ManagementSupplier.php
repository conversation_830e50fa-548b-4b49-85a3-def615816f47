<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\Resource\Dto\OrderConfirmation;
use App\Infrastructure\Portal\Management\HttpEndpoint\ManagementSupplierEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ManagementSupplierEndpoint::class, 'getCollection'],
            uriTemplate: '/management/supplier',
            filters: [
                new Filter(
                    parameterName: 'region',
                    parameterDescription: 'Filter for region',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for supplier.',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'SupplierListResponse',
        ),
        new Get(
            controller: [ManagementSupplierEndpoint::class, 'get'],
            uriTemplate: '/management/supplier/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'SupplierItemResponse',
        ),
        new Post(
            controller: [ManagementSupplierEndpoint::class, 'create'],
            uriTemplate: '/management/supplier',
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            requestOpenApiSchemaName: 'ManagementSupplierCreateRequest',
            responseOpenApiSchemaName: 'ManagementSupplierCreateResponse',
        ),
        new Patch(
            controller: [ManagementSupplierEndpoint::class, 'update'],
            uriTemplate: '/management/supplier/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'The supplier ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE]],
            requestOpenApiSchemaName: 'ManagementSupplierUpdateRequest',
            responseOpenApiSchemaName: 'ManagementSupplierUpdateResponse',
        ),
        new GetCollection(
            controller: [ManagementSupplierEndpoint::class, 'getOptionsDelay'],
            uriTemplate: '/management/supplier-delay-option',
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_OPTION_DELAY]],
            responseOpenApiSchemaName: 'ManagementSupplierOptionsDelayListResponse',
        ),
        new GetCollection(
            controller: [ManagementSupplierEndpoint::class, 'getOptionsDailyDelay'],
            uriTemplate: '/management/supplier-daily-delay-option',
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_OPTION_DAILY_DELAY]],
            responseOpenApiSchemaName: 'ManagementSupplierOptionsDailyDelayListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'ManagementSupplier',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ManagementSupplier
{
    private const string GROUP_COLLECTION = 'supplier:collection';
    public const string GROUP_DETAIL = 'supplier:detail';
    public const string GROUP_UPDATE = 'supplier:update';
    public const string GROUP_CREATE = 'supplier:create';
    public const string GROUP_OPTION_DELAY = 'supplier:option:delay';
    public const string GROUP_OPTION_DAILY_DELAY = 'supplier:option:daily-delay';

    #[SerializedName('id')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_OPTION_DELAY, self::GROUP_OPTION_DAILY_DELAY])]
    public string $id;

    #[SerializedName('display')]
    #[Groups([self::GROUP_OPTION_DELAY, self::GROUP_OPTION_DAILY_DELAY])]
    public ?string $display = null;

    #[SerializedName('region')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_CREATE])]
    public string $region;

    #[SerializedName('location')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_CREATE])]
    public string $location;

    #[SerializedName('description')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $description;

    #[SerializedName('number')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_CREATE])]
    public string $number;

    #[SerializedName('name')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $name;

    #[SerializedName('vkorg')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $vkorg;

    #[SerializedName('order_delay')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $orderDelay;

    #[SerializedName('street')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $street;

    #[SerializedName('house_number')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $houseNumber;

    #[SerializedName('city')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $city;

    #[SerializedName('postal_code')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $postalCode;

    #[SerializedName('country')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $country;

    #[SerializedName('email')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $email;

    #[SerializedName('phone')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $phone;

    #[SerializedName('opening_hours')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public string $openingHours;

    #[SerializedName('order_delay_monday')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public ?string $orderDelayMonday;

    #[SerializedName('order_delay_tuesday')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public ?string $orderDelayTuesday;

    #[SerializedName('order_delay_wednesday')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public ?string $orderDelayWednesday;

    #[SerializedName('order_delay_thursday')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public ?string $orderDelayThursday;

    #[SerializedName('order_delay_friday')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public ?string $orderDelayFriday;

    /**
     * @var OrderConfirmation[]
     */
    #[SerializedName('order_confirmation')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_UPDATE, self::GROUP_CREATE])]
    public array $orderConfirmation;
}
