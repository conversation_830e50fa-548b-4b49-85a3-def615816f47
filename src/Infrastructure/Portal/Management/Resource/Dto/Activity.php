<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\Resource\Dto;

use App\Infrastructure\Portal\Management\Resource\ManagementOrder;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class Activity
{
    #[SerializedName('id')]
    #[Groups([ManagementOrder::GROUP_DETAIL])]
    public string $id;

    #[SerializedName('type')]
    #[Groups([ManagementOrder::GROUP_DETAIL])]
    public string $type;

    #[SerializedName('name')]
    #[Groups([ManagementOrder::GROUP_DETAIL])]
    public ?string $name;

    #[SerializedName('description')]
    #[Groups([ManagementOrder::GROUP_DETAIL])]
    public string $description;

    #[SerializedName('created_at')]
    #[Groups([ManagementOrder::GROUP_DETAIL])]
    public string $createdAt;
}
