<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Management\HttpEndpoint\ManagementOrderEndpoint;
use App\Infrastructure\Portal\Management\Resource\Dto\Activity;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [ManagementOrderEndpoint::class, 'get'],
            uriTemplate: '/management/order/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'The order ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_DETAIL]],
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'ManagementOrderItemResponse',
        ),
        new GetCollection(
            controller: [ManagementOrderEndpoint::class, 'getCollection'],
            uriTemplate: '/management/order',
            denormalizationContext: ['groups' => [self::GROUP_DETAIL]],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Free search field.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'status',
                    parameterDescription: 'Filter for status',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'startDate',
                    parameterDescription: 'Filter for start date.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'endDate',
                    parameterDescription: 'Filter for end date.',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'ManagementOrderListResponse'
        ),
    ],
    identifier: 'id',
    tag: 'ManagementOrder',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ManagementOrder
{
    public const string GROUP_DETAIL = 'order-overview:detail';

    private const string GROUP_COLLECTION = 'order-overview:collection';

    #[SerializedName('id')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAIL])]
    public string $id;

    #[SerializedName('order_date')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAIL])]
    public string $orderDate;

    #[SerializedName('customer_number')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAIL])]
    public ?string $customerNumber;

    #[SerializedName('email')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?string $email;

    #[SerializedName('service_type')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?string $serviceType;

    #[SerializedName('contract_number')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAIL])]
    public ?string $contractNumber;

    #[SerializedName('service_id')]
    #[Groups([self::GROUP_DETAIL])]
    public ?string $serviceId;

    #[SerializedName('service_location')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAIL])]
    public ?string $serviceLocation;

    #[SerializedName('service_location_id')]
    #[Groups([self::GROUP_DETAIL])]
    public ?string $serviceLocationId;

    #[SerializedName('status')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAIL])]
    public string $status;

    #[SerializedName('status_text')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_DETAIL])]
    public string $statusText;

    /**
     * @var Activity[]
     */
    #[SerializedName('activities')]
    #[Groups([self::GROUP_DETAIL])]
    public array $activities = [];
}
