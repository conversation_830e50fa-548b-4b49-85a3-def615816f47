<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Management\Service;

use App\Domain\Entity\BusinessPartner as BusinessPartnerEntity;
use App\Domain\Entity\Group as GroupEntity;
use App\Domain\Entity\User as UserEntity;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\GroupRepository;
use App\Domain\Repository\UserRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\Dto\GroupSelected;
use App\Infrastructure\Portal\Management\Resource\ManagementBusinessPartner;

readonly class ManagementBusinessPartnerService
{
    public function __construct(
        private BusinessPartnerRepository $businessPartnerRepository,
        private GroupRepository $groupRepository,
        private UserRepository $userRepository,
    ) {
    }

    public function getBusinessPartner(string $businessPartnerId): BusinessPartnerEntity
    {
        $businessPartner = $this->businessPartnerRepository->find(id: $businessPartnerId);

        if (null === $businessPartner) {
            throw new NotFoundException(message: "Business partner $businessPartnerId not found");
        }

        return $businessPartner;
    }

    public function getGroup(string $groupId, BusinessPartnerEntity $businessPartner): GroupEntity
    {
        $group = $this->groupRepository->findOneByIdAndBusinessPartner(id: $groupId, businessPartner: $businessPartner);

        if (null === $group) {
            throw new NotFoundException(message: "Group $groupId not found");
        }

        return $group;
    }

    public function getUser(string $userId, BusinessPartnerEntity $businessPartner): UserEntity
    {
        $user = $this->userRepository->findByIdAndBusinessPartnerId(id: $userId, businessPartnerId: $businessPartner->getId());

        if (null === $user) {
            throw new NotFoundException(message: "User $userId not found");
        }

        return $user;
    }

    /**
     * @return array<int, GroupEntity>
     */
    public function findGroups(
        ManagementBusinessPartner $managementBusinessPartner,
        BusinessPartnerEntity $businessPartner,
    ): array {
        /** @var array<int, string> $groupIds */
        $groupIds = array_map(
            callback: fn (GroupSelected $selected): string => $selected->id,
            array: $managementBusinessPartner->groups,
        );

        return $this->groupRepository->findByIdsAndBusinessPartner(groups: $groupIds, businessPartner: $businessPartner);
    }
}
