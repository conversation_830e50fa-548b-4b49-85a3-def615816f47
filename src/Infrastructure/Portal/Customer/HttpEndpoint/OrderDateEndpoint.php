<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\ServiceType;
use App\Domain\Entity\Order as OrderEntity;
use App\Domain\Repository\ContractPositionRepository;
use App\Domain\Repository\ContractRepository;
use App\Domain\Repository\OrderRepository;
use App\Domain\Repository\ServiceLocationRepository;
use App\Domain\Service\OrderService;
use App\Infrastructure\Portal\Customer\Resource\OrderDate;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class OrderDateEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private OrderRepository $orderRepository,
        private OrderService $orderService,
        private ServiceLocationRepository $serviceLocationRepository,
        private ContractRepository $contractRepository,
        private ContractPositionRepository $contractPositionRepository,
    ) {
    }

    /**
     * @return PaginatedCollection<OrderDate>
     *
     * @throws QueryException
     * @throws \DateMalformedStringException
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $paginator = $this->orderRepository->findOrderDateByUserSearchCriteria(userSearchCriteria: $userSearchCriteria, businessPartnerId: $businessPartnerId);
        $items = array_map(
            callback: fn (OrderEntity $order): OrderDate => $this->mapToDto(order: $order),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    public function mapToDto(OrderEntity $order): OrderDate
    {
        $dto = new OrderDate();
        $dto->id = $order->getId();
        $dto->calendarWeek = $order->getOrderDate()->format(format: 'W');
        $dto->date = $order->getOrderDate()->format(format: 'd.m.Y');

        $service = $this->orderService->getService(order: $order);
        if (null !== $service) {
            $contract = $this->contractRepository->findByUniqueConstraint(extId: $service->getExtContractId());

            if (null === $contract) {
                throw new NotFoundHttpException(message: 'Contract '.$service->getExtContractId().' not found!');
            }

            $serviceType = ServiceType::tryFrom(value: $service->getServiceType());

            $materialPosition = $this->contractPositionRepository->findOneByExtContractIdAndExtMaterialId(
                extContractId: $contract->getId(),
                extMaterialId: $service->getExtWasteMaterialId()
            );

            $containerPosition = $this->contractPositionRepository->findOneByExtContractIdAndExtMaterialId(
                extContractId: $contract->getId(),
                extMaterialId: $service->getExtContainerMaterialId()
            );

            $serviceLocation = $this->serviceLocationRepository->findByUniqueConstraint(extId: $service->getExtServiceLocationId());

            if (null !== $serviceLocation) {
                // TODO
                $serviceLocationAddress = null !== $serviceLocation->getAdditionalInformation()
                    ? sprintf('%s - %s', $serviceLocation->getAddress(), $serviceLocation->getAdditionalInformation())
                    : $serviceLocation->getAddress();

                $dto->serviceLocation = $serviceLocationAddress;
            }

            $dto->container = $containerPosition?->getMaterialText() ?? null;
            $dto->material = $materialPosition?->getMaterialText() ?? null;
            $dto->contract = $contract->getExtId();
            $dto->serviceType = sprintf('{{%s}}', strtolower(string: ($serviceType->name) ?? ''));
        } else {
            $dto->material = null;
            $dto->container = null;
            $dto->serviceLocation = null;
            $dto->contract = null;
            $dto->serviceType = null;
        }

        return $dto;
    }
}
