<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class UserCheckEndpoint
{
    public function checkUsername(string $username): Response
    {
        $existingUser = 'username' !== $username ?: null;

        if (null === $existingUser) {
            return new Response();
        }

        return new Response(status: Response::HTTP_CONFLICT);
    }

    public function checkEmail(string $email): Response
    {
        $existingEmail = '<EMAIL>' !== $email ?: null;

        if (null === $existingEmail) {
            return new Response();
        }

        return new Response(status: Response::HTTP_CONFLICT);
    }
}
