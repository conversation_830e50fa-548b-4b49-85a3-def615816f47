<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Repository\WasteEcoSavingsRepository;
use App\Infrastructure\Portal\Customer\Resource\EmissionDataTotal;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class EmissionDataTotalEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private WasteEcoSavingsRepository $wasteEcoSavingsRepository,
    ) {
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function get(Request $request): EmissionDataTotal
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        $totalSavings = $this->wasteEcoSavingsRepository->getTotalSavingsByBusinessPartnerId(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartnerId
        );
        $totalEmissions = $this->wasteEcoSavingsRepository->getTotalEmissionsByBusinessPartnerId(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartnerId
        );

        return $this->mapToDto(totalSavings: $totalSavings, totalEmissions: $totalEmissions);
    }

    public function mapToDto(float $totalSavings, float $totalEmissions): EmissionDataTotal
    {
        $dto = new EmissionDataTotal();
        $dto->totalEmissions = $totalEmissions.' t';
        $dto->totalSavings = $totalSavings.' t';

        return $dto;
    }
}
