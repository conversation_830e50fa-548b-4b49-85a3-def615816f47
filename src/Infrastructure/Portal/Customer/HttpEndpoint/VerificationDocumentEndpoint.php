<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\VerificationDocumentStatus;
use App\Domain\Entity\VerificationDocument as VerificationDocumentEntity;
use App\Domain\Entity\VerificationDocumentPartner as VerificationDocumentPartnerEntity;
use App\Domain\Repository\VerificationDocumentPartnerRepository;
use App\Domain\Repository\VerificationDocumentRepository;
use App\Infrastructure\Portal\Customer\Resource\Dto\DisposalSite;
use App\Infrastructure\Portal\Customer\Resource\Dto\VerificationDocumentServiceLocation;
use App\Infrastructure\Portal\Customer\Resource\VerificationDocument;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class VerificationDocumentEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private VerificationDocumentRepository $verificationDocumentRepository,
        private VerificationDocumentPartnerRepository $verificationDocumentPartnerRepository,
    ) {
    }

    /**
     * @return PaginatedCollection<VerificationDocument>
     *
     * @throws QueryException
     * @throws \DateMalformedStringException
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $paginator = $this->verificationDocumentRepository->findByUserSearchCriteria(userSearchCriteria: $userSearchCriteria, businessPartnerId: $businessPartnerId);
        $items = array_map(
            callback: fn (VerificationDocumentEntity $verificationDocument): VerificationDocument => $this->mapToDto(verificationDocument: $verificationDocument),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    public function get(Request $request, string $id): VerificationDocument
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $supervisionDocument = $this->verificationDocumentRepository->findOneByIdAndBusinessPartner(id: $id, businessPartnerId: $businessPartnerId);

        if (null === $supervisionDocument) {
            throw new NotFoundHttpException(message: "Supervision Document $id not found");
        }

        return $this->mapToDto(verificationDocument: $supervisionDocument);
    }

    private function mapToDto(VerificationDocumentEntity $verificationDocument): VerificationDocument
    {
        $frontendStatus = VerificationDocumentStatus::from(value: $verificationDocument->getStatus())->getFrontendStatus();

        $dto = new VerificationDocument();
        $dto->id = $verificationDocument->getId();
        $dto->avvId = $verificationDocument->getAvvId();
        $dto->description = $verificationDocument->getDescription();
        $dto->endDate = $verificationDocument->getEndDate()->format(format: 'd.m.Y');
        $dto->remainingQuantity = $verificationDocument->getApprovedRemAmountDocument().' '.$verificationDocument->getAmountUnitOm();
        $dto->status = $frontendStatus ?? '';
        $dto->statusText = sprintf('{{%s}}', $frontendStatus);
        $dto->totalQuantity = $verificationDocument->getApprovedAmountDocument().' '.$verificationDocument->getAmountUnitOm();
        $dto->approvedDate = $verificationDocument->getApproval()->format(format: 'd.m.Y');
        $dto->businessPartner = $verificationDocument->getExtBusinessPartnerId();
        $dto->certificateNumber = $verificationDocument->getCertificateNumber();
        $dto->validTo = $verificationDocument->getEndDate()->format(format: 'd.m.Y');

        $verificationDocumentPartners = $this->verificationDocumentPartnerRepository->findAllByExtVerificationDocumentId(extVerificationDocumentId: $verificationDocument->getExtId());

        foreach ($verificationDocumentPartners as $verificationDocumentPartner) {
            if (str_contains(haystack: $verificationDocumentPartner->getType(), needle: 'ERZ')) {
                $dto->serviceLocation = new VerificationDocumentServiceLocation();
                $this->mapToServiceLocationDto(partner: $verificationDocumentPartner, dto: $dto->serviceLocation);
                $dto->serviceLocationDescription = $dto->serviceLocation->name;
            }

            if (str_contains(haystack: $verificationDocumentPartner->getType(), needle: 'ENT')) {
                $dto->disposalSite = new DisposalSite();
                $this->mapToDisposalSiteDto(partner: $verificationDocumentPartner, dto: $dto->disposalSite);
                $dto->disposalSiteDescription = $dto->disposalSite->name;
            }
        }

        return $dto;
    }

    private function mapToServiceLocationDto(VerificationDocumentPartnerEntity $partner, VerificationDocumentServiceLocation &$dto): void
    {
        $dto->id = $partner->getId();
        $dto->name = $partner->getName();
        $dto->postalCode = $partner->getPostalCode();
        $dto->city = $partner->getCity();
        $dto->street = $partner->getStreet();
    }

    private function mapToDisposalSiteDto(VerificationDocumentPartnerEntity $partner, DisposalSite &$dto): void
    {
        $dto->id = $partner->getId();
        $dto->name = $partner->getName();
        $dto->postalCode = $partner->getPostalCode();
        $dto->city = $partner->getCity();
        $dto->street = $partner->getStreet();
        $dto->number = $partner->getExtId();
    }
}
