<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\SupervisionDocumentStatus as SupervisionDocumentStatusEnum;
use App\Infrastructure\Portal\Customer\Resource\SupervisionDocumentStatus;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class SupervisionDocumentStatusEndpoint
{
    /**
     * @return Collection<SupervisionDocumentStatus>
     */
    public function getOptions(): Collection
    {
        $items = array_map(
            callback: function (SupervisionDocumentStatusEnum $status): ?SupervisionDocumentStatus {
                $frontendStatus = SupervisionDocumentStatusEnum::from(value: $status->value)->getFrontendStatus();

                if ($frontendStatus) {
                    $ret = new SupervisionDocumentStatus();
                    $ret->id = $frontendStatus;
                    $ret->display = sprintf('{{%s}}', $frontendStatus);

                    return $ret;
                }

                return null;
            },
            array: SupervisionDocumentStatusEnum::cases()
        );

        $filteredItems = array_filter(array: $items);
        $uniqueItems = [];
        $seenIds = [];

        foreach ($filteredItems as $item) {
            if (!in_array(needle: $item->id, haystack: $seenIds, strict: true)) {
                $uniqueItems[] = $item;
                $seenIds[] = $item->id;
            }
        }

        return new Collection(
            items: $uniqueItems
        );
    }
}
