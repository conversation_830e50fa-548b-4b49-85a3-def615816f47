<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\ProductHierarchy;
use App\Domain\Entity\WasteEcoSavings as WasteEcoSavingsEntity;
use App\Domain\Repository\WasteEcoSavingsRepository;
use App\Infrastructure\Portal\Customer\Resource\EmissionData;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class EmissionDataEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private WasteEcoSavingsRepository $wasteEcoSavingsRepository,
    ) {
    }

    /**
     * @return Collection<EmissionData>
     *
     * @throws \Exception
     */
    public function getOverview(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        $items = [];

        foreach (ProductHierarchy::cases() as $productHierarchy) {
            $overview = $this->wasteEcoSavingsRepository->findAllByProductHierarchyAndBusinessPartnerId(
                userSearchCriteria: $userSearchCriteria,
                productHierarchy: $productHierarchy->value,
                businessPartnerId: $businessPartnerId
            );

            if ([] === $overview) {
                continue;
            }

            $dto = $this->mapToDto(wasteEcoSavings: $overview, productHierarchy: $productHierarchy);

            $totalWeight = 0;
            foreach ($overview as $data) {
                $totalWeight += (float) $data->getTotalWeight();
            }
            $dto->totalWeight = $totalWeight.' t';

            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }

    /**
     * @return Collection<EmissionData>
     *
     * @throws \Exception
     */
    public function getMaterial(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        $items = [];

        foreach (ProductHierarchy::cases() as $productHierarchy) {
            $material = $this->wasteEcoSavingsRepository->findAllByProductHierarchyAndBusinessPartnerId(
                userSearchCriteria: $userSearchCriteria,
                productHierarchy: $productHierarchy->value,
                businessPartnerId: $businessPartnerId
            );

            if ([] === $material) {
                continue;
            }

            $dto = $this->mapToDto(wasteEcoSavings: $material, productHierarchy: $productHierarchy);
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }

    /**
     * @param array<WasteEcoSavingsEntity> $wasteEcoSavings
     */
    private function mapToDto(array $wasteEcoSavings, ProductHierarchy $productHierarchy): EmissionData
    {
        $dto = new EmissionData();
        $dto->material = $productHierarchy->getFrontendKey() ?? '';

        $savings = 0;
        $emissions = 0;

        foreach ($wasteEcoSavings as $data) {
            $savings += (float) $data->getCo2Savings();
            $emissions += (float) $data->getCo2Emissions();
        }

        $dto->savings = $savings.' t';
        $dto->emissions = $emissions.' t';

        return $dto;
    }
}
