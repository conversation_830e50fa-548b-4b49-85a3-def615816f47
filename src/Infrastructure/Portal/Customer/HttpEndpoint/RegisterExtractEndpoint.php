<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Service\Mailer;
use App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUser;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class RegisterExtractEndpoint
{
    public function __construct(
        private Security $security,
        private Mailer $mailer,
    ) {
    }

    public function request(): void
    {
        $user = $this->security->getUser();

        if ($user instanceof KeycloakUser && !is_null(value: $user->getEmail())) {
            $this->mailer->send(
                emailTo: [$user->getEmail()],
                subject: 'Register Extract',
                context: []
            );
        }
    }
}
