<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\ContractPosition as ContractPositionEntity;
use App\Domain\Repository\ContractPositionRepository;
use App\Domain\Repository\ContractRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\ContractPosition;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ContractPositionEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private ContractRepository $contractRepository,
        private ContractPositionRepository $contractPositionRepository,
    ) {
    }

    /**
     * @return PaginatedCollection<ContractPosition>
     *
     * @throws \Exception
     */
    public function getCollection(Request $request, string $id): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        // First verify that the contract exists and belongs to the business partner
        $contract = $this->contractRepository->findOneByIdAndBusinessPartner(id: $id, businessPartnerId: $businessPartnerId);
        if (null === $contract) {
            throw new NotFoundException(message: 'Contract not found');
        }

        // Get contract positions using the contract's extId with search criteria
        $contractPositions = $this->contractPositionRepository->findByUserSearchCriteria(
            userSearchCriteria: $userSearchCriteria,
            contractId: $contract->getExtId(),
            businessPartnerId: $businessPartnerId
        );

        $items = [];
        foreach ($contractPositions as $contractPosition) {
            $items[] = $this->mapToDto(contractPosition: $contractPosition);
        }

        return new PaginatedCollection(
            totalItems: $contractPositions->count(),
            items: $items,
        );
    }

    private function mapToDto(ContractPositionEntity $contractPosition): ContractPosition
    {
        $dto = new ContractPosition();
        $dto->id = $contractPosition->getId();
        $dto->contactPositionId = $contractPosition->getExtPosId();
        $dto->quantity = $contractPosition->getQuantity().' '.$contractPosition->getUnitOm();
        $dto->amount = ($contractPosition->getNetValue() ? (string) $contractPosition->getNetValue() : '0').' '.$contractPosition->getCurrency();
        $dto->description = $contractPosition->getMaterialText();

        return $dto;
    }
}
