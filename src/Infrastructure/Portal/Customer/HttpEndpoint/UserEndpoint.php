<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\Permission;
use App\Domain\Entity\Group as GroupEntity;
use App\Domain\Entity\User as UserEntity;
use App\Domain\Repository\GroupRepository;
use App\Domain\Repository\UserRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUser;
use App\Infrastructure\Portal\Customer\Resource\Dto\UserSelected;
use App\Infrastructure\Portal\Customer\Resource\User;
use App\Infrastructure\Portal\Customer\Service\UserService;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class UserEndpoint
{
    public function __construct(
        private GroupRepository $groupRepository,
        private Security $security,
        private UserRepository $userRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private UserService $userService,
    ) {
    }

    private function getResource(): User
    {
        $securityUser = $this->security->getUser();
        $user = new User();
        if ($securityUser instanceof KeycloakUser) {
            $user->id = uuid_create(); // @phpstan-ignore-line
            $user->firstname = $securityUser->getFirstName() ?? '';
            $user->lastname = $securityUser->getLastName() ?? '';
            $user->email = $securityUser->getEmail() ?? '';
            $user->permissionsText = (string) count(value: $securityUser->getRoles());
            $user->permissions = $securityUser->getRoles();
            $user->groupsText = mt_rand().', '.mt_rand(1, 3);
            $user->groups = [];
        }

        return $user;
    }

    public function get(Request $request, string $id): User
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $entity = $this->userRepository->findByIdAndBusinessPartnerId(id: $id, businessPartnerId: $businessPartnerId);

        if (null === $entity) {
            throw new NotFoundException();
        }

        return $this->mapElementToDto(entity: $entity);
    }

    /**
     * @return PaginatedCollection<User>
     *
     * @throws QueryException
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $paginator = $this->userRepository->findByUserSearchCriteriaAndBusinessPartnerIdForPortal(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartnerId,
            searchInUser: true,
            searchInPermission: true,
            searchInGroup: true,
        );

        $items = array_map(
            callback: fn (UserEntity $entity): User => $this->mapCollectionToDto(user: $entity),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    /**
     * @return Collection<User>
     */
    public function getPermissionOptions(): Collection
    {
        $permissions = [];

        foreach (Permission::cases() as $permission) {
            $userPermission = new User();
            $userPermission->id = $permission->value;
            $userPermission->display = sprintf('{{%s}}', $permission->value);
            $userPermission->tooltip = '{{tooltip_'.$userPermission->display.'}}';
            $permissions[] = $userPermission;
        }

        return new Collection(
            items: $permissions,
        );
    }

    /**
     * @return Collection<User>
     */
    public function getGroupOptions(Request $request): Collection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $groups = $this->groupRepository->findByBusinessPartnerId(businessPartnerId: $businessPartnerId);

        $userGroups = [];

        foreach ($groups as $group) {
            $user = new User();
            $user->id = $group->getId();
            $user->display = $group->getName();

            $userGroups[] = $user;
        }

        return new Collection(
            items: $userGroups,
        );
    }

    public function create(Request $request, User $user): User
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $businessPartner = $this->userService->getBusinessPartner(businessPartnerId: $businessPartnerId);

        $entity = new UserEntity();
        $entity->setUsername(username: $user->email);
        $entity->setFirstname(firstname: $user->firstname);
        $entity->setLastname(lastname: $user->lastname);
        $entity->addBusinessPartner(businessPartner: $businessPartner);

        foreach ($user->permissions as $permissionValue) {
            $permission = Permission::tryFrom(value: $permissionValue);

            if (null !== $permission) {
                $entity->addPermission(permission: $permission);
            }
        }

        foreach ($this->userService->findGroups(user: $user) as $group) {
            $entity->addGroup(group: $group);
        }

        $this->userRepository->add(entity: $entity);

        return $user;
    }

    public function update(Request $request, string $id, User $user): User
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        $entity = $this->userRepository->findByIdAndBusinessPartnerId(id: $id, businessPartnerId: $businessPartnerId);

        if (null === $entity) {
            throw new NotFoundException(message: 'User '.$id.' was not found.');
        }

        $entity->setUsername(username: $user->email);
        $entity->setFirstname(firstname: $user->firstname);
        $entity->setLastname(lastname: $user->lastname);
        $entity->clearPermissions();

        foreach ($user->permissions as $permissionValue) {
            $permission = Permission::tryFrom(value: $permissionValue);

            if (null !== $permission) {
                $entity->addPermission(permission: $permission);
            }
        }

        $entity->clearGroups();
        foreach ($this->userService->findGroups(user: $user) as $group) {
            $entity->addGroup(group: $group);
        }

        $this->userRepository->add(entity: $entity);

        return $user;
    }

    public function delete(Request $request, string $id): void
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $entity = $this->userRepository->findByIdAndBusinessPartnerId(id: $id, businessPartnerId: $businessPartnerId);

        if (null === $entity) {
            throw new NotFoundException(message: 'User '.$id.' was not found.');
        }

        $this->userRepository->remove(entity: $entity);
    }

    /**
     * @return PaginatedCollection<User>
     *
     * @throws \Exception
     */
    public function getGroupUsers(Request $request): PaginatedCollection
    {
        $maxCount = 10;
        $resources = [];

        for ($i = 0; $i <= $maxCount; ++$i) {
            $resources[] = $this->getResource();
        }

        return new PaginatedCollection(
            totalItems: $maxCount,
            items: $resources,
        );
    }

    public function updateGroupUser(): User
    {
        return $this->getResource();
    }

    private function mapCollectionToDto(UserEntity $user): User
    {
        $permissions = array_map(
            callback: fn (Permission $permission): string => $permission->value,
            array: $user->getPermissions(),
        );

        $groups = array_map(
            callback: fn (GroupEntity $group): string => $group->getName(),
            array: $this->groupRepository->findByUser(user: $user),
        );

        $dto = new User();
        $dto->id = $user->getId();
        $dto->email = $user->getUserIdentifier();
        $dto->name = $user->getName();
        $dto->permissionsText = implode(separator: ', ', array: $permissions);
        $dto->groupsText = implode(separator: ', ', array: $groups);

        return $dto;
    }

    private function mapElementToDto(UserEntity $entity): User
    {
        $dto = new User();
        $dto->id = $entity->getId();
        $dto->email = $entity->getUserIdentifier();
        $dto->firstname = $entity->getFirstname();
        $dto->lastname = $entity->getLastname();

        $dto->permissions = array_map(
            callback: fn (Permission $permission): string => $permission->value,
            array: $entity->getPermissions(),
        );

        $selectedGroups = [];

        foreach ($entity->getGroups() as $group) {
            $selectedGroup = new UserSelected();
            $selectedGroup->id = $group->getId();

            $selectedGroups[] = $selectedGroup;
        }

        $dto->groups = $selectedGroups;

        return $dto;
    }
}
