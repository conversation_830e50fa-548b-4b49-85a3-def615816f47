<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Invoice as InvoiceEntity;
use App\Domain\Repository\InvoiceRepository;
use App\Infrastructure\Portal\Customer\Resource\Invoice;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class InvoiceEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private InvoiceRepository $invoiceRepository,
    ) {
    }

    /**
     * @return PaginatedCollection<Invoice>
     *
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $invoices = $this->invoiceRepository->findByUserSearchCriteria(userSearchCriteria: $userSearchCriteria, businessPartnerId: $businessPartnerId);
        $items = [];
        foreach ($invoices as $invoice) {
            $items[] = $this->mapToDto(invoice: $invoice);
        }

        return new PaginatedCollection(
            totalItems: $invoices->count(),
            items: $items
        );
    }

    private function mapToDto(InvoiceEntity $invoice): Invoice
    {
        $dto = new Invoice();
        $dto->id = $invoice->getId();
        $dto->date = $invoice->getBillingDate()->format(format: 'd.m.Y');
        $dto->documentNumber = $invoice->getExtId();
        $dto->documentType = $invoice->getType();
        $dto->netAmount = $invoice->getNetValue().' '.$invoice->getCurrency();
        $dto->filePath = 'sample.pdf'; // TODO

        return $dto;
    }
}
