<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\ContractServiceLocation as ContractServiceLocationEntity;
use App\Domain\Entity\Enum\MaterialType;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\ContractPositionRepository;
use App\Domain\Repository\ContractRepository;
use App\Domain\Repository\ContractServiceLocationRepository;
use App\Domain\Repository\ServiceLocationRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\ContractServiceLocation;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ContractServiceLocationEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private ContractRepository $contractRepository,
        private ContractServiceLocationRepository $contractServiceLocationRepository,
        private BusinessPartnerRepository $businessPartnerRepository,
        private ContractPositionRepository $contractPositionRepository,
        private ServiceLocationRepository $serviceLocationRepository,
    ) {
    }

    /**
     * @return PaginatedCollection<ContractServiceLocation>
     *
     * @throws \Exception
     */
    public function getCollection(Request $request, string $id): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        // Get the business partner to use its external ID
        $businessPartner = $this->businessPartnerRepository->findOneBy(criteria: ['id' => $businessPartnerId]); // TODO: remove that later and change further process to bp uuid
        if (null === $businessPartner) {
            throw new NotFoundException(message: 'BusinessPartner not found');
        }

        // First verify that the contract exists and belongs to the business partner
        $contract = $this->contractRepository->findOneByIdAndBusinessPartner(id: $id, businessPartnerId: $businessPartnerId);
        if (null === $contract) {
            throw new NotFoundException(message: 'Contract not found');
        }

        // Get contract service locations using the contract's extId with search criteria
        // Use the business partner's external ID for the database query
        $contractServiceLocations = $this->contractServiceLocationRepository->findByUserSearchCriteria(
            userSearchCriteria: $userSearchCriteria,
            contractId: $contract->getExtId(), // TODO: remove that later and change further process to contract uuid
            businessPartnerId: $businessPartner->getExtId() // TODO: remove that later and change further process to bp uuid
        );

        $items = [];
        foreach ($contractServiceLocations as $contractServiceLocation) {
            $items[] = $this->mapToDto(contractServiceLocation: $contractServiceLocation);
        }

        return new PaginatedCollection(
            totalItems: $contractServiceLocations->count(),
            items: $items,
        );
    }

    private function mapToDto(ContractServiceLocationEntity $contractServiceLocation): ContractServiceLocation
    {
        $materialPos = $this->contractPositionRepository->findOneByExtContractIdAndExtMaterialId(
            extContractId: $contractServiceLocation->getExtContractId(),
            extMaterialId: $contractServiceLocation->getExtContainerMaterialId(),
            materialType: MaterialType::CONTAINER->value,
        );

        $serviceLocation = $this->serviceLocationRepository->findOneBy(criteria: [
            'extId' => $contractServiceLocation->getExtServiceLocationId(),
        ]);

        $dto = new ContractServiceLocation();
        $dto->id = $contractServiceLocation->getId();
        $dto->containerServiceLocationId = $contractServiceLocation->getExtId();
        $dto->container = $materialPos?->getMaterialText() ?? '';
        $dto->serviceLocation = $serviceLocation?->getAddress() ?? '';
        $dto->quantity = 1.0; // Default quantity as this field doesn't exist in the entity

        return $dto;
    }
}
