<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\SupervisionDocumentStatus;
use App\Domain\Entity\SignData as SignDataEntity;
use App\Domain\Entity\SupervisionDocument as SupervisionDocumentEntity;
use App\Domain\Entity\SupervisionDocumentPartner as SupervisionDocumentPartnerEntity;
use App\Domain\Repository\SignDataRepository;
use App\Domain\Repository\SupervisionDocumentPartnerRepository;
use App\Domain\Repository\SupervisionDocumentRepository;
use App\Domain\Repository\SupervisionDocumentSignDataRepository;
use App\Infrastructure\Portal\Customer\Resource\Dto\SignData;
use App\Infrastructure\Portal\Customer\Resource\SupervisionDocument;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class SupervisionDocumentEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private SupervisionDocumentRepository $supervisionDocumentRepository,
        private SupervisionDocumentPartnerRepository $supervisionDocumentPartnerRepository,
        private SignDataRepository $signDataRepository,
        private SupervisionDocumentSignDataRepository $supervisionDocumentSignDataRepository,
    ) {
    }

    private function getResource(?string $id = null): SupervisionDocument
    {
        $resource = new SupervisionDocument();
        $resource->id = $id ?? (string) mt_rand();
        $resource->date = (string) mt_rand();
        $resource->certificateNumber = (string) mt_rand();
        $resource->avvId = (string) mt_rand();
        $resource->description = (string) mt_rand();
        $resource->amount = (string) mt_rand();
        $resource->status = (string) mt_rand();
        $resource->supervisionDocumentNumber = (string) mt_rand();
        $resource->producer = $this->getSignData();
        $resource->carrier = $this->getSignData();
        $resource->disposer = $this->getSignData();

        return $resource;
    }

    private function getSignData(): SignData
    {
        $signData = new SignData();
        $signData->id = (string) mt_rand();
        $signData->type = (string) mt_rand();
        $signData->amount = (string) mt_rand();
        $signData->name = (string) mt_rand();
        $signData->date = (string) mt_rand();
        $signData->city = (string) mt_rand();
        $signData->postalCode = (string) mt_rand();
        $signData->street = (string) mt_rand();
        $signData->licencePlateNumber = (string) mt_rand();
        $signData->trailerLicencePlateNumber = (string) mt_rand();
        $signData->status = (string) mt_rand();
        $signData->signer = (string) mt_rand();
        $signData->signDate = (string) mt_rand();

        return $signData;
    }

    /**
     * @return PaginatedCollection<SupervisionDocument>
     *
     * @throws QueryException
     * @throws \DateMalformedStringException
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $paginator = $this->supervisionDocumentRepository->findByUserSearchCriteria(userSearchCriteria: $userSearchCriteria, businessPartnerId: $businessPartnerId);
        $items = array_map(
            callback: fn (SupervisionDocumentEntity $supervisionDocument): SupervisionDocument => $this->mapToDto(supervisionDocument: $supervisionDocument),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    public function get(Request $request, string $id): SupervisionDocument
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $supervisionDocument = $this->supervisionDocumentRepository->findOneByIdAndBusinessPartner(id: $id, businessPartnerId: $businessPartnerId);

        if (null === $supervisionDocument) {
            throw new NotFoundHttpException(message: "Supervision Document $id not found");
        }

        return $this->mapToDto(supervisionDocument: $supervisionDocument);
    }

    // TODO: sync
    public function sync(): SupervisionDocument
    {
        return $this->getResource(id: (string) mt_rand());
    }

    private function mapToDto(SupervisionDocumentEntity $supervisionDocument): SupervisionDocument
    {
        $role = $supervisionDocument->getRole();
        $status = $supervisionDocument->getStatus();
        $combined = $status.'_'.$role;
        $frontendStatus = SupervisionDocumentStatus::from(value: $combined)->getFrontendStatus();

        $dto = new SupervisionDocument();
        $dto->id = $supervisionDocument->getId();
        $dto->date = $supervisionDocument->getOrderDate()->format(format: 'd.m.Y');
        $dto->certificateNumber = $supervisionDocument->getCertificateNumber();
        $dto->avvId = $supervisionDocument->getAvvId();
        $dto->description = $supervisionDocument->getDescription();
        $dto->amount = $supervisionDocument->getAmount().' '.$supervisionDocument->getAmountUnitOm();
        $dto->status = $frontendStatus ?? '';
        $dto->statusText = sprintf('{{%s}}', $frontendStatus);
        $dto->supervisionDocumentNumber = $supervisionDocument->getExtId();

        $supervisionDocumentPartners = $this->supervisionDocumentPartnerRepository->findAllByExtSupervisionDocumentId(id: $supervisionDocument->getExtId());
        $supervisionDocumentSignData = $this->supervisionDocumentSignDataRepository->findAllBySupervisionDocumentId(id: $supervisionDocument->getId());

        $signData = [];
        foreach ($supervisionDocumentSignData as $data) {
            $signData = array_merge(
                $signData,
                $this->signDataRepository->findAllByExtId(id: $data->getExtSignDataId())
            );
        }

        if (([] !== $supervisionDocumentPartners) && ([] !== $signData)) {
            foreach ($supervisionDocumentPartners as $partner) {
                foreach ($signData as $sign) {
                    if (str_contains(haystack: $partner->getType(), needle: 'ERZ') && str_contains(haystack: $sign->getRole(), needle: 'ERZ')) {
                        $dto->producer = new SignData();
                        $this->mapToSignDataDto(supervisionDocumentPartner: $partner, signData: $sign, dto: $dto->producer);
                    }

                    if (str_contains(haystack: $partner->getType(), needle: 'BEF') && str_contains(haystack: $sign->getRole(), needle: 'BEF')) {
                        $dto->carrier = new SignData();
                        $this->mapToSignDataDto(supervisionDocumentPartner: $partner, signData: $sign, dto: $dto->carrier);
                    }

                    if (str_contains(haystack: $partner->getType(), needle: 'ENT') && str_contains(haystack: $sign->getRole(), needle: 'ENT')) {
                        $dto->disposer = new SignData();
                        $this->mapToSignDataDto(supervisionDocumentPartner: $partner, signData: $sign, dto: $dto->disposer);
                    }
                }
            }
        }

        return $dto;
    }

    private function mapToSignDataDto(SupervisionDocumentPartnerEntity $supervisionDocumentPartner, SignDataEntity $signData, SignData &$dto): void
    {
        $status = $signData->getStatus();

        $dto->id = $signData->getId();
        $dto->type = $supervisionDocumentPartner->getType();
        $dto->amount = $supervisionDocumentPartner->getAmount().' '.$supervisionDocumentPartner->getAmountUnitOm();
        $dto->name = $supervisionDocumentPartner->getName();
        $dto->date = $supervisionDocumentPartner->getTakeoverDate()->format(format: 'd.m.Y');
        $dto->city = $supervisionDocumentPartner->getCity();
        $dto->postalCode = $supervisionDocumentPartner->getPostalCode();
        $dto->street = $supervisionDocumentPartner->getStreet();
        $dto->licencePlateNumber = $supervisionDocumentPartner->getLicencePlateNumber1();
        $dto->trailerLicencePlateNumber = $supervisionDocumentPartner->getLicencePlateNumber2();
        $dto->status = $status;
        $dto->statusText = sprintf('{{%s}}', $status);
        $dto->signer = $signData->getSigner();
        $dto->signDate = $signData->getSignDateTime()->format(format: 'd.m.Y');
    }
}
