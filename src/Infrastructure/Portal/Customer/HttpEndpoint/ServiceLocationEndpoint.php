<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\ServiceLocationRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\ServiceLocation;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ServiceLocationEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private ServiceLocationRepository $serviceLocationRepository,
        private BusinessPartnerRepository $businessPartnerRepository,
    ) {
    }

    /**
     * @return Collection<ServiceLocation>
     *
     * @throws \Exception
     */
    public function getOptions(Request $request): Collection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        // Get the business partner to use its external ID
        $businessPartner = $this->businessPartnerRepository->findOneBy(criteria: ['id' => $businessPartnerId]);
        if (null === $businessPartner) {
            throw new NotFoundException(message: 'BusinessPartner not found');
        }

        // Get service locations that are associated with this business partner
        $serviceLocations = $this->serviceLocationRepository->findAllByBusinessPartnerAndUserSearchCriteria(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerExtId: $businessPartner->getExtId(),
        );

        $items = [];
        foreach ($serviceLocations as $serviceLocation) {
            $additionalText = $serviceLocation->getAdditionalInformation()
                ? ' - '.$serviceLocation->getAdditionalInformation()
                : '';

            $dto = new ServiceLocation();
            $dto->id = $serviceLocation->getId();
            $dto->display = $serviceLocation->getExtId().' - '.$serviceLocation->getName().$additionalText;
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }
}
