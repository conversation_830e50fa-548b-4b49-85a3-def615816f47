<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Service\ObjectStorage\FileObject;
use App\Domain\Service\ObjectStorage\ObjectMetadata;
use App\Domain\Service\ObjectStorage\ObjectRepositoryInterface;
use App\Domain\Service\ObjectStorage\ObjectStorageException;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;
use Symfony\Component\Mime\MimeTypes;

#[AsController]
readonly class FileEndpoint
{
    public function __construct(
        private ObjectRepositoryInterface $objectRepository,
        private Security $security,
        private LoggerInterface $logger,
    ) {
    }

    public function get(string $id): Response
    {
        try {
            $fileObject = $this->objectRepository->get($id);
        } catch (ObjectStorageException $e) {
            $this->logger->critical(
                'Could not get a file from object storage',
                ['exception' => $e]
            );

            throw new ServiceUnavailableHttpException();
        }

        if (null === $fileObject) {
            throw new NotFoundHttpException();
        }

        // TODO: Implement file access control
        // if (!$this->security->canAccessFileObject($fileObject)) {
        //    throw new AccessDeniedException();
        // }

        $disposition = HeaderUtils::makeDisposition(
            disposition: HeaderUtils::DISPOSITION_ATTACHMENT,
            filename: basename(path: $fileObject->identifier),
        );

        return new Response(
            content: $fileObject->content,
            status: Response::HTTP_OK,
            headers: [
                'Content-Type' => $fileObject->mimeType,
                'Content-Disposition' => $disposition,
            ],
        );
    }

    public function put(string $id, Request $request): Response
    {
        try {
            $exists = $this->objectRepository->exists($id);

            if ($exists) {
                throw new ConflictHttpException(message: 'File ID conflict');
            }
        } catch (ObjectStorageException $e) {
            $this->logger->critical(
                'Could not check if a file exists in object storage',
                ['exception' => $e]
            );

            throw new ServiceUnavailableHttpException();
        }

        $fileContents = $request->getContent();

        $tempFileName = tempnam(directory: sys_get_temp_dir(), prefix: 'upload-temp-');
        file_put_contents(filename: $tempFileName, data: $fileContents);

        $mimeTypes = new MimeTypes();
        $mimeType = $mimeTypes->guessMimeType(path: $tempFileName);

        $fileObject = new FileObject(
            identifier: $id,
            content: $fileContents,
            mimeType: $mimeType ?? 'application/octet-stream',
            objectMetadata: new ObjectMetadata(
                // tenantIdentifier: $this->tenantContext->getTenantIdentifier(),
                userIdentifier: $this->security->getUser()?->getUserIdentifier(),
            ),
        );

        try {
            $this->objectRepository->store($fileObject);
        } catch (ObjectStorageException $e) {
            $this->logger->critical(
                'Could not put a file to object storage',
                ['exception' => $e]
            );

            throw new ServiceUnavailableHttpException();
        } finally {
            unlink(filename: $tempFileName);
        }

        return new Response(status: Response::HTTP_CREATED);
    }
}
