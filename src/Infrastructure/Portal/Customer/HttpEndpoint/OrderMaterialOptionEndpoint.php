<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\MaterialType;
use App\Domain\Repository\ContractPositionRepository;
use App\Domain\Repository\ContractRepository;
use App\Infrastructure\Portal\Customer\Resource\OrderMaterialOption;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class OrderMaterialOptionEndpoint
{
    public function __construct(
        private ContractPositionRepository $contractPositionRepository,
        private ContractRepository $contractRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
    ) {
    }

    /**
     * @return Collection<OrderMaterialOption>
     *
     * @throws \Exception
     */
    public function getOptions(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        /** @var string $contractId */
        $contractId = $userSearchCriteria->filters['contractId']->value;

        $contract = $this->contractRepository->find(id: $contractId);
        if (null === $contract) {
            return new Collection(items: []);
        }

        $items = [];
        $materials = $this->contractPositionRepository->findAllByContractIdAndMaterialType(
            contractId: $contractId,
            materialType: MaterialType::WASTE->value,
        );

        foreach ($materials as $material) {
            $dto = new OrderMaterialOption();
            $dto->id = $material->getId();
            $dto->display = $material->getMaterialText();
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }
}
