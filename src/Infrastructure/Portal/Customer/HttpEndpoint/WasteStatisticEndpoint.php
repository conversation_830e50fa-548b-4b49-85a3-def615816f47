<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\WasteStatisticLocationRepository;
use App\Domain\Repository\WasteStatisticRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Framework\Serializer\SerializerInterface;
use App\Infrastructure\Portal\Customer\Resource\Dto\AvvWasteReport;
use App\Infrastructure\Portal\Customer\Resource\Dto\ContractWasteReport;
use App\Infrastructure\Portal\Customer\Resource\WasteStatistic;
use App\Infrastructure\SapEurope\Client\SapClientConfig;
use App\Infrastructure\SapEurope\Client\SapGetClient;
use App\Infrastructure\SapEurope\Resource\WasteStatistic as SapWasteStatistic;
use App\Infrastructure\SapEurope\Service\WasteStatisticService;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class WasteStatisticEndpoint
{
    public function __construct(
        private BusinessPartnerRepository $businessPartnerRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private WasteStatisticRepository $wasteStatisticRepository,
        private WasteStatisticLocationRepository $wasteStatisticLocationRepository,
        private SapGetClient $getClient,
        private SapClientConfig $sapClientConfig,
        private WasteStatisticService $wasteStatisticService,
        private SerializerInterface $serializer,
        private LoggerInterface $logger,
    ) {
    }

    public function fromSap(Request $request): void
    {
        $businessPartnerId = $request->request->getString(key: 'businessPartnerId');
        $dateFrom = $request->request->getString(key: 'dateFrom');
        $dateTo = $request->request->getString(key: 'dateTo');

        $url = $this->sapClientConfig->getWasteStatisticUrl();
        $query = [
            'businessPartnerId' => $businessPartnerId,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ];

        $json = $this->getClient->requestJsonResponse(url: $url, query: $query);

        try {
            /** @var array<SapWasteStatistic> $wasteStatistics */
            $wasteStatistics = $this->serializer->denormalize(
                data: $json,
                type: SapWasteStatistic::class.'[]'
            );

            $this->wasteStatisticService->upsertMany(wasteStatistics: $wasteStatistics, businessPartnerId: $businessPartnerId);
        } catch (\Exception $e) {
            $this->logger->critical(
                message: 'Waste Statistics could not be imported, cannot deserialize request data',
                context: ['exception' => $e]
            );
        }
    }

    public function export(Request $request): Response
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $businessPartner = $this->businessPartnerRepository->findOneBy(criteria: ['id' => $businessPartnerId]);
        if (null === $businessPartner) {
            throw new NotFoundException(message: "BusinessPartner $businessPartnerId not found!");
        }

        $wasteStatistics = $this->wasteStatisticRepository->findAllByUserSearchCriteriaAndBusinessPartner(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartner->getExtId()
        );

        $csv = fopen(filename: 'php://temp', mode: 'w');
        if (false === $csv) {
            throw new \RuntimeException(message: 'Failed to export Waste Statistic CSV file!');
        }

        foreach ($wasteStatistics as $wasteStatistic) {
            if (null === $wasteStatistic->getExtWasteStatisticLocationId()) {
                continue;
            }

            $wasteStatisticLocation = $this->wasteStatisticLocationRepository->findOneByExtWasteStatisticLocationIdAndBusinessPartner(
                extWasteStatisticLocationId: $wasteStatistic->getExtWasteStatisticLocationId(),
                businessPartnerId: $businessPartner->getExtId()
            );
            if (null === $wasteStatisticLocation) {
                throw new NotFoundException(message: sprintf('Waste Statistic Location %s not found!', $wasteStatistic->getExtWasteStatisticLocationId()));
            }

            fputcsv(stream: $csv, fields: [
                $wasteStatistic->getExtBusinessPartnerId(),
                $wasteStatistic->getExtWasteStatisticLocationId(),
                $wasteStatistic->getExtContractId(),
                $wasteStatistic->getExtMaterialId(),
                $wasteStatistic->getAvvId(),
                $wasteStatistic->getAvvText(),
                $wasteStatistic->getOrderDate()->format(format: 'Y-m-d H:i:s'),
                $wasteStatisticLocation->getName(),
                $wasteStatisticLocation->getAddress(),
                $wasteStatisticLocation->getInfo(),
                $wasteStatistic->getAmount().' '.$wasteStatistic->getUnitOm(),
                $wasteStatistic->getNet().' '.$wasteStatistic->getCurrency(),
            ], escape: '',
            );
        }

        // TODO: Implement file access control
        // if (!$this->security->canAccessFileObject($fileObject)) {
        //    throw new AccessDeniedException();
        // }

        $disposition = HeaderUtils::makeDisposition(
            disposition: HeaderUtils::DISPOSITION_ATTACHMENT,
            filename: 'WasteStatistics.csv',
        );

        rewind(stream: $csv);
        $response = new Response(
            content: stream_get_contents(stream: $csv),
            status: Response::HTTP_OK,
            headers: [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => $disposition,
            ],
        );

        fclose(stream: $csv);

        return $response;
    }

    public function get(Request $request, string $id): WasteStatistic
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $businessPartner = $this->businessPartnerRepository->findOneBy(criteria: ['id' => $businessPartnerId]);
        if (null === $businessPartner) {
            throw new NotFoundException(message: "BusinessPartner $businessPartnerId not found!");
        }

        $wasteStatisticLocation = $this->wasteStatisticLocationRepository->find(id: $id);
        if (null === $wasteStatisticLocation) {
            throw new NotFoundHttpException(message: "Waste Statistic Location $id not found!");
        }

        $wasteStatistics = $this->wasteStatisticRepository->findAllByExtWasteStatisticLocationIdAndBusinessPartner(
            extWasteStatisticLocationId: $wasteStatisticLocation->getExtId() ?? '',
            businessPartnerId: $businessPartner->getExtId()
        );
        if ([] === $wasteStatistics) {
            throw new NotFoundException(message: sprintf('No waste statistic with extWasteStatisticLocationId %s found!', $wasteStatisticLocation->getExtId()));
        }

        $dto = new WasteStatistic();
        $dto->id = $wasteStatisticLocation->getId();

        foreach ($wasteStatistics as $wasteStatistic) {
            $avvWasteReport = new AvvWasteReport();
            $avvWasteReport->avvId = $wasteStatistic->getAvvId();
            $avvWasteReport->material = $wasteStatistic->getMaterialText();
            $avvWasteReport->contract = $wasteStatistic->getExtContractId() ?? null;
            $avvWasteReport->amount = $wasteStatistic->getAmount().' '.$wasteStatistic->getUnitOm();
            $dto->avvWasteReport[] = $avvWasteReport;

            $contractWasteReport = new ContractWasteReport();
            $contractWasteReport->avvId = $wasteStatistic->getAvvId();
            $contractWasteReport->material = $wasteStatistic->getMaterialText();
            $contractWasteReport->contract = $wasteStatistic->getExtContractId() ?? null;
            $contractWasteReport->amount = $wasteStatistic->getAmount().' '.$wasteStatistic->getUnitOm();
            $dto->contractWasteReport[] = $contractWasteReport;
        }

        return $dto;
    }

    /**
     * @return PaginatedCollection<WasteStatistic>
     *
     * @throws QueryException
     * @throws \DateMalformedStringException
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $businessPartner = $this->businessPartnerRepository->findOneBy(criteria: ['id' => $businessPartnerId]);
        if (null === $businessPartner) {
            throw new NotFoundException(message: "BusinessPartner $businessPartnerId not found!");
        }

        $paginator = $this->wasteStatisticLocationRepository->findAllByUserSearchCriteriaAndBusinessPartner(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartner->getExtId(),
        );

        $items = [];

        foreach ($paginator as $wasteStatisticLocation) {
            $dto = new WasteStatistic();
            $dto->id = $wasteStatisticLocation->getId();
            $dto->serviceLocationName = $wasteStatisticLocation->getName() ?? null;
            $dto->serviceLocationNumber = $wasteStatisticLocation->getExtId() ?? null;

            $totalAmount = $this->wasteStatisticRepository->getTotalAmountByUserSearchCriteriaBusinessPartnerAndExtWasteStatisticLocationId(
                userSearchCriteria: $userSearchCriteria,
                businessPartnerId: $businessPartner->getExtId(),
                extWasteStatisticLocationId: $wasteStatisticLocation->getExtId() ?? ''
            );

            $dto->totalQuantity = $totalAmount.' t';
            $items[] = $dto;
        }

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }
}
