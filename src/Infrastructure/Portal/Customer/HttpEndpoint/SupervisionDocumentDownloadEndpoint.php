<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Infrastructure\Portal\Customer\Resource\SupervisionDocumentDownload;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class SupervisionDocumentDownloadEndpoint
{
    /**
     * @return Collection<SupervisionDocumentDownload>
     */
    public function getOptions(): Collection
    {
        $items = [];
        // TODO
        $download = new SupervisionDocumentDownload();
        $download->type = 'SEC_SIGNER';
        $download->filePath = 'secsigner.exe';
        $items[] = $download;

        $download = new SupervisionDocumentDownload();
        $download->type = 'BMU_VIEWER';
        $download->filePath = 'bmu.exe';
        $items[] = $download;

        return new Collection(
            items: $items
        );
    }
}
