<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\Locale;
use App\Domain\Service\Translation\TranslationInterface;
use App\Domain\Service\Translation\TranslationServiceInterface;
use App\Domain\Service\Translation\TranslationSystem;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\Translation;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class TranslationEndpoint
{
    public function __construct(
        private TranslationServiceInterface $translationService,
    ) {
    }

    /**
     * @return Collection<Translation>
     */
    public function getLocales(): Collection
    {
        return new Collection(
            items: array_map(
                callback: function (Locale $locale): Translation {
                    $ret = new Translation();
                    $ret->iso = $locale->value;
                    $ret->name = $locale->getCountryLabel();

                    return $ret;
                },
                array: Locale::cases(),
            )
        );
    }

    /**
     * @return Collection<Translation>
     */
    public function getTranslationsByIso(string $iso): Collection
    {
        $locale = Locale::tryFrom(value: $iso);

        if (null === $locale) {
            throw new NotFoundException(message: 'Iso '.$iso.' not found');
        }

        try {
            $translationArray = array_map(
                callback: fn (TranslationInterface $translation): Translation => $this->mapToDto(translation: $translation),
                array: $this->translationService->getTranslationList(TranslationSystem::PORTAL, $locale),
            );
        } catch (NotFoundException) {
            throw new NotFoundException(message: 'Translation not found');
        }

        return new Collection(
            items: $translationArray,
        );
    }

    private function mapToDto(TranslationInterface $translation): Translation
    {
        $translationDto = new Translation();

        $translationDto->key = $translation->getKey();
        $translationDto->value = $translation->getValue();

        return $translationDto;
    }
}
