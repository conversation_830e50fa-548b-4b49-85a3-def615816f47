<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\NoticeTextType;
use App\Domain\Entity\Enum\OrderStatus;
use App\Domain\Entity\Enum\ServiceType;
use App\Domain\Entity\NoticeText;
use App\Domain\Entity\Order as OrderEntity;
use App\Domain\Entity\Service as ServiceEntity;
use App\Domain\Repository\ContractPartnerRepository;
use App\Domain\Repository\ContractPositionRepository;
use App\Domain\Repository\ContractRepository;
use App\Domain\Repository\ContractServiceLocationRepository;
use App\Domain\Repository\NoticeTextRepository;
use App\Domain\Repository\OrderAgreementRepository;
use App\Domain\Repository\OrderRepository;
use App\Domain\Repository\ServiceLocationRepository;
use App\Domain\Repository\ServiceRepository;
use App\Domain\Service\OrderActivityService;
use App\Domain\Service\OrderService;
use App\Exception\NotFoundException;
use App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUser;
use App\Infrastructure\MessageQueue\Order\OrderCreated;
use App\Infrastructure\Portal\Customer\Resource\Dto\Document;
use App\Infrastructure\Portal\Customer\Resource\Order;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Psr\Log\LoggerInterface;
use Random\RandomException;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Messenger\Exception\ExceptionInterface;
use Symfony\Component\Messenger\MessageBusInterface;

#[AsController]
readonly class OrderEndpoint
{
    public function __construct(
        private ContractPartnerRepository $contractPartnerRepository,
        private ContractPositionRepository $contractPositionRepository,
        private ContractServiceLocationRepository $contractServiceLocationRepository,
        private LoggerInterface $logger,
        private NoticeTextRepository $noticeTextRepository,
        private OrderActivityService $orderActivityService,
        private OrderRepository $orderRepository,
        private OrderService $orderService,
        private Security $security,
        private ServiceLocationRepository $serviceLocationRepository,
        private ServiceRepository $serviceRepository,
        private OrderAgreementRepository $orderAgreementRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private ContractRepository $contractRepository,
        private MessageBusInterface $messageBus,
    ) {
    }

    public function get(Request $request, string $id): Order
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $order = $this->orderRepository->findOneByIdAndBusinessPartnerId(id: $id, businessPartnerId: $businessPartnerId);

        if (null === $order) {
            throw new NotFoundHttpException();
        }

        return $this->mapToDto(order: $order);
    }

    /**
     * @return PaginatedCollection<Order>
     *
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $paginator = $this->orderRepository->findByUserSearchCriteria(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartnerId,
        );

        $items = [];

        foreach ($paginator->getIterator() as $order) {
            try {
                $items[] = $this->mapToDto(order: $order);
            } catch (NotFoundException $exception) {
                $this->logger->error($exception->getMessage(), ['exception' => $exception]);
            }
        }

        return new PaginatedCollection(
            totalItems: count(value: $items),
            items: $items,
        );
    }

    /**
     * @throws NotFoundException
     * @throws \DateMalformedStringException
     * @throws RandomException
     */
    public function create(Request $request, Order $order): Order
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        $contractPartners = $this->contractPartnerRepository->findByContractAndBusinessPartner(
            contractId: $order->contractId,
            businessPartnerId: $businessPartnerId,
        );

        if ([] === $contractPartners) {
            throw new NotFoundException(message: 'No contract partner found for contract '.$order->contractId);
        }

        $contract = $this->contractRepository->find(id: $order->contractId);
        if (null === $contract) {
            throw new NotFoundException(message: 'Contract '.$order->contractId.' not found');
        }

        $contractPosition = $this->contractPositionRepository->find(id: $order->materialId);
        if (null === $contractPosition) {
            throw new NotFoundException(message: 'No contract position found for material Id '.$order->materialId);
        }

        $serviceLocation = $this->serviceLocationRepository->find(id: $order->serviceLocationId);
        if (null === $serviceLocation) {
            throw new NotFoundException(message: 'Service location '.$order->serviceLocationId.' not found');
        }

        $contractServiceLocation = $this->contractServiceLocationRepository->findOneBy(criteria: [
            'extServiceLocationId' => $serviceLocation->getExtId(),
            'extContractId' => $contract->getExtId(),
        ]); // TODO
        if (null === $contractServiceLocation) {
            throw new NotFoundException(message: 'No contract service location found for service location '.$order->serviceLocationId);
        }

        $service = new ServiceEntity();
        $service->setExtId(extId: null);
        $service->setExtPosId(extPosId: null);
        $service->setExtWasteMaterialId(extWasteMaterialId: $contractPosition->getExtMaterialId());
        $service->setServiceType(serviceType: ServiceType::from(value: $order->serviceType)->value);
        $service->setContainerCount(containerCount: $order->amount);
        $service->setStartDate(startDate: new \DateTimeImmutable(datetime: $order->date));
        $service->setEndDate(endDate: new \DateTimeImmutable(datetime: $order->date));
        $service->setPeriod(period: '1'); // TODO
        $service->setExtContractId(extContractId: $contract->getExtId());
        $service->setExtContractPosId(extContractPosId: '10'); // TODO
        $service->setExtContainerMaterialId(extContainerMaterialId: $contractServiceLocation->getExtContainerMaterialId()); // TODO
        $service->setExtServiceLocationId(extServiceLocationId: $serviceLocation->getExtId());
        $service->setEquipment(equipment: $contractServiceLocation->getEquipment()); // TODO
        $service->setExtRouteId(extRouteId: $contract->getExtRouteId() ?? ''); // TODO
        $service->setServiceFrequency(serviceFrequency: 'D');
        $service->setDailyFrequency(dailyFrequency: '1');
        $service->setWeeklyFrequency(weeklyFrequency: null); // TODO
        $service->setMonthlyFrequency(monthlyFrequency: null); // TODO
        $service->setMonthlyFrequencyDay(monthlyFrequencyDay: null); // TODO
        $service->setWeekday(weekday: null); // TODO

        $noticeText = new NoticeText();
        $noticeText->setExtId(extId: null);
        $noticeText->setExtPosId(extPosId: null);
        $noticeText->setServiceId(serviceId: $service->getId());
        $noticeText->setContent(content: $order->adrText);
        $noticeText->setType(type: NoticeTextType::DRIVER_INFO->value);
        $noticeText->setEndDate(endDate: $service->getEndDate());

        $user = $this->security->getUser();
        if (!$user instanceof KeycloakUser) {
            throw new UnauthorizedHttpException(challenge: 'Bearer');
        }

        $entity = new OrderEntity();
        $entity->setExtPosId(extPosId: null);
        $entity->setExtId(extId: null);
        $entity->setExtServiceId(extServiceId: null); // TODO
        $entity->setExtObjectId(extObjectId: (string) random_int(min: 100000, max: 999999)); // TODO
        $entity->setOrderDate(orderDate: new \DateTimeImmutable(datetime: $order->date));
        $entity->setBusinessPartnerId(businessPartnerId: $businessPartnerId);
        $entity->setOrderStatus(orderStatus: OrderStatus::CREATED->value);
        $entity->setEmail(email: $user->getEmail());

        $this->orderRepository->save(entity: $entity);
        $this->serviceRepository->save(entity: $service);
        $this->noticeTextRepository->save(entity: $noticeText);

        // send Order (Service) to SAP
        try {
            $this->messageBus->dispatch(new OrderCreated(
                serviceId: $service->getId(),
                noticeTextId: $noticeText->getId(),
                orderId: $entity->getId(),
            ));
        } catch (ExceptionInterface $e) {
            $this->logger->error($e->getMessage(), ['e' => $e]);
        }

        $order->id = $entity->getId();
        $order->status = $entity->getOrderStatus();
        $order->material = '';
        $order->container = '';
        $order->serviceLocation = '';
        $order->service = '';
        $order->driverInformation = $order->adrText;
        $order->display = '';
        $order->required = false;

        $this->orderActivityService->createEntry(order: $entity);

        return $order;
    }

    public function cancel(Request $request, string $id): Order
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $entity = $this->orderRepository->findOneByIdAndBusinessPartnerId(id: $id, businessPartnerId: $businessPartnerId);

        if (null === $entity) {
            throw new NotFoundException(message: 'Order '.$id.' not found');
        }

        // TODO cancel order in SAP.
        $entity->setOrderStatus(orderStatus: OrderStatus::CANCELED->value);
        $this->orderRepository->save(entity: $entity);

        $order = new Order();
        $order->id = $entity->getId();

        return $order;
    }

    /**
     * @throws NotFoundException
     */
    private function mapToDto(OrderEntity $order): Order
    {
        $businessPartner = $this->orderService->getBusinessPartner(order: $order);
        $frontendStatus = OrderStatus::from(value: $order->getOrderStatus())->getFrontendStatus();

        $dto = new Order();
        $dto->id = $order->getId();
        $dto->date = $order->getOrderDate()->format(format: 'd.m.Y');
        $dto->status = $frontendStatus;
        $dto->statusText = sprintf('{{%s}}', $frontendStatus);

        $service = $this->orderService->getService(order: $order);
        if (null !== $service) {
            $serviceLocation = $this->serviceLocationRepository->findOneByExtIdAndExtBusinessPartnerId(
                extId: $service->getExtServiceLocationId(),
                extBusinessPartnerId: $businessPartner->getExtId(),
            );

            $serviceLocationAddress = '';
            if (null !== $serviceLocation) {
                $serviceLocationAddress = null !== $serviceLocation->getAdditionalInformation()
                    ? sprintf('%s - %s', $serviceLocation->getAddress(), $serviceLocation->getAdditionalInformation())
                    : $serviceLocation->getAddress();
                // throw new NotFoundHttpException(message: 'Service location '.$service->getExtServiceLocationId().' not found!');
                // TODO
            }

            $materialPos = $this->contractPositionRepository->findOneByExtContractIdAndExtMaterialId(
                extContractId: $service->getExtContractId(),
                extMaterialId: $service->getExtWasteMaterialId(),
            );

            $containerPos = $this->contractPositionRepository->findOneByExtContractIdAndExtMaterialId(
                extContractId: $service->getExtContractId(),
                extMaterialId: $service->getExtContainerMaterialId(),
            );

            $noticeText = $this->noticeTextRepository->findOnyByServiceIdAndType(
                serviceId: $service->getId(),
                noticeTextType: NoticeTextType::DRIVER_INFO,
            );

            $serviceText = strtolower(string: ServiceType::from(value: $service->getServiceType())->name);

            $dto->material = $materialPos?->getMaterialText() ?? null;
            $dto->container = $containerPos?->getMaterialText() ?? null;
            $dto->serviceLocation = $serviceLocationAddress;
            $dto->service = sprintf('{{%s}}', $serviceText);
            $dto->driverInformation = $noticeText?->getContent() ?? null;
        } else {
            $dto->material = null;
            $dto->container = null;
            $dto->serviceLocation = null;
            $dto->service = null;
            $dto->driverInformation = null;
        }

        // TODO
        $document = new Document();
        $document->type = 'DELIVERY_NOTE';
        $document->name = '{{delivery_note}}';
        $document->filePath = 'sample.pdf'; // TODO
        $dto->documents[] = $document;

        $document = new Document();
        $document->type = 'WEIGHING_NOTE';
        $document->name = '{{weighing_note}}';
        $document->filePath = 'sample.pdf'; // TODO
        $dto->documents[] = $document;

        return $dto;
    }

    /**
     * @return Collection<Order>
     */
    public function getContainerOptions(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        /** @var string $serviceTypeValue */
        $serviceTypeValue = $userSearchCriteria->filters['serviceType']->value;
        /** @var string $contractId */
        $contractId = $userSearchCriteria->filters['contractId']->value;
        /** @var string $serviceLocationId */
        $serviceLocationId = $userSearchCriteria->filters['serviceLocationId']->value;

        $serviceType = ServiceType::tryFrom(value: $serviceTypeValue);

        if (null === $serviceType) {
            throw new NotFoundException(message: 'Service type '.$serviceTypeValue.' not found!');
        }

        $containerAmount = ServiceType::getMaxContainerCount(serviceType: $serviceType);

        if (null !== $containerAmount) {
            return new Collection(
                items: $this->generateContainerOptions(serviceType: $serviceType, containerAmount: $containerAmount),
            );
        }

        $containerAmount = $this
            ->contractServiceLocationRepository
            ->countEquipmentByContractIdServiceTypeAndServiceLocationId(
                contractId: $contractId,
                serviceLocationId: $serviceLocationId,
                serviceType: $serviceType,
            );

        return new Collection(
            items: $this->generateContainerOptions(serviceType: $serviceType, containerAmount: $containerAmount),
        );
    }

    /**
     * @return array<Order>
     */
    private function generateContainerOptions(ServiceType $serviceType, int $containerAmount): array
    {
        $items = [];

        if (0 >= $containerAmount) {
            return $items;
        }

        foreach (range(start: 1, end: $containerAmount) as $index) {
            $option = new Order();
            $option->id = $serviceType->value;
            $option->display = (string) $index;

            $items[] = $option;
        }

        return $items;
    }

    /**
     * @return Collection<Order>
     *
     * @throws \Exception
     */
    public function getAgreementOptions(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        /** @var string $serviceType */
        $serviceType = $userSearchCriteria->filters['serviceType']->value;

        $serviceType = ServiceType::tryFrom(value: $serviceType)?->value;
        if (null == $serviceType) {
            throw new NotFoundException(message: "Service Type $serviceType does not exist!");
        }

        $agreements = $this->orderAgreementRepository->findAllByServiceTypeAndNull(serviceType: $serviceType);

        $items = [];
        foreach ($agreements as $agreement) {
            $dto = new Order();
            $dto->id = $agreement->getId();
            $dto->display = sprintf('{{order_agreement_%s}}', $agreement->getName());
            $dto->required = $agreement->getRequired();
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }
}
