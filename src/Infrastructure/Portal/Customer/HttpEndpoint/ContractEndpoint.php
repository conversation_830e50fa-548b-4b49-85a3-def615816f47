<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Contract as ContractEntity;
use App\Domain\Entity\Enum\MaterialType;
use App\Domain\Entity\Enum\RentType;
use App\Domain\Repository\ContractPositionRepository;
use App\Domain\Repository\ContractRepository;
use App\Domain\Repository\ContractServiceLocationRepository;
use App\Domain\Repository\ServiceLocationRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\Contract;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ContractEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private ContractRepository $contractRepository,
        private ContractPositionRepository $contractPositionRepository,
        private ContractServiceLocationRepository $contractServiceLocationRepository,
        private ServiceLocationRepository $serviceLocationRepository,
    ) {
    }

    /**
     * @return PaginatedCollection<Contract>
     *
     * @throws \Exception
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $contracts = $this->contractRepository->findByUserSearchCriteria(userSearchCriteria: $userSearchCriteria, businessPartnerId: $businessPartnerId);
        $items = [];
        foreach ($contracts as $contract) {
            $items[] = $this->mapToDto(contract: $contract);
        }

        return new PaginatedCollection(
            totalItems: $contracts->count(),
            items: $items
        );
    }

    /**
     * @throws \Exception
     */
    public function get(Request $request, string $id): Contract
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        $contract = $this->contractRepository->findOneByIdAndBusinessPartner(id: $id, businessPartnerId: $businessPartnerId);
        if (null === $contract) {
            throw new NotFoundException(message: 'Contract not found');
        }

        $rentTypePosition = $this->contractPositionRepository->findOneByContractIdAndMaterialType(
            contractId: $contract->getId(),
            materialType: MaterialType::RENT->value,
        );

        $rentType = null === $rentTypePosition?->getRentType()
            ? ''
            : RentType::tryFrom(value: $rentTypePosition->getRentType())->name ?? '';

        $dto = new Contract();
        $dto->id = $contract->getId();
        $dto->contractNumber = $contract->getExtId();
        $dto->container = $contract->getDescription();
        $dto->material = $contract->getDescription();
        $dto->rentType = strtolower(string: $rentType);

        return $dto;
    }

    /**
     * @return Collection<Contract>
     */
    public function getOptions(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        if (!isset($userSearchCriteria->filters['serviceLocationId'])) {
            $contracts = $this->contractRepository->findAllByBusinessPartner(businessPartnerId: $businessPartnerId);

            $items = array_map(
                callback: function (ContractEntity $contract): Contract {
                    $dto = new Contract();
                    $dto->id = $contract->getId();
                    $dto->display = $contract->getExtId().' - '.$contract->getDescription();

                    return $dto;
                },
                array: $contracts,
            );

            return new Collection(
                items: $items,
            );
        }

        /** @var string $serviceLocationId */
        $serviceLocationId = $userSearchCriteria->filters['serviceLocationId']->value;
        $serviceLocation = $this->serviceLocationRepository->find(id: $serviceLocationId);

        if (null === $serviceLocation) {
            return new Collection(items: []);
        }

        $contractServiceLocations = $this->contractServiceLocationRepository->findAllByExtServiceLocationId(extServiceLocationId: $serviceLocation->getExtId());

        $items = [];
        foreach ($contractServiceLocations as $location) {
            $contract = $this->contractRepository->findByUniqueConstraint(extId: $location->getExtContractId());
            if (null === $contract) {
                return new Collection(items: []);
            }
            $waste = $this->contractPositionRepository->findOneByContractIdAndMaterialType(
                contractId: $contract->getId(),
                materialType: MaterialType::WASTE->value,
            );
            $container = $this->contractPositionRepository->findOneByContractIdAndMaterialType(
                contractId: $contract->getId(),
                materialType: MaterialType::CONTAINER->value,
            );

            if (null === $waste || null === $container) {
                throw new NotFoundException(message: sprintf('No waste or container to contract %s found', $contract->getId())
                );
            }

            $dto = new Contract();
            $dto->id = $contract->getId();
            $dto->display = $contract->getExtId().' ('.$waste->getMaterialText().') ('.$container->getMaterialText().')';
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }

    private function mapToDto(ContractEntity $contract): Contract
    {
        $dto = new Contract();
        $dto->id = $contract->getId();
        $dto->contractNumber = $contract->getExtId();

        $waste = $this->contractPositionRepository->findOneByContractIdAndMaterialType(
            contractId: $contract->getId(),
            materialType: MaterialType::WASTE->value,
        );
        $container = $this->contractPositionRepository->findOneByContractIdAndMaterialType(
            contractId: $contract->getId(),
            materialType: MaterialType::CONTAINER->value,
        );

        if (null === $waste || null === $container) {
            throw new NotFoundException(message: sprintf('No waste or container to contract %s found', $contract->getId())
            );
        }

        $dto->material = $waste->getMaterialText();
        $dto->container = $container->getMaterialText();

        return $dto;
    }
}
