<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Repository\VerificationDocumentRepository;
use App\Infrastructure\Portal\Customer\Resource\VerificationDocumentNumber;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class VerificationDocumentNumberEndpoint
{
    public function __construct(
        private VerificationDocumentRepository $verificationDocumentRepository,
    ) {
    }

    /**
     * @return Collection<VerificationDocumentNumber>
     */
    public function getOptions(Request $request): Collection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $verificationDocuments = $this->verificationDocumentRepository->findAllByBusinessPartner(businessPartnerId: $businessPartnerId);

        $items = [];
        foreach ($verificationDocuments as $verificationDocument) {
            $dto = new VerificationDocumentNumber();
            $dto->id = $verificationDocument->getId();
            $dto->display = $verificationDocument->getExtId().' - '.$verificationDocument->getDescription();
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }
}
