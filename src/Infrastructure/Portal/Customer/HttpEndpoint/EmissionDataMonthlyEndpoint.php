<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\ProductHierarchy;
use App\Domain\Repository\WasteEcoSavingsRepository;
use App\Infrastructure\Portal\Customer\Resource\Dto\EmissionDataMonthlyData;
use App\Infrastructure\Portal\Customer\Resource\EmissionDataMonthly;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class EmissionDataMonthlyEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private WasteEcoSavingsRepository $wasteEcoSavingsRepository,
    ) {
    }

    /**
     * @return Collection<EmissionDataMonthly>
     *
     * @throws \DateMalformedStringException
     */
    public function getSavings(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        $productHierarchies = ProductHierarchy::cases();

        $items = [];

        foreach ($productHierarchies as $productHierarchy) {
            $emissionData = $this->wasteEcoSavingsRepository->findAllByProductHierarchyAndBusinessPartnerId(
                userSearchCriteria: $userSearchCriteria,
                productHierarchy: $productHierarchy->value,
                businessPartnerId: $businessPartnerId
            );

            if ([] === $emissionData) {
                continue;
            }

            $dto = new EmissionDataMonthly();
            $dto->material = $productHierarchy->getFrontendKey() ?? '';

            $monthlyDataArr = [];
            foreach ($emissionData as $data) {
                $monthlyData = new EmissionDataMonthlyData();
                $monthlyData->savings = $data->getCo2Savings().' t';
                $monthlyData->date = new \DateTimeImmutable(datetime: $data->getYear().'-'.$data->getMonth().'-01')->format(format: 'Y-m-d');
                $monthlyDataArr[] = $monthlyData;
            }
            $dto->data = $monthlyDataArr;
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }

    /**
     * @return Collection<EmissionDataMonthly>
     *
     * @throws \DateMalformedStringException
     */
    public function getEmissions(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        $productHierarchies = ProductHierarchy::cases();

        $items = [];

        foreach ($productHierarchies as $productHierarchy) {
            $emissionData = $this->wasteEcoSavingsRepository->findAllByProductHierarchyAndBusinessPartnerId(
                userSearchCriteria: $userSearchCriteria,
                productHierarchy: $productHierarchy->value,
                businessPartnerId: $businessPartnerId
            );

            if ([] === $emissionData) {
                continue;
            }

            $dto = new EmissionDataMonthly();
            $dto->material = $productHierarchy->getFrontendKey() ?? '';

            $monthlyDataArr = [];
            foreach ($emissionData as $data) {
                $monthlyData = new EmissionDataMonthlyData();
                $monthlyData->emissions = $data->getCo2Emissions().' t';
                $monthlyData->date = new \DateTimeImmutable(datetime: $data->getYear().'-'.$data->getMonth().'-01')->format(format: 'Y-m-d');
                $monthlyDataArr[] = $monthlyData;
            }
            $dto->data = $monthlyDataArr;
            $items[] = $dto;
        }

        return new Collection(
            items: $items,
        );
    }
}
