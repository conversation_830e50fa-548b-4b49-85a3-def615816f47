<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUser;
use App\Infrastructure\Portal\Customer\Resource\UserMe;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class UserMeEndpoint
{
    public function __construct(
        private Security $security,
    ) {
    }

    public function getProfile(): UserMe
    {
        $securityUser = $this->security->getUser();

        $user = new UserMe();
        if ($securityUser instanceof KeycloakUser) {
            $user->userId = $securityUser->getUserIdentifier();
            $user->userName = $securityUser->getUserName();
            $user->email = $securityUser->getEmail();
            $user->firstName = $securityUser->getFirstName();
            $user->lastName = $securityUser->getLastName();
            $user->permissions = $securityUser->getRoles();
        }

        return $user;
    }
}
