<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\ServiceLocation;
use App\Domain\Entity\Supplier;
use App\Domain\Repository\ServiceLocationRepository;
use App\Domain\Repository\SupplierRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Holiday\PublicHoliday;
use App\Infrastructure\Portal\Customer\Resource\Dto\DatePickerDate;
use App\Infrastructure\Portal\Customer\Resource\OrderDatePicker;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class OrderDatePickerEndpoint
{
    private const string SHOW_DATES_UNTIL = '+6 months';

    private const string BLOCKED = 'BLOCKED';

    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private ServiceLocationRepository $serviceLocationRepository,
        private SupplierRepository $supplierRepository,
    ) {
    }

    /**
     * @throws \DateMalformedStringException
     * @throws \Exception
     * @throws NotFoundException
     */
    public function getOptions(Request $request): OrderDatePicker
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        /** @var string $contractId */
        $contractId = $userSearchCriteria->filters['contractId']->value;
        /** @var string $serviceLocationId */
        $serviceLocationId = $userSearchCriteria->filters['serviceLocationId']->value;

        $supplier = $this->getSupplier(contractId: $contractId);
        $serviceLocation = $this->getServiceLocation(serviceLocationId: $serviceLocationId);

        $datesUntil = $this->getLastCalculatedDate();
        $date = new \DateTimeImmutable();
        $holidays = $this->getHolidaysFor(
            states: $this->getStates(supplier: $supplier, serviceLocation: $serviceLocation),
            years: $this->getYears(from: $date, to: $datesUntil));

        $dto = new OrderDatePicker();
        $dto->default = self::BLOCKED;

        $allowedDates = [];
        while ($date <= $datesUntil) {
            $datePicker = new DatePickerDate();
            $datePicker->date = $date->format(format: 'd.m.Y');

            if ($this->isAllowed(date: $date, holidays: $holidays, supplier: $supplier)) {
                $allowedDates[] = $datePicker;
            }

            $date = $date->modify(modifier: '+ 1 day');
        }

        $dto->allowedDates = $allowedDates;

        return $dto;
    }

    private function getLastCalculatedDate(): \DateTimeImmutable
    {
        $today = new \DateTimeImmutable();

        return $today->modify(modifier: self::SHOW_DATES_UNTIL);
    }

    /**
     * @param array<string, string> $holidays
     *
     * @throws \DateMalformedStringException
     */
    private function isAllowed(\DateTimeImmutable $date, array $holidays, Supplier $supplier): bool
    {
        if ($this->isWeekend(date: $date)) {
            return false;
        }

        if ($this->isHoliday(date: $date, holidays: $holidays)) {
            return false;
        }

        return !$this->orderTooEarly(date: $date, supplier: $supplier);
    }

    private function isWeekend(\DateTimeImmutable $date): bool
    {
        return '6' === $date->format(format: 'N')  // Saturday
            || '7' === $date->format(format: 'N'); // Sunday
    }

    /**
     * @param array<string, string> $states
     * @param array<int, int>       $years
     *
     * @return array<string, string>
     */
    private function getHolidaysFor(array $states, array $years): array
    {
        $holidays = [];

        foreach ($years as $year) {
            foreach ($states as $state) {
                $holidays = array_merge($holidays, PublicHoliday::getPublicHolidaysFor(state: $state, year: $year));
            }
        }

        return $holidays;
    }

    /**
     * @param array<string, string> $holidays
     */
    private function isHoliday(\DateTimeImmutable $date, array $holidays): bool
    {
        return isset($holidays[$date->format(format: 'Y-m-d')]);
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function orderTooEarly(\DateTimeImmutable $date, Supplier $supplier): bool
    {
        $orderDelay = $supplier->getOrderDelay();

        if (null === $orderDelay) {
            return false;
        }

        $now = new \DateTimeImmutable();
        $delayedTime = $now
            ->modify(modifier: sprintf('+%s hours', $orderDelay))
            ->format(format: 'Y-m-d');

        if ($date->format(format: 'Y-m-d') < $delayedTime) {
            return true;
        }

        $delayWeekday = $this->getDelayTime(date: $now, supplier: $supplier);

        if ($date->format(format: 'Y-m-d') === $delayedTime && null === $delayWeekday) {
            return true;
        }

        return $date->format(format: 'Y-m-d') === $delayedTime
            && $date->format(format: 'H:i') > $delayWeekday;
    }

    /**
     * @throws \InvalidArgumentException
     */
    private function getDelayTime(\DateTimeImmutable $date, Supplier $supplier): ?string
    {
        return match ($date->format(format: 'l')) {
            'Monday' => $supplier->getOrderDelayMonday(),
            'Tuesday' => $supplier->getOrderDelayTuesday(),
            'Wednesday' => $supplier->getOrderDelayWednesday(),
            'Thursday' => $supplier->getOrderDelayThursday(),
            'Friday' => $supplier->getOrderDelayFriday(),
            default => null,
        };
    }

    /**
     * @return array<string, string>
     */
    private function getStates(Supplier $supplier, ServiceLocation $serviceLocation): array
    {
        $states = [];

        if (null !== $supplier->getFederalState()) {
            $states[$supplier->getFederalState()] = $supplier->getFederalState();
        }

        if (null !== $serviceLocation->getFederalState()) {
            $states[$supplier->getFederalState()] = $serviceLocation->getFederalState();
        }

        return $states;
    }

    /**
     * @return array<int, int>
     */
    private function getYears(\DateTimeImmutable $from, \DateTimeImmutable $to): array
    {
        return array_unique(array: range(
            start: (int) $from->format(format: 'Y'),
            end: (int) $to->format(format: 'Y')
        ));
    }

    /**
     * @throws NotFoundException
     */
    private function getSupplier(string $contractId): Supplier
    {
        $supplier = $this->supplierRepository->findByContractId(contractId: $contractId);

        if (null === $supplier) {
            throw new NotFoundException(message: 'Supplier not found.');
        }

        return $supplier;
    }

    /**
     * @throws NotFoundException
     */
    private function getServiceLocation(string $serviceLocationId): ServiceLocation
    {
        $serviceLocation = $this->serviceLocationRepository->find(id: $serviceLocationId);

        if (null === $serviceLocation) {
            throw new NotFoundException(message: 'Service Location not found.');
        }

        return $serviceLocation;
    }
}
