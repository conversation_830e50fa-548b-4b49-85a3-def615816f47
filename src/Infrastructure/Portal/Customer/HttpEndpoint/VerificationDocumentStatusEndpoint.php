<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\VerificationDocumentStatus as VerificationDocumentStatusEnum;
use App\Infrastructure\Portal\Customer\Resource\VerificationDocumentStatus;
use PreZero\ApiBundle\Collection\Collection;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class VerificationDocumentStatusEndpoint
{
    /**
     * @return Collection<VerificationDocumentStatus>
     */
    public function getOptions(): Collection
    {
        $items = array_map(
            callback: function (VerificationDocumentStatusEnum $status): ?VerificationDocumentStatus {
                $frontendStatus = VerificationDocumentStatusEnum::from(value: $status->value)->getFrontendStatus();

                if ($frontendStatus) {
                    $ret = new VerificationDocumentStatus();
                    $ret->id = $frontendStatus;
                    $ret->display = sprintf('{{%s}}', $frontendStatus);

                    return $ret;
                }

                return null;
            },
            array: VerificationDocumentStatusEnum::cases()
        );

        $filteredItems = array_filter(array: $items);
        $uniqueItems = [];
        $seenIds = [];

        foreach ($filteredItems as $item) {
            if (!in_array(needle: $item->id, haystack: $seenIds, strict: true)) {
                $uniqueItems[] = $item;
                $seenIds[] = $item->id;
            }
        }

        return new Collection(
            items: $uniqueItems
        );
    }
}
