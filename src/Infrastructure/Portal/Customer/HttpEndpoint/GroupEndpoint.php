<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Group as GroupEntity;
use App\Domain\Repository\GroupRepository;
use App\Domain\Repository\ServiceLocationRepository;
use App\Domain\Repository\UserRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\Group;
use App\Infrastructure\Portal\Customer\Service\GroupService;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class GroupEndpoint
{
    public function __construct(
        private GroupRepository $groupRepository,
        private UserRepository $userRepository,
        private ServiceLocationRepository $serviceLocationRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private GroupService $groupService,
    ) {
    }

    public function get(Request $request, string $id): Group
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $businessPartner = $this->groupService->getBusinessPartner(businessPartnerId: $businessPartnerId);

        $group = $this->groupRepository->findOneByIdAndBusinessPartner(id: $id, businessPartner: $businessPartner);

        if (null === $group) {
            throw new NotFoundException(message: "Group $id not found!");
        }

        $dto = new Group();
        $dto->id = $group->getId();
        $dto->groupName = $group->getName();

        return $dto;
    }

    /**
     * @return PaginatedCollection<Group>
     *
     * @throws QueryException
     */
    public function getCollection(Request $request): PaginatedCollection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $businessPartner = $this->groupService->getBusinessPartner(businessPartnerId: $businessPartnerId);

        $paginator = $this->groupRepository->findAllByUserSearchCriteriaAndBusinessPartner(
            userSearchCriteria: $userSearchCriteria,
            businessPartner: $businessPartner
        );

        $items = [];
        foreach ($paginator as $group) {
            $dto = new Group();
            $dto->id = $group->getId();
            $dto->groupName = $group->getName();
            $dto->userCount = $group->getUsers()->count();
            $dto->serviceLocationCount = $group->getServiceLocations()->count();

            $items[] = $dto;
        }

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    public function create(Request $request, Group $group): Group
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $businessPartner = $this->groupService->getBusinessPartner(businessPartnerId: $businessPartnerId);
        $entity = new GroupEntity();
        $entity->setName(name: $group->groupName);
        $entity->setBusinessPartner(businessPartner: $businessPartner);

        foreach ($group->selectedServiceLocations as $selectedServiceLocation) {
            $serviceLocation = $this->serviceLocationRepository->find(id: $selectedServiceLocation->id);

            if (null === $serviceLocation) {
                continue;
            }

            $entity->addServiceLocation(serviceLocation: $serviceLocation);
        }

        $this->groupRepository->add(group: $entity);

        return $group;
    }

    public function update(Group $group, string $id): Group
    {
        $entity = $this->groupRepository->find(id: $id);
        if (null === $entity) {
            throw new NotFoundException(message: "Group $id not found!");
        }

        if (isset($group->groupName)) {
            $entity->setName(name: $group->groupName);
        }

        $entity->clearServiceLocations();
        if (isset($group->selectedServiceLocations)) {
            foreach ($group->selectedServiceLocations as $selectedServiceLocation) {
                $serviceLocation = $this->serviceLocationRepository->find(id: $selectedServiceLocation->id);

                if (null === $serviceLocation) {
                    continue;
                }

                $entity->addServiceLocation(serviceLocation: $serviceLocation);
            }
        }

        $this->groupRepository->add(group: $entity);

        return $group;
    }

    public function updateUsers(Group $group, string $id): Group
    {
        $entity = $this->groupRepository->find(id: $id);
        if (null === $entity) {
            throw new NotFoundException(message: "Group $id not found!");
        }

        $entity->clearUsers();
        if (isset($group->selectedUsers)) {
            foreach ($group->selectedUsers as $selectedUser) {
                $user = $this->userRepository->find(id: $selectedUser->id);

                if (null === $user) {
                    continue;
                }

                $entity->addUser(user: $user);
            }
        }

        $this->groupRepository->add(group: $entity);

        return $group;
    }

    public function delete(Request $request, string $id): void
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $businessPartner = $this->groupService->getBusinessPartner(businessPartnerId: $businessPartnerId);

        $group = $this->groupRepository->findOneByIdAndBusinessPartner(id: $id, businessPartner: $businessPartner);

        if (null === $group) {
            throw new NotFoundException(message: "Group $id not found!");
        }

        $group->clearUsers();
        $group->clearServiceLocations();
        $this->groupRepository->remove(group: $group);
    }

    /**
     * @return PaginatedCollection<Group>
     *
     * @throws \Exception
     */
    public function getServiceLocationOptions(Request $request): PaginatedCollection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        /** @var string $groupId */
        $groupId = $userSearchCriteria->filters['groupId']->value ?? null;
        $group = $this->groupService->getGroup(groupId: $groupId);

        $paginator = $this->serviceLocationRepository->findAllByUserSearchCriteriaAndContractAndBusinessPartner(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartnerId
        );

        $items = [];
        foreach ($paginator as $serviceLocation) {
            $dto = new Group();
            $dto->id = $serviceLocation->getId();
            $dto->address = $serviceLocation->getAddress();
            $dto->information = $serviceLocation->getAdditionalInformation() ?? '';
            $dto->selected = null !== $group && $group->getServiceLocations()->contains($serviceLocation);

            $items[] = $dto;
        }

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    /**
     * @return PaginatedCollection<Group>
     *
     * @throws \Exception
     */
    public function getUserOptions(Request $request): PaginatedCollection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');

        /** @var string $groupId */
        $groupId = $userSearchCriteria->filters['groupId']->value ?? null;
        $group = $this->groupService->getGroup(groupId: $groupId);

        $paginator = $this->userRepository->findByUserSearchCriteriaAndBusinessPartnerIdForPortal(
            userSearchCriteria: $userSearchCriteria,
            businessPartnerId: $businessPartnerId,
            searchInUser: true,
        );

        $items = [];
        foreach ($paginator as $user) {
            $dto = new Group();
            $dto->id = $user->getId();
            $dto->email = $user->getUsername();
            $dto->firstname = $user->getFirstname() ?? '';
            $dto->lastname = $user->getLastname() ?? '';
            $dto->selected = null !== $group && $group->getUsers()->contains($user);

            $items[] = $dto;
        }

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }
}
