<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\SupervisionDocument as SupervisionDocumentEntity;
use App\Domain\Entity\VerificationDocument as VerificationDocumentEntity;
use App\Domain\Repository\SupervisionDocumentRepository;
use App\Domain\Repository\VerificationDocumentRepository;
use App\Infrastructure\Portal\Customer\Resource\VerificationDocumentSupervisionDocument;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\PaginatedCollection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

#[AsController]
readonly class VerificationDocumentSupervisionDocumentEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private VerificationDocumentRepository $verificationDocumentRepository,
        private SupervisionDocumentRepository $supervisionDocumentRepository,
    ) {
    }

    /**
     * @return PaginatedCollection<VerificationDocumentSupervisionDocument>
     *
     * @throws QueryException
     * @throws \Exception
     */
    public function getCollection(Request $request, string $id): PaginatedCollection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        $verificationDocument = $this->verificationDocumentRepository->findOneByIdAndBusinessPartner(id: $id, businessPartnerId: $businessPartnerId);

        if (null === $verificationDocument) {
            throw new NotFoundHttpException(message: "Verification document $id not found");
        }

        $paginator = $this->supervisionDocumentRepository->findAllByExtVerificationDocumentIdAndBusinessPartner(
            userSearchCriteria: $userSearchCriteria,
            id: $verificationDocument->getExtId(),
            businessPartnerId: $businessPartnerId
        );
        $items = array_map(
            callback: fn (SupervisionDocumentEntity $supervisionDocument): VerificationDocumentSupervisionDocument
            => $this->mapToDto(verificationDocument: $verificationDocument, supervisionDocument: $supervisionDocument),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new PaginatedCollection(
            totalItems: $paginator->count(),
            items: $items,
        );
    }

    public function mapToDto(
        VerificationDocumentEntity $verificationDocument,
        SupervisionDocumentEntity $supervisionDocument,
    ): VerificationDocumentSupervisionDocument {
        $dto = new VerificationDocumentSupervisionDocument();
        $dto->id = $supervisionDocument->getId();
        $dto->date = $verificationDocument->getDateDisposer()->format(format: 'd.m.Y');
        $dto->supervisionDocumentNumber = $supervisionDocument->getExtId();
        $dto->amountDisposer = $supervisionDocument->getAmount().' '.$supervisionDocument->getAmountUnitOm();

        return $dto;
    }
}
