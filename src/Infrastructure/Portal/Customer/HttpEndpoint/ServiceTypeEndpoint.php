<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\MaterialType;
use App\Domain\Entity\Enum\ServiceType as ServiceTypeEnum;
use App\Domain\Entity\MaterialServiceType;
use App\Domain\Repository\ContractPositionRepository;
use App\Domain\Repository\MaterialServiceTypeRepository;
use App\Exception\BadRequestException;
use App\Infrastructure\Portal\Customer\Resource\ServiceType;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class ServiceTypeEndpoint
{
    public function __construct(
        private ContractPositionRepository $contractPositionRepository,
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private MaterialServiceTypeRepository $materialServiceRepository,
    ) {
    }

    /**
     * @return Collection<ServiceType>
     */
    public function getOptions(Request $request): Collection
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);
        /** @var ?string $contractId */
        $contractId = $userSearchCriteria->filters['contractId']->value ?? null;

        if (!isset($contractId)) {
            throw new BadRequestException(message: 'Please filter for ContractId!');
        }

        $contractPositions = $this->contractPositionRepository->findAllByContractIdAndMaterialType(
            contractId: $contractId,
            materialType: MaterialType::WASTE->value,
        );

        $items = [];

        foreach ($contractPositions as $contractPosition) {
            $materialServices = $this->materialServiceRepository->findAllByExtMaterialId(
                extMaterialId: $contractPosition->getExtMaterialId()
            );
            $materialServices = [];
            // todo
            $materialService = new MaterialServiceType();
            $materialService->setExtMaterialId(extMaterialId: $contractPosition->getExtMaterialId());
            $materialService->setServiceType(serviceType: '10');
            $materialServices[] = $materialService;
            $materialService = new MaterialServiceType();
            $materialService->setExtMaterialId(extMaterialId: $contractPosition->getExtMaterialId());
            $materialService->setServiceType(serviceType: '11');
            $materialServices[] = $materialService;
            $materialService = new MaterialServiceType();
            $materialService->setExtMaterialId(extMaterialId: $contractPosition->getExtMaterialId());
            $materialService->setServiceType(serviceType: '12');
            $materialServices[] = $materialService;

            foreach ($materialServices as $materialService) {
                $serviceType = ServiceTypeEnum::tryFrom(value: $materialService->getServiceType());
                if (null == $serviceType) {
                    throw new \LogicException(message: "Service type '".$materialService->getServiceType()."' not found!");
                }

                $serviceTypeKey = strtolower(string: $serviceType->name);

                $dto = new ServiceType();
                $dto->id = $serviceType->value; // todo
                $dto->display = sprintf('{{%s}}', $serviceTypeKey);
                $dto->tooltip = '{{tooltip_'.$materialService->getExtMaterialId().' - '.$serviceTypeKey.'}}';
                $items[] = $dto;
            }
        }

        return new Collection(
            items: $items
        );
    }
}
