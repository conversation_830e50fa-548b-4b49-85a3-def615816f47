<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\HttpEndpoint;

use App\Domain\Entity\Enum\OrderStatus;
use App\Domain\Entity\Enum\ServiceType;
use App\Domain\Entity\Invoice as InvoiceEntity;
use App\Domain\Entity\Order as OrderEntity;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\ContractPositionRepository;
use App\Domain\Repository\InvoiceRepository;
use App\Domain\Repository\OrderRepository;
use App\Domain\Repository\WasteEcoSavingsRepository;
use App\Domain\Repository\WasteStatisticRepository;
use App\Domain\Service\OrderService;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\Dashboard;
use Doctrine\ORM\Query\QueryException;
use PreZero\ApiBundle\Collection\Collection;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use PreZero\ApiBundle\UserSearchCriteriaBuilder;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;

#[AsController]
readonly class DashboardEndpoint
{
    public function __construct(
        private UserSearchCriteriaBuilder $userSearchCriteriaBuilder,
        private BusinessPartnerRepository $businessPartnerRepository,
        private WasteStatisticRepository $wasteStatisticRepository,
        private InvoiceRepository $invoiceRepository,
        private OrderRepository $orderRepository,
        private OrderService $orderService,
        private ContractPositionRepository $contractPositionRepository,
        private WasteEcoSavingsRepository $wasteEcoSavingsRepository,
    ) {
    }

    public function wasteStatistic(Request $request): Dashboard
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $businessPartner = $this->businessPartnerRepository->findOneBy(criteria: ['id' => $businessPartnerId]);
        if (null === $businessPartner) {
            throw new NotFoundException(message: "BusinessPartner $businessPartnerId not found!");
        }

        $currentYear = (int) new \DateTimeImmutable()->format(format: 'Y');
        $totalAmount = $this->wasteStatisticRepository->getTotalAmountPerYearAndBusinessPartner(
            year: $currentYear,
            businessPartnerId: $businessPartner->getExtId()
        );

        $resource = new Dashboard();
        $resource->amount = $totalAmount.' t';

        return $resource;
    }

    public function co2Report(Request $request): Dashboard
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $currentYear = new \DateTimeImmutable()->format(format: 'Y');

        $savingsPerYear = $this->wasteEcoSavingsRepository->getTotalSavingsPerYearByBusinessPartnerId(year: $currentYear, businessPartnerId: $businessPartnerId);

        $resource = new Dashboard();
        $resource->amount = (string) $savingsPerYear;

        return $resource;
    }

    /**
     * @return Collection<Dashboard>
     *
     * @throws QueryException
     * @throws \Exception
     */
    public function nextOrderDates(Request $request): Collection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->getDashboardUserSearchCriteria(request: $request);

        $paginator = $this->orderRepository->findAllByUpcomingDates(userSearchCriteria: $userSearchCriteria, businessPartnerId: $businessPartnerId);
        $items = array_map(
            callback: fn (OrderEntity $supervisionDocument): Dashboard => $this->mapOrderToDto(order: $supervisionDocument),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new Collection(
            items: $items,
        );
    }

    /**
     * @return Collection<Dashboard>
     *
     * @throws \Exception
     */
    public function lastOrders(Request $request): Collection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->getDashboardUserSearchCriteria(request: $request);

        $paginator = $this->orderRepository->findAllByPastDates(userSearchCriteria: $userSearchCriteria, businessPartnerId: $businessPartnerId);
        $items = array_map(
            callback: fn (OrderEntity $supervisionDocument): Dashboard => $this->mapOrderToDto(order: $supervisionDocument),
            array: iterator_to_array(iterator: $paginator->getIterator()),
        );

        return new Collection(
            items: $items,
        );
    }

    /**
     * @return Collection<Dashboard>
     *
     * @throws \Exception
     */
    public function invoices(Request $request): Collection
    {
        $businessPartnerId = (string) $request->headers->get(key: 'x-current-user-context-id');
        $userSearchCriteria = $this->getDashboardUserSearchCriteria(request: $request);

        $invoices = $this->invoiceRepository->findByUserSearchCriteria(userSearchCriteria: $userSearchCriteria, businessPartnerId: $businessPartnerId);
        $items = [];
        foreach ($invoices as $invoice) {
            $items[] = $this->mapInvoiceToDto(invoice: $invoice);
        }

        return new Collection(
            items: $items,
        );
    }

    private function mapInvoiceToDto(InvoiceEntity $invoice): Dashboard
    {
        $dto = new Dashboard();
        $dto->id = $invoice->getId();
        $dto->amount = $invoice->getNetValue().' '.$invoice->getCurrency();
        $dto->date = $invoice->getBillingDate()->format(format: 'd.m.Y');
        $dto->number = $invoice->getExtId();
        $dto->filePath = 'sample.pdf'; // TODO: Add file path logic if needed

        return $dto;
    }

    private function mapOrderToDto(OrderEntity $order): Dashboard
    {
        $orderStatus = OrderStatus::from(value: $order->getOrderStatus())->getFrontendStatus();

        $dto = new Dashboard();
        $dto->id = $order->getId();
        $dto->date = $order->getOrderDate()->format(format: 'd.m.Y');
        $dto->status = $order->getOrderStatus();
        $dto->statusText = sprintf('{{%s}}', $orderStatus);

        $service = $this->orderService->getService(order: $order);
        if (null !== $service) {
            $serviceText = strtolower(string: ServiceType::from(value: $service->getServiceType())->name);

            $containerPos = $this->contractPositionRepository->findOneByExtContractIdAndExtMaterialId(
                extContractId: $service->getExtContractId(),
                extMaterialId: $service->getExtContainerMaterialId(),
            );

            $materialPos = $this->contractPositionRepository->findOneByExtContractIdAndExtMaterialId(
                extContractId: $service->getExtContractId(),
                extMaterialId: $service->getExtWasteMaterialId(),
            );

            $dto->material = $materialPos?->getMaterialText() ?? null;
            $dto->container = $containerPos?->getMaterialText() ?? null;
            $dto->description = $materialPos?->getMaterialText() ?? null;
            $dto->service = sprintf('{{%s}}', $serviceText);
        } else {
            $dto->material = null;
            $dto->container = null;
            $dto->description = null;
            $dto->service = null;
        }

        return $dto;
    }

    private function getDashboardUserSearchCriteria(Request $request): UserSearchCriteria
    {
        $userSearchCriteria = $this->userSearchCriteriaBuilder->fromSymfonyRequest(request: $request);

        return new UserSearchCriteria(
            page: 1,
            perPage: 5,
            nextPageToken: null,
            filters: $userSearchCriteria->filters,
        );
    }
}
