<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\SupervisionDocumentEndpoint;
use App\Infrastructure\Portal\Customer\Resource\Dto\SignData;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [SupervisionDocumentEndpoint::class, 'get'],
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'The supervision document ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'SupervisionDocumentItemResponse',
        ),
        new Get(
            controller: [SupervisionDocumentEndpoint::class, 'sync'],
            uriTemplate: '/supervision-document/sync',
            pathParameters: [],
            normalizationContext: ['groups' => [self::GROUP_SYNC]],
            responseOpenApiSchemaName: 'SupervisionDocumentEmptyResponse'
        ),
        new GetCollection(
            controller: [SupervisionDocumentEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Free text search for supervision document, avv id and certificate number.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'status',
                    parameterDescription: 'Filter by status.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'SupervisionDocumentListResponse'
        ),
    ],
    identifier: 'id',
    tag: 'SupervisionDocument',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class SupervisionDocument
{
    public const string GROUP_DETAIL = 'supervision-document:detail';

    private const string GROUP_COLLECTION = 'supervision-document:collection';

    private const string GROUP_SYNC = 'supervision-document:sync';

    #[SerializedName('id')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $id;

    #[SerializedName('supervision_document_number')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $supervisionDocumentNumber;

    #[SerializedName('date')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $date;

    #[SerializedName('certificate_number')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $certificateNumber;

    #[SerializedName('avv_id')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $avvId;

    #[SerializedName('description')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $description;

    #[SerializedName('amount')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $amount;

    #[SerializedName('status')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $status;

    #[SerializedName('status_text')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $statusText;

    #[SerializedName('producer')]
    #[Groups([self::GROUP_DETAIL])]
    public SignData $producer;

    #[SerializedName('carrier')]
    #[Groups([self::GROUP_DETAIL])]
    public SignData $carrier;

    #[SerializedName('disposer')]
    #[Groups([self::GROUP_DETAIL])]
    public SignData $disposer;
}
