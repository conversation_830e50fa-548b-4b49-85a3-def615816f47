<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\FileEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [FileEndpoint::class, 'get'],
            requestDescription: 'Binary file contents',
            responseType: ContentType::BINARY,
            responseOpenApiSchemaName: 'FileItemResponse',
        ),
        new Put(
            controller: [FileEndpoint::class, 'put'],
            requestType: ContentType::BINARY,
            requestDescription: 'Binary file contents',
            responseDescription: 'File uploaded',
            responseType: ContentType::EMPTY,
        ),
    ],
    identifier: 'id',
    tag: 'File',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class File
{
    #[SerializedName('id')]
    public string $id;

    public \DateTimeImmutable $date;

    public string $name;

    public string $origin;
}
