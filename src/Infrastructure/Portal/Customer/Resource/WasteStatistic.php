<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\WasteStatisticEndpoint;
use App\Infrastructure\Portal\Customer\Resource\Dto\AvvWasteReport;
use App\Infrastructure\Portal\Customer\Resource\Dto\ContractWasteReport;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new Post(
            controller: [WasteStatisticEndpoint::class, 'export'],
            uriTemplate: '/waste-statistic-export',
            denormalizationContext: ['groups' => [self::GROUP_POST]],
            responseType: ContentType::BINARY,
            normalizationContext: ['groups' => [self::GROUP_POST]],
            successHttpCode: 200,
            responseOpenApiSchemaName: 'WasteStatisticExportResponse'
        ),
        new GetCollection(
            controller: [WasteStatisticEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'WasteStatisticListResponse'
        ),
        new Get(
            controller: [WasteStatisticEndpoint::class, 'get'],
            uriTemplate: '/waste-statistic/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'The waste statistic ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'WasteStatisticItemResponse'
        ),
        new Post(
            controller: [WasteStatisticEndpoint::class, 'fromSap'],
            name: 'waste-statistic-sap',
            uriTemplate: '/waste-statistic/sap',
            filters: [
                new Filter(parameterName: 'businessPartnerId', parameterType: 'string'),
                new Filter(parameterName: 'dateFrom', parameterType: 'string'),
                new Filter(parameterName: 'dateTo', parameterType: 'string'),
            ],
            responseType: ContentType::EMPTY,
            normalizationContext: [],
        ),
    ],
    tag: 'WasteStatistic',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class WasteStatistic
{
    public const string GROUP_POST = 'waste-statistic:post';

    public const string GROUP_DETAIL = 'waste-statistic:detail';

    private const string GROUP_COLLECTION = 'waste-statistic:collection';

    #[SerializedName('id')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $id;

    #[SerializedName('service_location_name')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?string $serviceLocationName;

    #[SerializedName('service_location_number')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?string $serviceLocationNumber;

    #[SerializedName('total_quantity')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $totalQuantity;

    /** @var AvvWasteReport[] */
    #[SerializedName('avv_waste_report')]
    #[Groups([self::GROUP_DETAIL])]
    public array $avvWasteReport;

    /** @var ContractWasteReport[] */
    #[SerializedName('contract_waste_report')]
    #[Groups([self::GROUP_DETAIL])]
    public array $contractWasteReport;
}
