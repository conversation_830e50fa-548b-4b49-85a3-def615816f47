<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\ContractPositionEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ContractPositionEndpoint::class, 'getCollection'],
            uriTemplate: '/contract/{id}/position',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'The contract ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'ContractPositionListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'Contract',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ContractPosition
{
    private const string GROUP_COLLECTION = 'order-position:collection';

    #[SerializedName('id')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $id;

    #[SerializedName('contract_position_id')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $contactPositionId;

    #[SerializedName('quantity')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $quantity;

    #[SerializedName('amount')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $amount;

    #[SerializedName('description')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $description;
}
