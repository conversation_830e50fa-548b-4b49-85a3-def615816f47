<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\EmissionDataMonthlyEndpoint;
use App\Infrastructure\Portal\Customer\Resource\Dto\EmissionDataMonthlyData;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [EmissionDataMonthlyEndpoint::class, 'getSavings'],
            uriTemplate: '/emission-data/monthly-savings',
            pathParameters: [],
            denormalizationContext: ['groups' => [self::GROUP_SAVINGS]],
            filters: [
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],

            normalizationContext: ['groups' => [self::GROUP_SAVINGS]],
            responseOpenApiSchemaName: 'EmissionDataMonthlySavingsListResponse'
        ),
        new GetCollection(
            controller: [EmissionDataMonthlyEndpoint::class, 'getEmissions'],
            uriTemplate: '/emission-data/monthly-emissions',
            pathParameters: [],
            denormalizationContext: ['groups' => [self::GROUP_EMISSIONS]],
            filters: [
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_EMISSIONS]],
            responseOpenApiSchemaName: 'EmissionDataMonthlyEmissionsListResponse'
        ),
    ],
    identifier: null,
    tag: 'EmissionData',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class EmissionDataMonthly
{
    public const string GROUP_SAVINGS = 'emission-data-monthly:savings';
    public const string GROUP_EMISSIONS = 'emission-data-monthly:emissions';

    #[SerializedName('material')]
    #[Groups([self::GROUP_SAVINGS, self::GROUP_EMISSIONS])]
    public string $material;

    /** @var EmissionDataMonthlyData[] */
    #[SerializedName('data')]
    #[Groups([self::GROUP_EMISSIONS, self::GROUP_SAVINGS])]
    public array $data;
}
