<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\ServiceLocationEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ServiceLocationEndpoint::class, 'getOptions'],
            uriTemplate: '/service-location/option',
            filters: [
                new Filter(
                    parameterName: 'contractId',
                    parameterDescription: 'Filter for contract.',
                    parameterFormat: 'string',
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_OPTION]],
            responseOpenApiSchemaName: 'ServiceLocationOptionListResponse'
        ),
    ],
    identifier: 'id',
    tag: 'ServiceLocation',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ServiceLocation
{
    private const string GROUP_OPTION = 'service-location:option';

    #[Groups([self::GROUP_OPTION])]
    public string $id;

    #[Groups([self::GROUP_OPTION])]
    public ?string $display = null;
}
