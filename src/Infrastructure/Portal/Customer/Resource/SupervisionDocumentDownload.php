<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\SupervisionDocumentDownloadEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [SupervisionDocumentDownloadEndpoint::class, 'getOptions'],
            uriTemplate: '/supervision-document/download/option',
            pagination: Pagination::NONE,
            responseOpenApiSchemaName: 'SupervisionDocumentDownloadOptionListResponse',
        ),
    ],
    identifier: 'type',
    tag: 'SupervisionDocument',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class SupervisionDocumentDownload
{
    #[SerializedName('type')]
    public string $type;

    #[SerializedName('file_path')]
    public string $filePath;
}
