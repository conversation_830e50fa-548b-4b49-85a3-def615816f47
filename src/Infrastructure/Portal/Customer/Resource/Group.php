<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\GroupEndpoint;
use App\Infrastructure\Portal\Customer\Resource\Dto\GroupSelected;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [GroupEndpoint::class, 'get'],
            uriTemplate: '/group/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'GroupItemResponse',
        ),
        new GetCollection(
            controller: [GroupEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for ....',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'GroupListResponse',
        ),
        new Post(
            controller: [GroupEndpoint::class, 'create'],
            uriTemplate: '/group',
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            requestOpenApiSchemaName: 'GroupCreateRequest',
            normalizationContext: ['groups' => [self::GROUP_CREATE]],
            responseOpenApiSchemaName: 'GroupCreateResponse',
        ),
        new Patch(
            controller: [GroupEndpoint::class, 'update'],
            uriTemplate: '/group/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_UPDATE]],
            requestOpenApiSchemaName: 'GroupUpdateRequest',
            normalizationContext: ['groups' => [self::GROUP_UPDATE]],
            responseOpenApiSchemaName: 'GroupUpdateResponse',
        ),
        new Delete(
            controller: [GroupEndpoint::class, 'delete'],
            uriTemplate: '/group/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            responseOpenApiSchemaName: 'GroupDeleteRequest',
        ),
        new GetCollection(
            controller: [GroupEndpoint::class, 'getServiceLocationOptions'],
            uriTemplate: '/group/service-location-option',
            denormalizationContext: ['groups' => [self::GROUP_SERVICE_LOCATION_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'groupId',
                    parameterDescription: 'Id of the group to get the service locations for',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for Service Locations',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_SERVICE_LOCATION_COLLECTION]],
            responseOpenApiSchemaName: 'GroupServiceLocationListResponse'
        ),
        new GetCollection(
            controller: [GroupEndpoint::class, 'getUserOptions'],
            uriTemplate: '/group/user-option',
            denormalizationContext: ['groups' => [self::GROUP_USER_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'groupId',
                    parameterDescription: 'Id of the group to get the service locations for',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for Users',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_USER_COLLECTION]],
            responseOpenApiSchemaName: 'GroupUserListResponse'
        ),
        new Patch(
            controller: [GroupEndpoint::class, 'updateUsers'],
            uriTemplate: '/group/{id}/user',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::GROUP_USER_UPDATE]],
            requestOpenApiSchemaName: 'GroupUpdateUserRequest',
            normalizationContext: ['groups' => [self::GROUP_USER_UPDATE]],
            responseOpenApiSchemaName: 'GroupUpdateUserResponse',
        ),
    ],
    identifier: 'id',
    tag: 'Group',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class Group
{
    public const string GROUP_DETAIL = 'group:detail';

    public const string GROUP_COLLECTION = 'group:collection';

    public const string GROUP_CREATE = 'group:create';

    public const string GROUP_UPDATE = 'group:update';

    public const string GROUP_SERVICE_LOCATION_COLLECTION = 'group-service-location-collection';
    public const string GROUP_USER_COLLECTION = 'group-user-collection';
    public const string GROUP_USER_UPDATE = 'group-user-update';

    #[SerializedName('id')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION, self::GROUP_SERVICE_LOCATION_COLLECTION, self::GROUP_USER_COLLECTION])]
    public string $id;

    #[SerializedName('group_name')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION, self::GROUP_CREATE, self::GROUP_UPDATE])]
    public string $groupName;

    #[SerializedName('user_count')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?int $userCount = null;

    #[SerializedName('service_location_count')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?int $serviceLocationCount = null;

    /**
     * @var GroupSelected[]
     */
    #[Groups([self::GROUP_UPDATE, self::GROUP_CREATE])]
    public array $selectedServiceLocations;

    #[Groups([self::GROUP_SERVICE_LOCATION_COLLECTION])]
    public string $address;

    #[Groups([self::GROUP_SERVICE_LOCATION_COLLECTION])]
    public string $information;

    #[Groups([self::GROUP_USER_COLLECTION])]
    public string $email;

    #[Groups([self::GROUP_USER_COLLECTION])]
    public string $firstname;

    #[Groups([self::GROUP_USER_COLLECTION])]
    public string $lastname;

    #[Groups([self::GROUP_SERVICE_LOCATION_COLLECTION, self::GROUP_USER_COLLECTION])]
    public bool $selected;

    /**
     * @var GroupSelected[]
     */
    #[Groups([self::GROUP_USER_UPDATE])]
    public array $selectedUsers;
}
