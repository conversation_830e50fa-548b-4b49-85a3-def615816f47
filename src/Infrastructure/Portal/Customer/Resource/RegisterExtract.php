<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\RegisterExtractEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Enum\ContentType;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Post(
            controller: [RegisterExtractEndpoint::class, 'request'],
            name: 'register-extract-request',
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/register-extract-request',
            responseType: ContentType::EMPTY,
            successHttpCode: 200
        ),
    ],
    identifier: null,
    tag: 'RegisterExtract',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class RegisterExtract
{
    #[SerializedName('business_partner_id')]
    public string $businessPartnerId;

    #[SerializedName('verification_document_id')]
    public string $verificationDocumentId;

    #[SerializedName('service_location_id')]
    public string $serviceLocationId;

    #[SerializedName('date_from')]
    public string $dateFrom;

    #[SerializedName('date_to')]
    public string $dateTo;
}
