<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\BusinessPartnerEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [BusinessPartnerEndpoint::class, 'getOptions'],
            uriTemplate: '/business-partner/option',
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => ['options']],
            responseOpenApiSchemaName: 'BusinessPartnerOptionListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'BusinessPartner',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class BusinessPartner
{
    #[SerializedName('id')]
    #[Groups(['options'])]
    public string $id;

    #[SerializedName('display')]
    #[Groups(['options'])]
    public string $display;
}
