<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\OrderDateEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [OrderDateEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search field.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'serviceLocationId',
                    parameterDescription: 'Filter for service location',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'contractId',
                    parameterDescription: 'Filter for contract',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            responseOpenApiSchemaName: 'OrderDateListResponse'
        ),
    ],
    tag: 'OrderDate',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class OrderDate
{
    #[SerializedName('id')]
    public string $id;

    #[SerializedName('calendar_week')]
    public string $calendarWeek;

    #[SerializedName('date')]
    public string $date;

    #[SerializedName('material')]
    public ?string $material;

    #[SerializedName('container')]
    public ?string $container;

    #[SerializedName('service_location')]
    public ?string $serviceLocation = ''; // TODO

    #[SerializedName('contract')]
    public ?string $contract;

    #[SerializedName('service_type')]
    public ?string $serviceType;
}
