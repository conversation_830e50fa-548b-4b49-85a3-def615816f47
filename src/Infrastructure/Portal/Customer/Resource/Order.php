<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\OrderEndpoint;
use App\Infrastructure\Portal\Customer\Resource\Dto\Document;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [OrderEndpoint::class, 'getCollection'],
            denormalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'serviceLocationId',
                    parameterDescription: 'Filter for service location.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'contractId',
                    parameterDescription: 'Filter for contract.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for container.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'OrderListResponse',
        ),
        new GetCollection(
            controller: [OrderEndpoint::class, 'getContainerOptions'],
            uriTemplate: '/order/container/option',
            filters: [
                new Filter(
                    parameterName: 'serviceLocationId',
                    required: true,
                    parameterDescription: 'Filter for service location.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'contractId',
                    required: true,
                    parameterDescription: 'Filter for contract.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'serviceType',
                    required: true,
                    parameterDescription: 'Filter for service type.',
                    parameterFormat: 'string',
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_OPTION_CONTAINER]],
            responseOpenApiSchemaName: 'OrderContainerOptionResponse'
        ),
        new GetCollection(
            controller: [OrderEndpoint::class, 'getAgreementOptions'],
            uriTemplate: '/order/agreement/option',
            filters: [
                new Filter(
                    parameterName: 'serviceType',
                    required: true,
                    parameterDescription: 'Filter for Service Type.',
                    parameterFormat: 'string',
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_OPTION_AGREEMENT]],
            responseOpenApiSchemaName: 'OrderAgreementOptionResponse'
        ),
        new Post(
            controller: [OrderEndpoint::class, 'create'],
            denormalizationContext: ['groups' => [self::GROUP_CREATE]],
            requestOpenApiSchemaName: 'OrderCreateRequest',
            normalizationContext: ['groups' => [self::GROUP_CREATE]],
            responseOpenApiSchemaName: 'OrderCreateResponse',
        ),
        new Patch(
            controller: [OrderEndpoint::class, 'cancel'],
            name: 'order-cancel',
            description: 'Cancel order',
            uriTemplate: '/order/cancel/{id}',
            pathParameters: [new PathParameter(name: 'id')],
            denormalizationContext: ['groups' => []],
            requestOpenApiSchemaName: 'OrderCancelRequest',
            normalizationContext: ['groups' => [self::GROUP_CANCEL]],
            responseOpenApiSchemaName: 'OrderCancelResponse',
        ),
    ],
    identifier: 'id',
    tag: 'Order',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class Order
{
    public const string GROUP_CREATE = 'order:create';
    public const string GROUP_DETAIL = 'order:detail';
    public const string GROUP_COLLECTION = 'order:collection';
    private const string GROUP_CANCEL = 'order:cancel';
    private const string GROUP_OPTION_CONTAINER = 'order:option:container';
    private const string GROUP_OPTION_AGREEMENT = 'order:option:agreement';

    #[SerializedName('id')]
    #[Groups([self::GROUP_COLLECTION, self::GROUP_CANCEL, self::GROUP_OPTION_CONTAINER, self::GROUP_OPTION_AGREEMENT])]
    public string $id;

    #[SerializedName('date')]
    #[Groups([self::GROUP_CREATE, self::GROUP_COLLECTION])]
    public string $date;

    #[SerializedName('status')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $status;

    #[SerializedName('status_text')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $statusText;

    #[SerializedName('material_id')]
    #[Groups([self::GROUP_CREATE])]
    public string $materialId;

    #[SerializedName('material')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?string $material;

    #[SerializedName('container')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?string $container;

    #[SerializedName('service_location_id')]
    #[Groups([self::GROUP_CREATE])]
    public string $serviceLocationId;

    #[SerializedName('service_location')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?string $serviceLocation;

    #[SerializedName('service_type')]
    #[Groups([self::GROUP_CREATE])]
    public string $serviceType;

    #[SerializedName('service')]
    #[Groups([self::GROUP_COLLECTION])]
    public ?string $service;

    #[SerializedName('driver_information')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public ?string $driverInformation;

    /**
     * @var Document[]
     */
    #[SerializedName('documents')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public array $documents = [];

    #[SerializedName('contract_id')]
    #[Groups([self::GROUP_CREATE])]
    public string $contractId;

    #[SerializedName('amount')]
    #[Groups([self::GROUP_CREATE])]
    public int $amount;

    #[SerializedName('adr_text')]
    #[Groups([self::GROUP_CREATE])]
    public string $adrText;

    #[SerializedName('display')]
    #[Groups([self::GROUP_OPTION_CONTAINER, self::GROUP_OPTION_AGREEMENT])]
    public ?string $display;

    #[SerializedName('required')]
    #[Groups([self::GROUP_OPTION_AGREEMENT])]
    public ?bool $required;
}
