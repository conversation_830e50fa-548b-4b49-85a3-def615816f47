<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\DashboardEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [DashboardEndpoint::class, 'wasteStatistic'],
            uriTemplate: '/dashboard/waste-statistic',
            pathParameters: [],
            normalizationContext: ['groups' => [self::GROUP_WASTE_STATISTIC]],
            responseOpenApiSchemaName: 'DashboardWasteStatisticItemResponse'
        ),
        new Get(
            controller: [DashboardEndpoint::class, 'co2Report'],
            uriTemplate: '/dashboard/co2-report',
            pathParameters: [],
            normalizationContext: ['groups' => [self::GROUP_CO2_REPORT]],
            responseOpenApiSchemaName: 'DashboardCo2ReportItemResponse'
        ),
        new GetCollection(
            controller: [DashboardEndpoint::class, 'nextOrderDates'],
            uriTemplate: '/dashboard/next-order-date',
            pathParameters: [],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_NEXT_ORDER_DATES]],
            responseOpenApiSchemaName: 'DashboardNextOrderListResponse'
        ),
        new GetCollection(
            controller: [DashboardEndpoint::class, 'lastOrders'],
            uriTemplate: '/dashboard/last-order',
            pathParameters: [],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_LAST_ORDERS]],
            responseOpenApiSchemaName: 'DashboardLastOrderListResponse'
        ),
        new GetCollection(
            controller: [DashboardEndpoint::class, 'invoices'],
            uriTemplate: '/dashboard/invoice',
            pathParameters: [],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_INVOICE]],
            responseOpenApiSchemaName: 'DashboardInvoiceListResponse'
        ),
    ],
    tag: 'Dashboard',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class Dashboard
{
    private const string GROUP_WASTE_STATISTIC = 'dashboard:waste-statistic';

    private const string GROUP_CO2_REPORT = 'dashboard:co2-report';

    private const string GROUP_NEXT_ORDER_DATES = 'dashboard:next-order-dates';

    private const string GROUP_LAST_ORDERS = 'dashboard:last-orders';

    private const string GROUP_INVOICE = 'dashboard:invoice';

    #[SerializedName('id')]
    #[Groups([self::GROUP_NEXT_ORDER_DATES, self::GROUP_LAST_ORDERS, self::GROUP_INVOICE])]
    public ?string $id = null;

    #[SerializedName('amount')]
    #[Groups([self::GROUP_WASTE_STATISTIC, self::GROUP_CO2_REPORT, self::GROUP_INVOICE])]
    public string $amount;

    #[SerializedName('date')]
    #[Groups([self::GROUP_NEXT_ORDER_DATES, self::GROUP_LAST_ORDERS, self::GROUP_INVOICE])]
    public string $date;

    #[SerializedName('container')]
    #[Groups([self::GROUP_NEXT_ORDER_DATES, self::GROUP_LAST_ORDERS])]
    public ?string $container;

    #[SerializedName('material')]
    #[Groups([self::GROUP_NEXT_ORDER_DATES])]
    public ?string $material;

    #[SerializedName('service')]
    #[Groups([self::GROUP_NEXT_ORDER_DATES, self::GROUP_LAST_ORDERS])]
    public ?string $service;

    #[SerializedName('status')]
    #[Groups([self::GROUP_LAST_ORDERS])]
    public string $status;

    #[SerializedName('status_text')]
    #[Groups([self::GROUP_LAST_ORDERS])]
    public string $statusText;

    #[SerializedName('description')]
    #[Groups([self::GROUP_LAST_ORDERS])]
    public ?string $description;

    #[SerializedName('number')]
    #[Groups([self::GROUP_INVOICE])]
    public string $number;

    #[SerializedName('file_path')]
    #[Groups([self::GROUP_INVOICE])]
    public ?string $filePath = null;
}
