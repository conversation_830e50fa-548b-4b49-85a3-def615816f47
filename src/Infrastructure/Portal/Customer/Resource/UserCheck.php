<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\UserCheckEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [UserCheckEndpoint::class, 'checkUsername'],
            name: 'portal-user-username-check',
            uriTemplate: '/user/check-username/{username}',
            pathParameters: [new PathParameter(name: 'username')],
            responses: [
                new Response(httpCode: 200, description: 'Username is available'),
                new Response(httpCode: 409, description: 'Username is already taken'),
            ],
            responseOpenApiSchemaName: 'UserCheckUsernameResponse'
        ),
        new Get(
            controller: [UserCheckEndpoint::class, 'checkEmail'],
            name: 'portal-user-email-check',
            uriTemplate: '/user/check-email/{email}',
            pathParameters: [new PathParameter(name: 'email')],
            responses: [
                new Response(httpCode: 200, description: 'Email is available'),
                new Response(httpCode: 409, description: 'Email is already taken'),
            ],
            responseOpenApiSchemaName: 'UserCheckEmailResponse'
        ),
    ],
    identifier: 'userId',
    tag: 'User Check',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class UserCheck
{
    #[SerializedName('userId')]
    public string $userId;
}
