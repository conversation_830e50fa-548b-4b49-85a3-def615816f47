<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\ServiceTypeEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ServiceTypeEndpoint::class, 'getOptions'],
            uriTemplate: '/order/service-type/option',
            filters: [
                new Filter(
                    parameterName: 'contractId',
                    required: true,
                    parameterDescription: 'Filter for contract.',
                    parameterFormat: 'string',
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => ['options']],
            responseOpenApiSchemaName: 'ServiceTypeOptionListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'Order',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ServiceType
{
    #[SerializedName('id')]
    #[Groups(['options'])]
    public string $id;

    #[SerializedName('display')]
    #[Groups(['options'])]
    public string $display;

    #[SerializedName('tooltip')]
    #[Groups(['options'])]
    public string $tooltip;
}
