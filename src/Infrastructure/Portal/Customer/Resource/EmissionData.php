<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\EmissionDataEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [EmissionDataEndpoint::class, 'getOverview'],
            uriTemplate: '/emission-data/overview',
            pathParameters: [],
            denormalizationContext: ['groups' => [self::GROUP_OVERVIEW]],
            filters: [
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::GROUP_OVERVIEW]],
            responseOpenApiSchemaName: 'EmissionDataReportListResponse'
        ),
        new GetCollection(
            controller: [EmissionDataEndpoint::class, 'getMaterial'],
            uriTemplate: '/emission-data/material',
            pathParameters: [],
            denormalizationContext: ['groups' => [self::GROUP_MATERIAL]],
            filters: [
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_MATERIAL]],
            responseOpenApiSchemaName: 'EmissionDataReportMaterialListResponse'
        ),
    ],
    identifier: null,
    tag: 'EmissionData',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class EmissionData
{
    private const string GROUP_OVERVIEW = 'emission-data:overview';
    private const string GROUP_MATERIAL = 'emission-data:material';

    #[SerializedName('material')]
    #[Groups([self::GROUP_OVERVIEW, self::GROUP_MATERIAL])]
    public string $material;

    #[SerializedName('total_weight')]
    #[Groups([self::GROUP_OVERVIEW])]
    public string $totalWeight;

    #[SerializedName('emissions')]
    #[Groups([self::GROUP_OVERVIEW, self::GROUP_MATERIAL])]
    public string $emissions;

    #[SerializedName('savings')]
    #[Groups([self::GROUP_OVERVIEW, self::GROUP_MATERIAL])]
    public string $savings;
}
