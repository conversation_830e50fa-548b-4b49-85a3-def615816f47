<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\EmissionDataTotalEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\Filter;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [EmissionDataTotalEndpoint::class, 'get'],
            uriTemplate: '/emission-data/total',
            pathParameters: [],
            filters: [
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            responseOpenApiSchemaName: 'EmissionDataTotalListResponse'
        ),
    ],
    identifier: null,
    tag: 'EmissionData',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class EmissionDataTotal
{
    #[SerializedName('total_emissions')]
    public string $totalEmissions;

    #[SerializedName('total_savings')]
    public string $totalSavings;
}
