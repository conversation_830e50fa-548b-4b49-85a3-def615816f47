<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\ContractServiceLocationEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [ContractServiceLocationEndpoint::class, 'getCollection'],
            uriTemplate: '/contract/{id}/container-service-location',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'The contract ID', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'ContractServiceLocationListResponse',
        ),
    ],
    tag: 'Contract',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class ContractServiceLocation
{
    private const string GROUP_COLLECTION = 'order-service-location:collection';

    #[SerializedName('id')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $id;

    #[SerializedName('container_service_location_id')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $containerServiceLocationId;

    #[SerializedName('container')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $container;

    #[SerializedName('service_location')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $serviceLocation;

    #[SerializedName('quantity')]
    #[Groups([self::GROUP_COLLECTION])]
    public float $quantity;
}
