<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\VerificationDocumentNumberEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [VerificationDocumentNumberEndpoint::class, 'getOptions'],
            uriTemplate: '/verification-document/option',
            routePriority: 100,
            responseOpenApiSchemaName: 'VerificationDocumentNumberOptionListResponse',
        ),
    ],
    tag: 'VerificationDocument',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class VerificationDocumentNumber
{
    #[SerializedName('id')]
    public string $id;

    #[SerializedName('display')]
    public string $display;
}
