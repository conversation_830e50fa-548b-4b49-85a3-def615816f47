<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource\Dto;

use App\Infrastructure\Portal\Management\Resource\ManagementSupplier;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class OrderConfirmation
{
    #[SerializedName('id')]
    #[Groups([ManagementSupplier::GROUP_DETAIL, ManagementSupplier::GROUP_UPDATE])]
    public string $id;

    #[SerializedName('email')]
    #[Groups([ManagementSupplier::GROUP_DETAIL, ManagementSupplier::GROUP_UPDATE, ManagementSupplier::GROUP_CREATE])]
    public string $email;

    #[SerializedName('name')]
    #[Groups([ManagementSupplier::GROUP_DETAIL, ManagementSupplier::GROUP_UPDATE, ManagementSupplier::GROUP_CREATE])]
    public string $name;
}
