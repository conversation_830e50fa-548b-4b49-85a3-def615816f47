<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource\Dto;

use App\Infrastructure\Portal\Customer\Resource\SupervisionDocument;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class SignData
{
    #[SerializedName('id')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $id;

    #[SerializedName('type')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $type;

    #[SerializedName('amount')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $amount;

    #[SerializedName('name')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $name;

    #[SerializedName('date')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public ?string $date;

    #[SerializedName('city')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $city;

    #[SerializedName('postal_code')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $postalCode;

    #[SerializedName('street')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $street;

    #[SerializedName('licence_plate_number')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public ?string $licencePlateNumber;

    #[SerializedName('trailer_licence_plate_number')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public ?string $trailerLicencePlateNumber;

    #[SerializedName('status')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $status;

    #[SerializedName('status_text')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public string $statusText;

    #[SerializedName('signer')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public ?string $signer;

    #[SerializedName('sign_date')]
    #[Groups([SupervisionDocument::GROUP_DETAIL])]
    public ?string $signDate;
}
