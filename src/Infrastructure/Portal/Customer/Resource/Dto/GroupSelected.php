<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource\Dto;

use App\Infrastructure\Portal\Customer\Resource\Group;
use App\Infrastructure\Portal\Management\Resource\ManagementBusinessPartner;
use Symfony\Component\Serializer\Attribute\Groups;

class GroupSelected
{
    #[Groups([Group::GROUP_UPDATE, Group::GROUP_CREATE, Group::GROUP_USER_UPDATE, ManagementBusinessPartner::GROUP_UPDATE, ManagementBusinessPartner::GROUP_USER_UPDATE, ManagementBusinessPartner::GROUP_CREATE, ManagementBusinessPartner::USER_CREATE, ManagementBusinessPartner::USER_DETAIL, ManagementBusinessPartner::USER_UPDATE])]
    public string $id;
}
