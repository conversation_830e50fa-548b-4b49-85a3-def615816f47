<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource\Dto;

use Symfony\Component\Serializer\Attribute\SerializedName;

class DisposalSite
{
    #[SerializedName('id')]
    public string $id;

    #[SerializedName('number')]
    public string $number;

    #[SerializedName('name')]
    public string $name;

    #[SerializedName('postalCode')]
    public string $postalCode;

    #[SerializedName('city')]
    public string $city;

    #[SerializedName('street')]
    public string $street;
}
