<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource\Dto;

use App\Infrastructure\Portal\Customer\Resource\WasteStatistic;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class ContractWasteReport
{
    #[SerializedName('contract')]
    #[Groups([WasteStatistic::GROUP_DETAIL])]
    public ?string $contract;

    #[SerializedName('avv_id')]
    #[Groups([WasteStatistic::GROUP_DETAIL])]
    public string $avvId;

    #[SerializedName('material')]
    #[Groups([WasteStatistic::GROUP_DETAIL])]
    public string $material;

    #[SerializedName('amount')]
    #[Groups([WasteStatistic::GROUP_DETAIL])]
    public string $amount;
}
