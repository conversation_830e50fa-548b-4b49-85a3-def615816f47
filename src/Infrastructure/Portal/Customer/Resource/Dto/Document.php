<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource\Dto;

use App\Infrastructure\Portal\Customer\Resource\Order;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class Document
{
    #[SerializedName('type')]
    #[Groups([Order::GROUP_DETAIL, Order::GROUP_COLLECTION])]
    public string $type;

    #[SerializedName('name')]
    #[Groups([Order::GROUP_DETAIL, Order::GROUP_COLLECTION])]
    public string $name;

    #[SerializedName('file_path')]
    #[Groups([Order::GROUP_DETAIL, Order::GROUP_COLLECTION])]
    public string $filePath;
}
