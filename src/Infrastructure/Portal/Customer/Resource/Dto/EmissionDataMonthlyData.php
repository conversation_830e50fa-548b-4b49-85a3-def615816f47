<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource\Dto;

use App\Infrastructure\Portal\Customer\Resource\EmissionDataMonthly;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

class EmissionDataMonthlyData
{
    #[SerializedName('date')]
    #[Groups([EmissionDataMonthly::GROUP_EMISSIONS, EmissionDataMonthly::GROUP_SAVINGS])]
    public string $date;

    #[SerializedName('emissions')]
    #[Groups([EmissionDataMonthly::GROUP_EMISSIONS])]
    public string $emissions;

    #[SerializedName('savings')]
    #[Groups([EmissionDataMonthly::GROUP_SAVINGS])]
    public string $savings;
}
