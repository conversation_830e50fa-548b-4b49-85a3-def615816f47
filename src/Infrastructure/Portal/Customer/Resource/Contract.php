<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\ContractEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [ContractEndpoint::class, 'get'],
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'ContractItemResponse',
        ),
        new GetCollection(
            controller: [ContractEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'serviceLocationId',
                    parameterDescription: 'Filter for service location.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Free text search for contract number, AVV number, material and container.',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'ContractListResponse',
        ),
        new GetCollection(
            controller: [ContractEndpoint::class, 'getOptions'],
            uriTemplate: '/contract/option',
            filters: [
                new Filter(
                    parameterName: 'serviceLocationId',
                    parameterDescription: 'Filter for service location.',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_OPTION]],
            responseOpenApiSchemaName: 'ContractOptionListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'Contract',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class Contract
{
    private const string GROUP_DETAIL = 'order:detail';
    private const string GROUP_COLLECTION = 'order:collection';
    private const string GROUP_OPTION = 'order:option';

    #[SerializedName('id')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION, self::GROUP_OPTION])]
    public string $id;

    #[SerializedName('display')]
    #[Groups([self::GROUP_OPTION])]
    public string $display;

    #[SerializedName('contract_number')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $contractNumber;

    #[SerializedName('container')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $container;

    #[SerializedName('material')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $material;

    #[SerializedName('rental_type')]
    #[Groups([self::GROUP_DETAIL])]
    public string $rentType;
}
