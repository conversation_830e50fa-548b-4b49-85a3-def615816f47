<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\TranslationEndpoint;
use <PERSON><PERSON><PERSON>\Attribute\Property;
use Dok<PERSON>\OpenApi\Schema;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [TranslationEndpoint::class, 'getLocales'],
            uriTemplate: '/translation/locale',
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => ['locale']],
            responseOpenApiSchemaName: 'TranslationLocaleListResponse',
        ),
        new GetCollection(
            controller: [TranslationEndpoint::class, 'getTranslationsByIso'],
            uriTemplate: '/translation/locale/{iso}',
            pathParameters: [new PathParameter(name: 'iso')],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => ['iso']],
            responseOpenApiSchemaName: 'TranslationByIsoListResponse',
        ),
    ],
    identifier: 'iso',
    tag: 'Translation',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class Translation
{
    #[SerializedName('iso')]
    #[Groups(['locale'])]
    #[Property(new Schema(examples: ['de_DE', 'en_GB']))]
    public string $iso;

    #[SerializedName('name')]
    #[Groups(['locale'])]
    #[Property(new Schema(examples: ['Deutschland (deutsch)', 'United Kingdom (english)']))]
    public string $name;

    #[SerializedName('key')]
    #[Groups(['iso'])]
    #[Property(new Schema(examples: ['home_page_title']))]
    public string $key;

    #[SerializedName('value')]
    #[Groups(['iso'])]
    #[Property(new Schema(examples: ['Homepage']))]
    public string $value;
}
