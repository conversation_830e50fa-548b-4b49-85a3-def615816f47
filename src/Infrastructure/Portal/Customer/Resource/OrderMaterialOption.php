<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\OrderMaterialOptionEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [OrderMaterialOptionEndpoint::class, 'getOptions'],
            uriTemplate: '/order/material/option',
            filters: [
                new Filter(
                    parameterName: 'contractId',
                    required: true,
                    parameterDescription: 'Filter for contract.',
                    parameterFormat: 'string',
                ),
            ],
            responseOpenApiSchemaName: 'OrderMaterialOptionListResponse',
        ),
    ],
    tag: 'Order',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class OrderMaterialOption
{
    #[SerializedName('id')]
    public string $id;

    #[SerializedName('display')]
    public string $display;
}
