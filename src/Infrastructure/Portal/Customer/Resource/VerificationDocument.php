<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\VerificationDocumentEndpoint;
use App\Infrastructure\Portal\Customer\Resource\Dto\DisposalSite;
use App\Infrastructure\Portal\Customer\Resource\Dto\VerificationDocumentServiceLocation;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [VerificationDocumentEndpoint::class, 'get'],
            routePriority: 10,
            normalizationContext: ['groups' => [self::GROUP_DETAIL]],
            responseOpenApiSchemaName: 'VerificationDocumentItemResponse',
        ),
        new GetCollection(
            controller: [VerificationDocumentEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Free text search for status and avv id.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'status',
                    parameterDescription: 'Filter by status.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            normalizationContext: ['groups' => [self::GROUP_COLLECTION]],
            responseOpenApiSchemaName: 'VerificationDocumentListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'VerificationDocument',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class VerificationDocument
{
    private const string GROUP_DETAIL = 'verification-document:detail';

    private const string GROUP_COLLECTION = 'verification-document:collection';

    #[SerializedName('id')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $id;

    #[SerializedName('certificate_number')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $certificateNumber;

    #[SerializedName('avv_id')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $avvId;

    #[SerializedName('description')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $description;

    #[SerializedName('service_location_description')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $serviceLocationDescription;

    #[SerializedName('disposal_site_description')]
    #[Groups([self::GROUP_COLLECTION])]
    public string $disposalSiteDescription;

    #[SerializedName('end_date')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $endDate;

    #[SerializedName('remaining_quantity')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $remainingQuantity;

    #[SerializedName('status')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $status;

    #[SerializedName('status_text')]
    #[Groups([self::GROUP_DETAIL, self::GROUP_COLLECTION])]
    public string $statusText;

    #[SerializedName('total_quantity')]
    #[Groups([self::GROUP_DETAIL])]
    public string $totalQuantity;

    #[SerializedName('approved_date')]
    #[Groups([self::GROUP_DETAIL])]
    public string $approvedDate;

    #[SerializedName('business_partner')]
    #[Groups(['get'])]
    public string $businessPartner;

    #[SerializedName('valid_to')]
    #[Groups([self::GROUP_DETAIL])]
    public string $validTo;

    #[SerializedName('service_location')]
    #[Groups([self::GROUP_DETAIL])]
    public VerificationDocumentServiceLocation $serviceLocation;

    #[SerializedName('disposal_site')]
    #[Groups([self::GROUP_DETAIL])]
    public DisposalSite $disposalSite;
}
