<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\SupervisionDocumentStatusEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [SupervisionDocumentStatusEndpoint::class, 'getOptions'],
            uriTemplate: '/supervision-document/status/option',
            pagination: Pagination::NONE,
            responseOpenApiSchemaName: 'SupervisionDocumentStatusOptionListResponse',
        ),
    ],
    tag: 'SupervisionDocument',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class SupervisionDocumentStatus
{
    #[SerializedName('id')]
    public string $id;

    #[SerializedName('display')]
    public string $display;
}
