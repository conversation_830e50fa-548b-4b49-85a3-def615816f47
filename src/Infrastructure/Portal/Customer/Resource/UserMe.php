<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\UserMeEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints as Assert;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [UserMeEndpoint::class, 'getProfile'],
            name: 'portal-user-profile',
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/user/me',
            pathParameters: [],
            normalizationContext: ['groups' => [self::GROUP_PROFILE_DETAIL]],
            responseOpenApiSchemaName: 'UserProfileResponse'
        ),
    ],
    identifier: 'userId',
    tag: 'User Me',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class UserMe
{
    private const string GROUP_PROFILE_DETAIL = 'user:profile:detail';

    #[SerializedName('userId')]
    #[Groups([self::GROUP_PROFILE_DETAIL])]
    public string $userId;

    #[SerializedName('username')]
    #[Assert\Length(min: 1, max: 255)]
    #[Groups([self::GROUP_PROFILE_DETAIL])]
    public ?string $userName = null;

    #[SerializedName('firstname')]
    #[Assert\Length(min: 1, max: 255)]
    #[Groups([self::GROUP_PROFILE_DETAIL])]
    public ?string $firstName = null;

    #[SerializedName('lastname')]
    #[Assert\Length(min: 1, max: 255)]
    #[Groups([self::GROUP_PROFILE_DETAIL])]
    public ?string $lastName = null;

    /**
     * @var string[]
     */
    #[Groups([self::GROUP_PROFILE_DETAIL])]
    public ?array $groups = [];

    #[Assert\Length(min: 1, max: 255)]
    #[Assert\Email]
    #[Groups([self::GROUP_PROFILE_DETAIL])]
    public ?string $email = null;

    /**
     * @var string[]
     */
    #[Groups([self::GROUP_PROFILE_DETAIL])]
    public ?array $permissions = [];
}
