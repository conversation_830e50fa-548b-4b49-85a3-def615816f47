<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\VerificationDocumentSupervisionDocumentEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [VerificationDocumentSupervisionDocumentEndpoint::class, 'getCollection'],
            uriTemplate: '/verification-document/{id}/supervision-document',
            pathParameters: [new PathParameter(name: 'id')],
            responseOpenApiSchemaName: 'VerificationDocumentSupervisionDocumentListResponse',
        ),
    ],
    tag: 'VerificationDocument',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class VerificationDocumentSupervisionDocument
{
    #[SerializedName('id')]
    public string $id;

    #[SerializedName('supervision_document_number')]
    public string $supervisionDocumentNumber;

    #[SerializedName('amount_disposer')]
    public string $amountDisposer;

    #[SerializedName('date')]
    public string $date;
}
