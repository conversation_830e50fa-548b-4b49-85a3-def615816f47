<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\InvoiceEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use Symfony\Component\Serializer\Attribute\SerializedName;
use Symfony\Component\Validator\Constraints\Date;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [InvoiceEndpoint::class, 'getCollection'],
            filters: [
                new Filter(
                    parameterName: 'dateFrom',
                    parameterDescription: 'Date from filter.',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
                new Filter(
                    parameterName: 'dateTo',
                    parameterDescription: 'Date to filter',
                    parameterFormat: 'date',
                    validators: [new Date()],
                ),
            ],
            responseOpenApiSchemaName: 'InvoiceListResponse',
        ),
    ],
    tag: 'Invoice',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class Invoice
{
    #[SerializedName('id')]
    public string $id;

    #[SerializedName('date')]
    public string $date;

    #[SerializedName('document_number')]
    public string $documentNumber;

    #[SerializedName('document_type')]
    public string $documentType;

    #[SerializedName('net_amount')]
    public string $netAmount;

    #[SerializedName('file_path')]
    public string $filePath;
}
