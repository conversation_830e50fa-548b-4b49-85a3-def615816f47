<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\UserEndpoint;
use App\Infrastructure\Portal\Customer\Resource\Dto\UserSelected;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Attribute\Value\Response;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [UserEndpoint::class, 'getCollection'],
            uriTemplate: '/user',
            denormalizationContext: ['groups' => [self::USER_COLLECTION]],
            filters: [
                new Filter(
                    parameterName: 'search',
                    parameterDescription: 'Search for email, groups and roles.',
                    parameterFormat: 'string',
                ),
            ],
            normalizationContext: ['groups' => [self::USER_COLLECTION]],
            responseOpenApiSchemaName: 'UserListResponse',
        ),
        new Get(
            controller: [UserEndpoint::class, 'get'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/user/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_DETAIL]],
            normalizationContext: ['groups' => [self::USER_DETAIL]],
            responseOpenApiSchemaName: 'UserItemResponse',
        ),
        new Post(
            controller: [UserEndpoint::class, 'create'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/user',
            denormalizationContext: ['groups' => [self::USER_CREATE]],
            requestOpenApiSchemaName: 'UserCreateRequest',
            normalizationContext: ['groups' => [self::USER_CREATE]],
            responseOpenApiSchemaName: 'UserCreateResponse',
        ),
        new Patch(
            controller: [UserEndpoint::class, 'update'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/user/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            denormalizationContext: ['groups' => [self::USER_UPDATE]],
            requestOpenApiSchemaName: 'UserUpdateRequest',
            normalizationContext: ['groups' => [self::USER_UPDATE]],
            responseOpenApiSchemaName: 'UserUpdateResponse',
        ),
        new Delete(
            controller: [UserEndpoint::class, 'delete'],
            security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
            uriTemplate: '/user/{id}',
            pathParameters: [
                new PathParameter(name: 'id', type: 'string', description: 'Id parameter', constraint: '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}'),
            ],
            responses: [
                new Response(httpCode: 204, description: 'User deleted'),
            ],
            responseOpenApiSchemaName: 'UserDeleteResponse',
        ),
        new GetCollection(
            controller: [UserEndpoint::class, 'getPermissionOptions'],
            uriTemplate: '/user/permissions-option',
            denormalizationContext: ['groups' => [self::USER_PERMISSION_OPTION]],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::USER_PERMISSION_OPTION]],
            responseOpenApiSchemaName: 'UserPermissionListResponse',
        ),
        new GetCollection(
            controller: [UserEndpoint::class, 'getGroupOptions'],
            uriTemplate: '/user/groups-option',
            denormalizationContext: ['groups' => [self::USER_GROUP_OPTION]],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => [self::USER_GROUP_OPTION]],
            responseOpenApiSchemaName: 'UserGroupListResponse',
        ),
    ],
    identifier: 'id',
    tag: 'User',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class User
{
    public const string USER_COLLECTION = 'user:collection';
    public const string USER_DETAIL = 'user:detail';
    public const string USER_CREATE = 'user:create';
    public const string USER_UPDATE = 'user:update';
    public const string USER_GROUP_OPTION = 'user:group-option';
    public const string USER_PERMISSION_OPTION = 'user:permission-option';

    #[Groups([self::USER_COLLECTION, self::USER_DETAIL, self::USER_UPDATE, self::USER_GROUP_OPTION, self::USER_PERMISSION_OPTION])]
    public string $id;

    #[Groups([self::USER_COLLECTION, self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public string $email;

    #[Groups([self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public ?string $firstname = null;

    #[Groups([self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public ?string $lastname = null;

    #[Groups([self::USER_COLLECTION])]
    public string $name;

    #[Groups([self::USER_COLLECTION])]
    public string $groupsText;

    /**
     * @var UserSelected[]
     */
    #[Groups([self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public array $groups = [];

    #[Groups([self::USER_COLLECTION])]
    public string $permissionsText;

    /**
     * @var string[]
     */
    #[Groups([self::USER_DETAIL, self::USER_CREATE, self::USER_UPDATE])]
    public array $permissions = [];

    #[SerializedName('display')]
    #[Groups([self::USER_GROUP_OPTION, self::USER_PERMISSION_OPTION])]
    public string $display;

    #[SerializedName('tooltip')]
    #[Groups([self::USER_PERMISSION_OPTION])]
    public string $tooltip;
}
