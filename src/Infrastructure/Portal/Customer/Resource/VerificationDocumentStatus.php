<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\VerificationDocumentStatusEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new GetCollection(
            controller: [VerificationDocumentStatusEndpoint::class, 'getOptions'],
            uriTemplate: '/verification-document/status/option',
            pagination: Pagination::NONE,
            responseOpenApiSchemaName: 'VerificationDocumentStatusOptionListResponse',
        ),
    ],
    tag: 'VerificationDocument',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class VerificationDocumentStatus
{
    #[SerializedName('id')]
    public string $id;

    #[SerializedName('display')]
    public string $display;
}
