<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Resource;

use App\Domain\Entity\Enum\UserRole;
use App\Infrastructure\Portal\Customer\HttpEndpoint\OrderDatePickerEndpoint;
use App\Infrastructure\Portal\Customer\Resource\Dto\DatePickerDate;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Value\Filter;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'portal',
    operations: [
        new Get(
            controller: [OrderDatePickerEndpoint::class, 'getOptions'],
            uriTemplate: '/order/date-picker/option',
            filters: [
                new Filter(
                    parameterName: 'serviceLocationId',
                    required: true,
                    parameterDescription: 'Filter for service location.',
                    parameterFormat: 'string',
                ),
                new Filter(
                    parameterName: 'contractId',
                    required: true,
                    parameterDescription: 'Filter for contract.',
                    parameterFormat: 'string',
                ),
            ],
            responseOpenApiSchemaName: 'OrderDatePickerOptionResponse'
        ),
    ],
    identifier: null,
    tag: 'Order',
    security: 'is_granted("'.UserRole::ROLE_PORTAL->value.'")',
)]
class OrderDatePicker
{
    #[SerializedName('default')]
    public string $default;

    /**
     * @var DatePickerDate[]
     */
    #[SerializedName('allowed_dates')]
    public array $allowedDates = [];

    /**
     * @var DatePickerDate[]
     */
    #[SerializedName('blocked_dates')]
    public array $blockedDates = [];
}
