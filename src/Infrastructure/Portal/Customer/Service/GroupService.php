<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Service;

use App\Domain\Entity\BusinessPartner as BusinessPartnerEntity;
use App\Domain\Entity\Group as GroupEntity;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\GroupRepository;
use App\Exception\NotFoundException;

readonly class GroupService
{
    public function __construct(
        private BusinessPartnerRepository $businessPartnerRepository,
        private GroupRepository $groupRepository,
    ) {
    }

    public function getBusinessPartner(string $businessPartnerId): BusinessPartnerEntity
    {
        $businessPartner = $this->businessPartnerRepository->find(id: $businessPartnerId);

        if (null === $businessPartner) {
            throw new NotFoundException(message: "Business partner $businessPartnerId not found");
        }

        return $businessPartner;
    }

    public function getGroup(?string $groupId): ?GroupEntity
    {
        if (null === $groupId) {
            return null;
        }

        return $this->groupRepository->find(id: $groupId);
    }
}
