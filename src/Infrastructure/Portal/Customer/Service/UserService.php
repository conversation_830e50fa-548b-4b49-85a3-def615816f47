<?php

declare(strict_types=1);

namespace App\Infrastructure\Portal\Customer\Service;

use App\Domain\Entity\BusinessPartner as BusinessPartnerEntity;
use App\Domain\Entity\Group as GroupEntity;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\GroupRepository;
use App\Exception\NotFoundException;
use App\Infrastructure\Portal\Customer\Resource\Dto\UserSelected;
use App\Infrastructure\Portal\Customer\Resource\User;

readonly class UserService
{
    public function __construct(
        private BusinessPartnerRepository $businessPartnerRepository,
        private GroupRepository $groupRepository,
    ) {
    }

    public function getBusinessPartner(string $businessPartnerId): BusinessPartnerEntity
    {
        $businessPartner = $this->businessPartnerRepository->find(id: $businessPartnerId);

        if (null === $businessPartner) {
            throw new NotFoundException(message: 'Business partner not found');
        }

        return $businessPartner;
    }

    /**
     * @return array<int, GroupEntity>
     */
    public function findGroups(User $user): array
    {
        /** @var array<int, string> $groupIds */
        $groupIds = array_map(
            callback: fn (UserSelected $selected): string => $selected->id,
            array: $user->groups,
        );

        return $this->groupRepository->findByIds(groups: $groupIds);
    }
}
