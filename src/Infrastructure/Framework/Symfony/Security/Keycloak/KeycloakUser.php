<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Symfony\Security\Keycloak;

use App\Domain\Entity\Enum\Tenant;
use Symfony\Component\Security\Core\User\UserInterface;

readonly class KeycloakUser implements UserInterface
{
    private const array ROLE_PERMISSION_MAP = [
        'myprezero-portal-access' => 'ROLE_PORTAL',
    ];

    /** @var array<string> */
    private array $roles;

    /**
     * @param string[] $realmRoles
     */
    public function __construct(
        private string $username,
        private array $realmRoles,
        private Tenant $tenant,
        private ?string $email = null,
        private ?string $firstName = null,
        private ?string $lastName = null,
    ) {
        $roles = [];

        foreach ($this->realmRoles as $realmRole) {
            if (isset(self::ROLE_PERMISSION_MAP[$realmRole])) {
                $roles[] = self::ROLE_PERMISSION_MAP[$realmRole];
            }
        }

        $this->roles = array_values(array: array_unique(array: $roles));
    }

    public function getRoles(): array
    {
        return $this->roles;
    }

    public function eraseCredentials(): void
    {
    }

    public function getUserIdentifier(): string
    {
        assert(assertion: '' !== $this->username);

        return $this->username;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function getTenant(): Tenant
    {
        return $this->tenant;
    }

    public function getEmail(): ?string
    {
        return null === $this->email || '' === $this->email ? null : $this->email;
    }

    public function getFirstName(): ?string
    {
        return null === $this->firstName || '' === $this->firstName ? null : $this->firstName;
    }

    public function getLastName(): ?string
    {
        return null === $this->lastName || '' === $this->lastName ? null : $this->lastName;
    }
}
