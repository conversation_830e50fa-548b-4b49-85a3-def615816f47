<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Symfony\Security\Keycloak;

use App\Domain\Entity\Enum\Tenant;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Security\Core\User\AttributesBasedUserProviderInterface;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * @implements AttributesBasedUserProviderInterface<KeycloakUser>
 */
readonly class KeycloakUserProvider implements AttributesBasedUserProviderInterface
{
    public function __construct(
        private LoggerInterface $logger,
    ) {
    }

    public function refreshUser(UserInterface $user): UserInterface
    {
        assert(assertion: $user instanceof KeycloakUser, description: 'User must be an instance of KeycloakUser');

        return $user;
    }

    public function supportsClass(string $class): bool
    {
        return KeycloakUser::class === $class;
    }

    /**
     * @param array<string, mixed> $attributes
     */
    public function loadUserByIdentifier(string $identifier, array $attributes = []): UserInterface
    {
        $this->logger->debug(
            'Keycloak user provider load user by identifier',
            ['identifier' => $identifier, 'attributes' => $attributes]
        );

        if (!isset($attributes['tenant']) || !is_string(value: $attributes['tenant'])) {
            throw new UnauthorizedHttpException(challenge: 'Tenant attribute is required');
        }

        if (!isset($attributes['scope']) || !is_string(value: $attributes['scope'])) {
            throw new UnauthorizedHttpException(challenge: 'Scope attribute is required');
        }

        $realmRoles = [];

        if (
            isset($attributes['resource_access']['myprezero-backend']['roles']) // @phpstan-ignore-line
            && is_array(value: $attributes['resource_access']['myprezero-backend']['roles'])
        ) {
            /** @phpstan-var string[] $realmRoles */
            $realmRoles = $attributes['resource_access']['myprezero-backend']['roles'];
        }

        return new KeycloakUser(
            username: $identifier,
            realmRoles: $realmRoles,
            tenant: Tenant::from(value: $attributes['tenant']),
            email: isset($attributes['email']) && is_string(value: $attributes['email']) ? $attributes['email'] : null,
            firstName: isset($attributes['given_name']) && is_string(value: $attributes['given_name'])
                ? $attributes['given_name']
                : null,
            lastName: isset($attributes['family_name']) && is_string(value: $attributes['family_name'])
                ? $attributes['family_name']
                : null,
        );
    }
}
