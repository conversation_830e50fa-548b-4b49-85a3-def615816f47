<?php

declare(strict_types=1);

namespace App\Infrastructure\Framework\Database\EventSubscriber;

use App\Domain\Entity\Enum\SystemUser;
use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\User;
use App\Domain\Security\Security;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Symfony\Contracts\Service\ResetInterface;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
class DoctrineSetUserSpecificFields implements ResetInterface
{
    private ?User $currentUser = null;

    public function __construct(
        private readonly Security $security,
    ) {
    }

    public function setCurrentUser(?User $user): void
    {
        $this->currentUser = $user;
    }

    /**
     * @return array<string>
     */
    public function getSubscribedEvents(): array
    {
        return [
            Events::prePersist,
            Events::preUpdate,
        ];
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        $entity = $args->getObject();

        if ($entity instanceof EntityInterface && (null === $entity->getCreatedBy() || SystemUser::DEFAULT->value === $entity->getCreatedBy())) {
            $this->setEntityCreatedBy(entity: $entity);
            $this->setEntityModifiedBy(entity: $entity);
        }
    }

    public function preUpdate(PreUpdateEventArgs $args): void
    {
        $entity = $args->getObject();

        if ($entity instanceof EntityInterface) {
            $this->setEntityModifiedBy(entity: $entity);
        }
    }

    private function setEntityCreatedBy(EntityInterface $entity): void
    {
        if (null !== $this->currentUser) {
            $entity->setCreatedBy($this->currentUser->getId());

            return;
        }

        if ($this->security->hasAuthenticatedUser()) {
            $entity->setCreatedBy($this->security->getAuthenticatedUser()->getId());
        }
    }

    private function setEntityModifiedBy(EntityInterface $entity): void
    {
        $entity->setModifiedAt(new \DateTimeImmutable());

        if (null !== $this->currentUser) {
            $entity->setModifiedBy($this->currentUser->getId());

            return;
        }

        if ($this->security->hasAuthenticatedUser()) {
            $entity->setModifiedBy($this->security->getAuthenticatedUser()->getId());
        }
    }

    public function reset(): void
    {
        $this->currentUser = null;
    }
}
