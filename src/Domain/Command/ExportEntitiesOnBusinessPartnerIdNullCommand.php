<?php

declare(strict_types=1);

namespace App\Domain\Command;

use App\Domain\Entity\Enum\Tenant;
use App\Domain\Service\ExportEntitiesOnBusinessPartnerIdNullService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Console\Attribute\Argument;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Attribute\Option;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

#[AsCommand(
    name: 'app:export-bpid-null',
    description: 'Exports all rows of all entities where BusinessPartnerId (uuid) is null.')]
class ExportEntitiesOnBusinessPartnerIdNullCommand
{
    private const string PATH = 'App\\Domain\\Entity\\';

    public function __construct(
        #[Autowire('%kernel.project_dir%')]
        private readonly string $projectDir,
        private readonly ManagerRegistry $registry,
        private readonly ExportEntitiesOnBusinessPartnerIdNullService $exportEntitiesOnBusinessPartnerIdNullService,
    ) {
    }

    public function __invoke(
        InputInterface $input,
        OutputInterface $output,
        #[Argument(
            description: 'Name of the entity to export.',
            name: 'entity'
        )] ?string $entityName = null,
        #[Argument(
            description: "Last date the entity has been modified on in 'Y-m-d' format.",
            name: 'date'
        )] ?string $date = null,
        #[Argument(
            description: "Last time the entity has been modified at in 'H:i:s' format.",
            name: 'time'
        )] ?string $time = null,
        #[Option(
            description: "Filter type for the date ('on', 'before' or 'after'). Defaults to 'on'.",
            name: 'operator'
        )] string $operator = 'on',
    ): int {
        $em = $this->registry->getManager();
        if (!$em instanceof EntityManagerInterface) {
            throw new \LogicException(message: 'Invalid entity manager.');
        }

        $filters = $em->getFilters();
        if ($filters->isEnabled(name: 'tenant')) {
            $tenantFilter = $filters->getFilter(name: 'tenant');
            $tenantFilter->setParameter(name: 'tenant', value: Tenant::EUROPE->value);
        }

        $dirPath = $this->projectDir.'/var/csv/';
        $filePath = $dirPath.'export.csv';
        if (!is_dir(filename: $dirPath)) {
            mkdir(directory: $dirPath, permissions: 0755, recursive: true);
        } elseif (is_file(filename: $filePath)) {
            unlink(filename: $filePath);
        }

        $modifiedAt = $this->exportEntitiesOnBusinessPartnerIdNullService->resolveModifiedAt(date: $date, time: $time);
        $operator = $this->exportEntitiesOnBusinessPartnerIdNullService->resolveOperator(operator: $operator);

        if (null !== $entityName) {
            return $this->resolveEntityName(
                entityName: $entityName,
                modifiedAt: $modifiedAt,
                operator: $operator,
                filePath: $filePath,
                input: $input,
                output: $output
            );
        }

        /** @var array<string> $entities */
        $entities = $this->exportEntitiesOnBusinessPartnerIdNullService->getAllEntitiesWithBusinessPartner();

        return $this->resolveAllEntities(
            entities: $entities,
            modifiedAt: $modifiedAt,
            operator: $operator,
            filePath: $filePath,
            input: $input,
            output: $output
        );
    }

    private function resolveEntityName(
        string $entityName,
        ?\DateTimeImmutable $modifiedAt,
        string $operator,
        string $filePath,
        InputInterface $input,
        OutputInterface $output,
    ): int {
        $io = new SymfonyStyle(input: $input, output: $output);

        /** @var class-string<object> $entity */
        $entity = self::PATH.$entityName;

        $count = $this->exportEntitiesOnBusinessPartnerIdNullService->queryBusinessPartnerIdNull(
            entity: $entity,
            count: true,
            modifiedAt: $modifiedAt,
            operator: $operator
        )->getSingleScalarResult();
        if (0 !== $count) {
            $io->info(message: $entity.' ('.$count.')');
        }

        if (!$this->confirmExport(count: (int) $count, input: $input, output: $output)) {
            return Command::SUCCESS;
        }

        /** @var iterable<object> $data */
        $data = $this->exportEntitiesOnBusinessPartnerIdNullService->queryBusinessPartnerIdNull(
            entity: $entity,
            count: false,
            modifiedAt: $modifiedAt,
            operator: $operator
        )->toIterable();
        $this->exportEntitiesOnBusinessPartnerIdNullService->exportToCSV(arr: $data, name: $entityName, filename: $filePath, mode: 'w+');

        $message = sprintf('CSV with all rows where the Business Partner Id (uuid) is null has been created in %s.', $filePath);
        $io->success(message: $message);

        return Command::SUCCESS;
    }

    /**
     * @param array<string> $entities
     */
    private function resolveAllEntities(
        array $entities,
        ?\DateTimeImmutable $modifiedAt,
        string $operator,
        string $filePath,
        InputInterface $input,
        OutputInterface $output,
    ): int {
        $io = new SymfonyStyle(input: $input, output: $output);

        $totalCount = 0;
        foreach ($entities as $entity) {
            $count = $this->exportEntitiesOnBusinessPartnerIdNullService->queryBusinessPartnerIdNull(
                entity: $entity,
                count: true,
                modifiedAt: $modifiedAt,
                operator: $operator
            )->getSingleScalarResult();

            if (0 !== $count) {
                $totalCount += (int) $count;
                $io->info(message: $entity.' ('.$count.')');
            }
        }

        if (!$this->confirmExport(count: $totalCount, input: $input, output: $output)) {
            return Command::SUCCESS;
        }

        foreach ($entities as $entity) {
            /** @var iterable<object> $data */
            $data = $this->exportEntitiesOnBusinessPartnerIdNullService->queryBusinessPartnerIdNull(
                entity: $entity,
                count: false,
                modifiedAt: $modifiedAt,
                operator: $operator
            )->toIterable();
            $this->exportEntitiesOnBusinessPartnerIdNullService->exportToCSV(arr: $data, name: $entity, filename: $filePath, mode: 'a');
        }

        $message = sprintf('CSV with all rows where the Business Partner Id (uuid) is null has been created in %s.', $filePath);
        $io->success(message: $message);

        return Command::SUCCESS;
    }

    private function confirmExport(int $count, InputInterface $input, OutputInterface $output): bool
    {
        $io = new SymfonyStyle(input: $input, output: $output);

        if (0 == $count) {
            $io->warning(message: 'Nothing to export.');

            return false;
        }

        $helper = new QuestionHelper();
        $question = new ConfirmationQuestion(question: 'Do you want to continue with the export? (Y/N)', default: false);
        if (!$helper->ask(input: $input, output: $output, question: $question)) {
            $io->warning(message: 'Export has been cancelled.');

            return false;
        }

        return true;
    }
}
