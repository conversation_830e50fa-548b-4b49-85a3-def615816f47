<?php

declare(strict_types=1);

namespace App\Domain\Command;

use App\Domain\Entity\Enum\Tenant;
use App\Domain\Service\DeleteEntitiesOnBusinessPartnerIdNullService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Console\Attribute\Argument;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Attribute\Option;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:delete-bpid-null',
    description: 'Delete all rows of all entities where BusinessPartnerId (uuid) is null.')]
class DeleteEntitiesOnBusinessPartnerIdNullCommand
{
    private const string PATH = 'App\\Domain\\Entity\\';

    public function __construct(
        private readonly ManagerRegistry $registry,
        private readonly DeleteEntitiesOnBusinessPartnerIdNullService $deleteEntitiesOnBusinessPartnerIdNullService,
    ) {
    }

    public function __invoke(
        InputInterface $input,
        OutputInterface $output,
        #[Argument(
            description: "Last date the entity has been modified on in 'Y-m-d' format.",
            name: 'date'
        )] string $date,
        #[Argument(
            description: "Last time the entity has been modified at in 'H:i:s' format.",
            name: 'time'
        )] string $time,
        #[Argument(
            description: 'Name of the entity to delete.',
            name: 'entity'
        )] ?string $entityName = null,
        #[Option(
            description: "Filter type for the date time ('on', 'before' or 'after'). Defaults to 'on'.",
            name: 'operator',
        )] string $operator = 'on',
    ): int {
        $em = $this->registry->getManager();
        if (!$em instanceof EntityManagerInterface) {
            throw new \LogicException(message: 'Invalid entity manager.');
        }

        $filters = $em->getFilters();
        if ($filters->isEnabled(name: 'tenant')) {
            $tenantFilter = $filters->getFilter(name: 'tenant');
            $tenantFilter->setParameter(name: 'tenant', value: Tenant::EUROPE->value);
        }

        $modifiedAt = $this->deleteEntitiesOnBusinessPartnerIdNullService->resolveModifiedAt(date: $date, time: $time);
        $operator = $this->deleteEntitiesOnBusinessPartnerIdNullService->resolveOperator(operator: $operator);

        if (null !== $entityName) {
            return $this->resolveEntityName(
                entityName: $entityName,
                modifiedAt: $modifiedAt,
                operator: $operator,
                em: $em,
                input: $input,
                output: $output
            );
        }

        /** @var array<string> $entities */
        $entities = $this->deleteEntitiesOnBusinessPartnerIdNullService->getAllEntitiesWithBusinessPartner();

        return $this->resolveAllEntities(
            entities: $entities,
            modifiedAt: $modifiedAt,
            operator: $operator,
            em: $em,
            input: $input,
            output: $output
        );
    }

    private function resolveEntityName(
        string $entityName,
        \DateTimeImmutable $modifiedAt,
        string $operator,
        EntityManagerInterface $em,
        InputInterface $input,
        OutputInterface $output,
    ): int {
        $io = new SymfonyStyle(input: $input, output: $output);

        /** @var class-string<object> $entity */
        $entity = self::PATH.$entityName;

        $count = $this->deleteEntitiesOnBusinessPartnerIdNullService->queryBusinessPartnerIdNull(
            entity: $entity,
            count: true,
            modifiedAt: $modifiedAt,
            operator: $operator
        )->getSingleScalarResult();
        if (0 !== $count) {
            $io->info(message: $entity.' ('.$count.')');
        }

        if (!$this->confirmDeletion(count: (int) $count, input: $input, output: $output)) {
            return Command::SUCCESS;
        }

        /** @var iterable<object> $results */
        $results = $this->deleteEntitiesOnBusinessPartnerIdNullService->queryBusinessPartnerIdNull(
            entity: $entity,
            count: false,
            modifiedAt: $modifiedAt,
            operator: $operator
        )->getResult();
        foreach ($results as $result) {
            $em->remove($result);
        }

        $em->flush();
        $io->success(message: 'Deletion was successful.');

        return Command::SUCCESS;
    }

    /**
     * @param array<string> $entities
     */
    private function resolveAllEntities(
        array $entities,
        \DateTimeImmutable $modifiedAt,
        string $operator,
        EntityManagerInterface $em,
        InputInterface $input,
        OutputInterface $output,
    ): int {
        $io = new SymfonyStyle(input: $input, output: $output);

        $totalCount = 0;
        foreach ($entities as $entity) {
            $count = $this->deleteEntitiesOnBusinessPartnerIdNullService->queryBusinessPartnerIdNull(
                entity: $entity,
                count: true,
                modifiedAt: $modifiedAt,
                operator: $operator
            )->getSingleScalarResult();

            if (0 !== $count) {
                $totalCount += (int) $count;
                $io->info(message: $entity.' ('.$count.')');
            }
        }

        if (!$this->confirmDeletion(count: $totalCount, input: $input, output: $output)) {
            return Command::SUCCESS;
        }

        foreach ($entities as $entity) {
            /** @var iterable<object> $results */
            $results = $this->deleteEntitiesOnBusinessPartnerIdNullService->queryBusinessPartnerIdNull(
                entity: $entity,
                count: false,
                modifiedAt: $modifiedAt,
                operator: $operator
            )->getResult();
            foreach ($results as $result) {
                $em->remove($result);
            }
        }

        $em->flush();
        $io->success(message: 'Deletion was successful.');

        return Command::SUCCESS;
    }

    private function confirmDeletion(int $count, InputInterface $input, OutputInterface $output): bool
    {
        $io = new SymfonyStyle(input: $input, output: $output);

        if (0 == $count) {
            $io->warning(message: 'Nothing to delete.');

            return false;
        }

        $helper = new QuestionHelper();
        $question = new ConfirmationQuestion(question: 'Do you want to continue with the deletion? (Y/N)', default: false);
        if (!$helper->ask(input: $input, output: $output, question: $question)) {
            $io->warning(message: 'Deletion has been cancelled.');

            return false;
        }

        return true;
    }
}
