<?php

declare(strict_types=1);

namespace App\Domain\Security;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\User;
use App\Domain\Repository\UserRepository;
use App\Domain\Service\ObjectStorage\FileObject;
use App\Domain\Service\ObjectStorage\ObjectMetadata;
use App\Infrastructure\Framework\Symfony\Security\Keycloak\KeycloakUser;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\SecurityBundle\Security as SymfonySecurity;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Security\Core\User\InMemoryUser;

readonly class Security
{
    public function __construct(
        private SymfonySecurity $security,
        private UserRepository $userRepository,
        private TenantContext $tenantContext,
        private LoggerInterface $logger,
    ) {
    }

    public function canAccessFileObject(FileObject $fileObject): bool
    {
        $objectMetadata = $fileObject->objectMetadata;

        if (null === $objectMetadata) {
            return false;
        }

        $fileTenantIdentifier = $this->getFileTenantIdentifier(objectMetadata: $objectMetadata);

        try {
            $user = $this->getAuthenticatedUser();

            return $objectMetadata->userIdentifier === $user->getUserIdentifier()
                || $fileTenantIdentifier === $user->getTenant()->value;
        } catch (UnauthorizedHttpException) {
            $user = $this->security->getUser();

            if ($user instanceof InMemoryUser) {
                return $fileTenantIdentifier === $this->tenantContext->getTenant()->value;
            }
        }

        return false;
    }

    public function hasAuthenticatedUser(): bool
    {
        $user = $this->security->getUser();

        return $user instanceof KeycloakUser;
    }

    public function getAuthenticatedUser(): User
    {
        $user = $this->security->getUser();

        if (null === $user) {
            throw new UnauthorizedHttpException(challenge: 'Bearer');
        }

        if (!$user instanceof KeycloakUser) {
            $this->logger->critical(
                'Tried to login with user that is not a KeycloakUser',
                ['username' => $user->getUserIdentifier(), 'class' => $user::class],
            );

            throw new UnauthorizedHttpException(challenge: 'Bearer');
        }

        $userEntity = $this->userRepository->getByUsername(username: $user->getUserIdentifier());

        if (null === $userEntity) {
            $this->logger->critical(
                'Tried to login with user that does not exist in local database',
                ['username' => $user->getUserIdentifier()],
            );

            throw new UnauthorizedHttpException(challenge: 'Bearer');
        }

        $userEntity->setRoles(roles: $user->getRoles());

        return $userEntity;
    }

    public function isGranted(string $attribute, object $subject): bool
    {
        return $this->security->isGranted(attributes: $attribute, subject: $subject);
    }

    private function getFileTenantIdentifier(ObjectMetadata $objectMetadata): ?string
    {
        if (null !== $objectMetadata->tenantIdentifier && '' !== $objectMetadata->tenantIdentifier) {
            return $objectMetadata->tenantIdentifier;
        }

        if (null === $objectMetadata->userIdentifier || '' === $objectMetadata->userIdentifier) {
            return null;
        }

        $user = $this->userRepository->getByUsername(username: $objectMetadata->userIdentifier);

        return $user?->getTenant()->value;
    }
}
