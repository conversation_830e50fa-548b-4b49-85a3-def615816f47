<?php

declare(strict_types=1);

namespace App\Domain\Context;

use App\Domain\Entity\Enum\Tenant;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\Service\ResetInterface;

class TenantContext implements ResetInterface
{
    private ?Tenant $tenant = null;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    public function getTenant(): Tenant
    {
        return $this->tenant ?? throw new \RuntimeException(message: 'Tenant not set');
    }

    public function hasTenant(): bool
    {
        return null !== $this->tenant;
    }

    public function setTenant(?Tenant $tenant): void
    {
        $this->tenant = $tenant;

        $this->entityManager
            ->getFilters()
            ->getFilter(name: 'tenant')
            ->setParameter(name: 'tenant', value: $tenant?->value);
    }

    public function reset(): void
    {
        $this->tenant = null;

        $this->entityManager
            ->getFilters()
            ->getFilter(name: 'tenant')
            ->setParameter(name: 'tenant', value: null);
    }
}
