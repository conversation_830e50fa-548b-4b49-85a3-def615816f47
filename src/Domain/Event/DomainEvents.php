<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\BankAccount;
use App\Domain\Entity\ContactPerson;
use App\Domain\Entity\IdentificationElement;
use App\Domain\Entity\Invoice;
use App\Domain\Entity\SalesOrganisation;
use App\Domain\Entity\SupervisionDocumentPartner;
use App\Domain\Entity\VerificationDocumentPartner;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

readonly class DomainEvents
{
    public function __construct(
        private EventDispatcherInterface $eventDispatcher,
    ) {
    }

    public function dispatchBankAccountCreated(BankAccount $bankAccount): void
    {
        $this->eventDispatcher->dispatch(
            event: new BankAccountCreatedEvent(
                bankAccount: $bankAccount
            ),
            eventName: BankAccountCreatedEvent::NAME,
        );
    }

    public function dispatchContactPersonCreated(ContactPerson $contactPerson): void
    {
        $this->eventDispatcher->dispatch(
            event: new ContactPersonCreatedEvent(
                contactPerson: $contactPerson
            ),
            eventName: ContactPersonCreatedEvent::NAME,
        );
    }

    public function dispatchIdentificationElementCreated(IdentificationElement $identificationElement): void
    {
        $this->eventDispatcher->dispatch(
            event: new IdentificationElementCreatedEvent(
                identificationElement: $identificationElement
            ),
            eventName: IdentificationElementCreatedEvent::NAME,
        );
    }

    public function dispatchInvoiceCreated(Invoice $invoice): void
    {
        $this->eventDispatcher->dispatch(
            event: new InvoiceCreatedEvent(
                invoice: $invoice
            ),
            eventName: InvoiceCreatedEvent::NAME,
        );
    }

    public function dispatchSalesOrganisationCreated(SalesOrganisation $salesOrganisation): void
    {
        $this->eventDispatcher->dispatch(
            event: new SalesOrganisationCreatedEvent(
                salesOrganisation: $salesOrganisation
            ),
            eventName: SalesOrganisationCreatedEvent::NAME,
        );
    }

    public function dispatchSupervisionDocumentPartnerCreated(SupervisionDocumentPartner $supervisionDocumentPartner): void
    {
        $this->eventDispatcher->dispatch(
            event: new SupervisionDocumentPartnerCreatedEvent(
                supervisionDocumentPartner: $supervisionDocumentPartner
            ),
            eventName: SupervisionDocumentPartnerCreatedEvent::NAME,
        );
    }

    public function dispatchVerificationDocumentPartnerCreated(VerificationDocumentPartner $verificationDocumentPartner): void
    {
        $this->eventDispatcher->dispatch(
            event: new VerificationDocumentPartnerCreatedEvent(
                verificationDocumentPartner: $verificationDocumentPartner
            ),
            eventName: VerificationDocumentPartnerCreatedEvent::NAME,
        );
    }
}
