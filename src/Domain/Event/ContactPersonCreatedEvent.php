<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\ContactPerson;

class ContactPersonCreatedEvent
{
    final public const string NAME = DomainEvent::CONTACT_PERSON_CREATED->value;

    public function __construct(private readonly ContactPerson $contactPerson)
    {
    }

    public function getContactPerson(): ContactPerson
    {
        return $this->contactPerson;
    }
}
