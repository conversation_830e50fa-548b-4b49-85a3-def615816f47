<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\SalesOrganisation;

class SalesOrganisationCreatedEvent
{
    final public const string NAME = DomainEvent::SALES_ORGANISATION_CREATED->value;

    public function __construct(private readonly SalesOrganisation $salesOrganisation)
    {
    }

    public function getSalesOrganisation(): SalesOrganisation
    {
        return $this->salesOrganisation;
    }
}
