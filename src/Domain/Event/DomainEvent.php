<?php

declare(strict_types=1);

namespace App\Domain\Event;

enum DomainEvent: string
{
    // Bank Account
    case BANK_ACCOUNT_CREATED = 'domain.bank.account.created';

    // Contact Person
    case CONTACT_PERSON_CREATED = 'domain.contact.person.created';

    // Identification Element
    case IDENTIFICATION_ELEMENT_CREATED = 'domain.identification.element.created';

    // Invoice
    case INVOICE_CREATED = 'domain.invoice.created';

    // Verification Document
    case VERIFICATION_DOCUMENT_PARTNER_CREATED = 'domain.invoice.partner.created';

    // Sales Organisation
    case SALES_ORGANISATION_CREATED = 'domain.sales.organisation.created';

    // Supervision Document
    case SUPERVISION_DOCUMENT_PARTNER_CREATED = 'domain.supervision.partner.created';
}
