<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\SupervisionDocumentPartner;

class SupervisionDocumentPartnerCreatedEvent
{
    final public const string NAME = DomainEvent::SUPERVISION_DOCUMENT_PARTNER_CREATED->value;

    public function __construct(private readonly SupervisionDocumentPartner $supervisionDocumentPartner)
    {
    }

    public function getSupervisionDocumentPartner(): SupervisionDocumentPartner
    {
        return $this->supervisionDocumentPartner;
    }
}
