<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\IdentificationElement;

class IdentificationElementCreatedEvent
{
    final public const string NAME = DomainEvent::IDENTIFICATION_ELEMENT_CREATED->value;

    public function __construct(private readonly IdentificationElement $identificationElement)
    {
    }

    public function getIdentificationElement(): IdentificationElement
    {
        return $this->identificationElement;
    }
}
