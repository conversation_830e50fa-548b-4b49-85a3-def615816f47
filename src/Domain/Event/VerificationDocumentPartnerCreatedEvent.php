<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\VerificationDocumentPartner;

class VerificationDocumentPartnerCreatedEvent
{
    final public const string NAME = DomainEvent::VERIFICATION_DOCUMENT_PARTNER_CREATED->value;

    public function __construct(private readonly VerificationDocumentPartner $verificationDocumentPartner)
    {
    }

    public function getVerificationDocumentPartner(): VerificationDocumentPartner
    {
        return $this->verificationDocumentPartner;
    }
}
