<?php

declare(strict_types=1);

namespace App\Domain\Event;

use App\Domain\Entity\Invoice;
use Symfony\Contracts\EventDispatcher\Event;

class InvoiceCreatedEvent extends Event
{
    final public const string NAME = DomainEvent::INVOICE_CREATED->value;

    public function __construct(private readonly Invoice $invoice)
    {
    }

    public function getInvoice(): Invoice
    {
        return $this->invoice;
    }
}
