<?php

declare(strict_types=1);

namespace App\Domain\Service;

use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasBusinessPartner as BusinessPartnerEntity;
use App\Domain\Repository\Interface\HasBusinessPartner as BusinessPartnerRepository;
use App\Exception\BadRequestException;
use App\Exception\NotFoundException;
use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class DeleteEntitiesOnBusinessPartnerIdNullService
{
    public function __construct(
        #[Autowire(param: 'app.entity.path')]
        private string $entityPath,
        private ManagerRegistry $registry,
    ) {
    }

    /**
     * @return array<string>
     */
    public function getAllEntitiesWithBusinessPartner(): array
    {
        // dynamically load and get all entities that have a business partner uuid
        /** @var list<string> $allEntities */
        $allEntities = glob(pattern: $this->entityPath.'/*.php');
        foreach ($allEntities as $file) {
            require_once $file;
        }

        $interface = HasBusinessPartner::class;
        /** @var array<string> $entities */
        $entities = array_filter(
            array: get_declared_classes(),
            callback: fn (string $class): bool => in_array(needle: $interface, haystack: class_implements(object_or_class: $class))
        );

        return $entities;
    }

    public function resolveModifiedAt(string $date, string $time): \DateTimeImmutable
    {
        $modifiedAt = \DateTimeImmutable::createFromFormat(format: 'Y-m-d H:i:s', datetime: $date.$time);
        if (!$modifiedAt) {
            throw new \InvalidArgumentException(message: "Invalid date time format '$date' '$time'. Expected 'Y-m-d H:i:s'.");
        }

        return $modifiedAt;
    }

    public function resolveOperator(string $operator): string
    {
        $operator = strtolower(string: $operator);
        if (!('on' === $operator || 'after' === $operator || 'before' === $operator)) {
            $message = sprintf("Invalid operator %s. Expected 'on', 'before' or 'after'.", $operator);
            throw new \InvalidArgumentException(message: $message);
        }

        return $operator;
    }

    public function queryBusinessPartnerIdNull(
        string $entity,
        bool $count,
        \DateTimeImmutable $modifiedAt,
        ?string $operator = null,
    ): Query {
        // check if class exists
        if (!class_exists(class: $entity)) {
            $message = sprintf('Class %s does not exist.', $entity);
            throw new NotFoundException(message: $message);
        }

        // check if entity has a business partner id
        $repository = $this->registry->getRepository(persistentObject: $entity);
        $object = new $entity();
        if (
            !$repository instanceof BusinessPartnerRepository
            || !$object instanceof BusinessPartnerEntity
        ) {
            $message = sprintf('Entity %s does not have a Business Partner Id.', $entity);
            throw new BadRequestException(message: $message);
        }

        // start query
        /** @var QueryBuilder $qb */
        $qb = $repository->findRowsOnBusinessPartnerIdNull();

        if ('on' === $operator) {
            $qb->andWhere('e.modifiedAt = :modifiedAt')
                ->setParameter(key: 'modifiedAt', value: $modifiedAt);
        } elseif ('before' === $operator) {
            $qb->andWhere('e.modifiedAt < :modifiedAt')
                ->setParameter(key: 'modifiedAt', value: $modifiedAt);
        } elseif ('after' === $operator) {
            $qb->andWhere('e.modifiedAt > :modifiedAt')
                ->setParameter(key: 'modifiedAt', value: $modifiedAt);
        }

        if ($count) {
            $qb->select('count(e.id)');
        }

        return $qb->getQuery();
    }
}
