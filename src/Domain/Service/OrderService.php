<?php

declare(strict_types=1);

namespace App\Domain\Service;

use App\Domain\Entity\BusinessPartner;
use App\Domain\Entity\Order;
use App\Domain\Entity\Service;
use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\ServiceRepository;
use App\Exception\NotFoundException;

final readonly class OrderService
{
    public function __construct(
        private BusinessPartnerRepository $businessPartnerRepository,
        private ServiceRepository $serviceRepository,
    ) {
    }

    /**
     * @throws NotFoundException
     */
    public function getService(Order $order): ?Service
    {
        if (null === $order->getExtServiceId() || null === $order->getExtServicePosId()) {
            return null;
        }

        return $this->serviceRepository->findOneByExtIdAndExtPosId(
            extId: $order->getExtServiceId(),
            extPosId: $order->getExtServicePosId(),
        );
    }

    public function getBusinessPartner(Order $order): BusinessPartner
    {
        $businessPartner = $this->businessPartnerRepository->findOneBy(criteria: [
            'id' => $order->getBusinessPartnerId(),
        ]);

        if (null == $businessPartner) {
            throw new NotFoundException(message: 'Business partner '.$order->getBusinessPartnerId().' not found!');
        }

        return $businessPartner;
    }
}
