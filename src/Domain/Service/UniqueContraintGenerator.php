<?php

declare(strict_types=1);

namespace App\Domain\Service;

class UniqueContraintGenerator
{
    /** @param array<int, string|int|\DateTimeImmutable|null> $contraints */
    public function generateUniqueContraintKey(array $contraints): string
    {
        $key = '';

        foreach ($contraints as $contraint) {
            /** @var string $content */
            $content = $contraint;

            if (null == $contraint) {
                $content = '';
            }
            if ($contraint instanceof \DateTimeImmutable) {
                $content = (string) $contraint->getTimestamp();
            }
            if (is_int(value: $contraint)) {
                $content = (string) $contraint;
            }
            if ('' === $key) {
                $key = $content;
                continue;
            }

            $key = $key.'_'.$content;
        }

        return $key;
    }
}
