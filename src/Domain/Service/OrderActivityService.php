<?php

declare(strict_types=1);

namespace App\Domain\Service;

use App\Domain\Entity\Enum\OrderActivityType;
use App\Domain\Entity\Order;
use App\Domain\Entity\OrderActivity;

final readonly class OrderActivityService
{
    public function createEntry(
        Order $order,
        OrderActivityType $activityType = OrderActivityType::CREATED,
        string $description = '',
    ): void {
        $orderActivity = new OrderActivity();
        $orderActivity->setOrder(order: $order);
        $orderActivity->setName(name: $order->getEmail());
        $orderActivity->setType(type: $activityType->value);
        $orderActivity->setDescription(description: $description);

        $order->addActivity(activity: $orderActivity);
    }
}
