<?php

declare(strict_types=1);

namespace App\Domain\Service\Translation;

use App\Domain\Entity\Enum\Locale;

interface TranslationServiceInterface
{
    /**
     *  Get a list of all translations for a given system and locale as key value pairs.
     *
     * @return array<int, TranslationInterface>
     */
    public function getTranslationList(TranslationSystem $system, Locale $locale): array;

    /**
     * Get an associative array mapping all translation keys of a system and locale to their translation.
     *
     * @return array<string>
     */
    public function getTranslationDict(TranslationSystem $system, Locale $locale): array;

    /**
     * Get the timestamp of the last modification to the translations for a given system and locale.
     */
    public function getTranslationTimestamp(TranslationSystem $system, Locale $locale): \DateTimeImmutable;
}
