<?php

declare(strict_types=1);

namespace App\Domain\Service\Tenant;

use App\Domain\Context\TenantContext;
use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Events;
use Psr\Log\LoggerInterface;

#[AsDoctrineListener(event: Events::prePersist)]
readonly class DoctrineTenantSetter
{
    public function __construct(
        private TenantContext $tenantContext,
        private LoggerInterface $logger,
    ) {
    }

    /**
     * @return array<string>
     */
    public function getSubscribedEvents(): array
    {
        return [
            Events::prePersist,
        ];
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        $entity = $args->getObject();

        if ($entity instanceof HasTenant && !$entity->hasTenant()) {
            $this->setEntityTenant(entity: $entity);
        }
    }

    private function setEntityTenant(HasTenant $entity): void
    {
        if ($this->tenantContext->hasTenant()) {
            $entity->setTenant($this->tenantContext->getTenant());

            return;
        }

        $this->logger->critical(
            'Unable to set tenant on entity',
            [
                'entityName' => $entity::class,
                'entityId' => $entity instanceof EntityInterface ? $entity->getId() : null,
            ]
        );
    }
}
