<?php

declare(strict_types=1);

namespace App\Domain\Service\Tenant;

use App\Domain\Entity\Interface\HasTenant;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\Query\Filter\SQLFilter;

class DoctrineTenantFilter extends SQLFilter
{
    public function addFilterConstraint(ClassMetadata $targetEntity, string $targetTableAlias): string
    {
        if (!$targetEntity->reflClass?->implementsInterface(HasTenant::class)) {
            return '';
        }

        return $targetTableAlias.'.tenant = '.$this->getParameter(name: 'tenant');
    }
}
