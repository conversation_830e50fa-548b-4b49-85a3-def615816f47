<?php

declare(strict_types=1);

namespace App\Domain\Service;

use Psr\Log\LoggerInterface;
use S<PERSON>fony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;

readonly class Mailer
{
    public function __construct(
        private MailerInterface $mailer,
        private LoggerInterface $logger,
        #[Autowire('%env(DATA_ENV)%')]
        private string $dataEnv,
        #[Autowire('%env(int:MAIL_ENABLED)%')]
        private int $mailEnabled,
        #[Autowire('%env(MAIL_BASE_LINK)%')]
        private string $mailBaselink,
        #[Autowire('%env(EMAIL_FROM)%')]
        private string $emailFrom,
        #[Autowire('%env(EMAIL_FROM_NAME)%')]
        private string $emailFromName,
        #[Autowire('%env(EMAIL_REPLY_TO)%')]
        private string $emailReplyTo,
    ) {
    }

    /**
     * @param array<string>        $emailTo
     * @param array<string, mixed> $context
     * @param array<string>|null   $emailBcc
     */
    public function send(
        array $emailTo,
        string $subject,
        array $context,
        ?string $emailReplyTo = null,
        ?string $emailFrom = null,
        ?string $emailFromName = null,
        ?array $emailBcc = null,
        string $htmlTemplate = 'email/message.html.twig',
    ): void {
        if ($this->mailEnabled && $emailTo) {
            $emailReplyTo ??= $this->emailReplyTo;
            $emailFrom ??= $this->emailFrom;
            $emailFromName ??= $this->emailFromName;

            if ('PROD' !== strtoupper(string: $this->dataEnv)) {
                $subject = '['.strtoupper(string: $this->dataEnv).'] '.$subject;
            }

            $email = new TemplatedEmail()
                ->subject(subject: $subject)
                ->from(new Address(address: $emailFrom, name: $emailFromName))
                ->to(...$emailTo)
                ->replyTo($emailReplyTo)
                ->htmlTemplate(template: $htmlTemplate)
                ->context(
                    context: array_merge([
                        'mailBaseLink' => $this->mailBaselink,
                    ], $context)
                );

            if (!is_null(value: $emailBcc) && ($emailTo != $emailBcc)) {
                $email->bcc(...$emailBcc);
            }

            try {
                $this->mailer->send($email);
            } catch (TransportExceptionInterface $e) {
                $this->logger->error(
                    message: 'Error while sending email.',
                    context: [
                        'message' => $e->getMessage(),
                    ],
                );
            }
        }
    }
}
