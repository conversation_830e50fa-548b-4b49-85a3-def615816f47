<?php

declare(strict_types=1);

namespace App\Domain\Service;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasBusinessPartner as BusinessPartnerEntity;
use App\Domain\Repository\Interface\HasBusinessPartner as BusinessPartnerRepository;
use App\Exception\BadRequestException;
use App\Exception\NotFoundException;
use Doctrine\ORM\Query;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

readonly class ExportEntitiesOnBusinessPartnerIdNullService
{
    public function __construct(
        #[Autowire(param: 'app.entity.path')]
        private string $entityPath,
        private ManagerRegistry $registry,
    ) {
    }

    /**
     * @return array<string>
     */
    public function getAllEntitiesWithBusinessPartner(): array
    {
        // dynamically load and get all entities that have a business partner uuid
        /** @var list<string> $allEntities */
        $allEntities = glob(pattern: $this->entityPath.'/*.php');
        foreach ($allEntities as $file) {
            require_once $file;
        }

        $interface = HasBusinessPartner::class;
        /** @var array<string> $entities */
        $entities = array_filter(
            array: get_declared_classes(),
            callback: fn (string $class): bool => in_array(needle: $interface, haystack: class_implements(object_or_class: $class))
        );

        return $entities;
    }

    public function resolveModifiedAt(?string $date, ?string $time): ?\DateTimeImmutable
    {
        if (null === $date) {
            return null;
        }

        if (null === $time) {
            throw new BadRequestException(message: 'Cannot enter a date without the time.');
        }

        $modifiedAt = \DateTimeImmutable::createFromFormat(format: 'Y-m-d H:i:s', datetime: $date.$time);
        if (!$modifiedAt) {
            $message = sprintf("Invalid date time format %s %s. Expected 'Y-m-d H:i:s'.", $date, $time);
            throw new \InvalidArgumentException(message: $message);
        }

        return $modifiedAt;
    }

    public function resolveOperator(string $operator): string
    {
        $operator = strtolower(string: $operator);
        if (!('on' === $operator || 'after' === $operator || 'before' === $operator)) {
            $message = sprintf("Invalid operator %s. Expected 'on', 'before' or 'after'.", $operator);
            throw new \InvalidArgumentException(message: $message);
        }

        return $operator;
    }

    public function queryBusinessPartnerIdNull(
        string $entity,
        bool $count,
        ?\DateTimeImmutable $modifiedAt = null,
        ?string $operator = null,
    ): Query {
        // check if class exists
        if (!class_exists(class: $entity)) {
            $message = sprintf('Class %s does not exist.', $entity);
            throw new NotFoundException(message: $message);
        }

        // check if entity has a business partner id
        $repository = $this->registry->getRepository(persistentObject: $entity);
        $object = new $entity();
        if (
            !$repository instanceof BusinessPartnerRepository
            || !$object instanceof BusinessPartnerEntity
        ) {
            $message = sprintf('Entity %s does not have a Business Partner Id.', $entity);
            throw new BadRequestException(message: $message);
        }

        // start query
        /** @var QueryBuilder $qb */
        $qb = $repository->findRowsOnBusinessPartnerIdNull();

        if (isset($modifiedAt)) {
            if ('on' === $operator) {
                $qb->andWhere('e.modifiedAt = :modifiedAt')
                    ->setParameter(key: 'modifiedAt', value: $modifiedAt);
            } elseif ('before' === $operator) {
                $qb->andWhere('e.modifiedAt < :modifiedAt')
                    ->setParameter(key: 'modifiedAt', value: $modifiedAt);
            } elseif ('after' === $operator) {
                $qb->andWhere('e.modifiedAt > :modifiedAt')
                    ->setParameter(key: 'modifiedAt', value: $modifiedAt);
            }
        }

        if ($count) {
            $qb->select('count(e.id)');
        }

        return $qb->getQuery();
    }

    /**
     * @param iterable<object> $arr
     */
    public function exportToCSV(iterable $arr, string $name, string $filename, string $mode): void
    {
        $csv = fopen(filename: $filename, mode: $mode);
        if (false === $csv) {
            $message = sprintf('Failed to open file %s for mode %s.', $filename, $mode);
            throw new \RuntimeException(message: $message);
        }

        foreach ($arr as $entity) {
            if (
                !$entity instanceof EntityInterface
                || !method_exists(object_or_class: $entity, method: 'getExtId')
            ) {
                continue;
            }

            /** @var \DateTimeImmutable $modifiedAt */
            $modifiedAt = $entity->getModifiedAt();

            fputcsv(stream: $csv, fields: [
                $name,
                print_r(value: $entity->getId(), return: true),
                print_r(value: $entity->getExtId(), return: true),
                print_r(value: $modifiedAt->format(format: 'Y-m-d H:i:s'), return: true),
            ], escape: '',
            );
        }

        fclose(stream: $csv);
    }
}
