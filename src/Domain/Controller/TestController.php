<?php

namespace App\Domain\Controller;

use GuzzleHttp\Client;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;

#[AsController]
readonly class TestController
{
    public function __construct(
        private HttpClientInterface $client,
    ) {
    }

    #[Route('/test', name: 'test')]
    public function test(): string
    {
        // does not work
        $client = HttpClient::create();
        $response = $client->request('GET', 'https://moapi.prezero.network:10443', [
            'proxy' => 'socks5h://host.docker.internal:1080',
        ]);

        // works
        $guzzle = new Client();
        $res = $guzzle->get('https://moapi.prezero.network:10443', [
            'proxy' => 'socks5h://host.docker.internal:1080',
        ]);

        return 'test';
    }
}
