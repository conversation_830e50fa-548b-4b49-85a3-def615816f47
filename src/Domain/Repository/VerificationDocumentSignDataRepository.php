<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\VerificationDocumentSignData;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<VerificationDocumentSignData>
 */
class VerificationDocumentSignDataRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, VerificationDocumentSignData> */
    private array $verificationDocumentSignDataByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, VerificationDocumentSignData::class);
    }

    public function save(VerificationDocumentSignData $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getVerificationDocumentId(),
            $entity->getExtSignDataId(),
        ]);

        $this->verificationDocumentSignDataByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $verificationDocumentId, string $extSignDataId): ?VerificationDocumentSignData
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $verificationDocumentId,
            $extSignDataId,
        ]);

        if (array_key_exists(key: $key, array: $this->verificationDocumentSignDataByUniqueContraints)) {
            return $this->verificationDocumentSignDataByUniqueContraints[$key];
        }

        $verificationDocumentSignData = $this->findOneBy(criteria: [
            'verificationDocumentId' => $verificationDocumentId,
            'extSignDataId' => $extSignDataId,
        ]);

        if (null !== $verificationDocumentSignData) {
            $this->verificationDocumentSignDataByUniqueContraints[$key] = $verificationDocumentSignData;
        }

        return $verificationDocumentSignData;
    }

    public function reset(): void
    {
        $this->verificationDocumentSignDataByUniqueContraints = [];
    }
}
