<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Contract;
use App\Domain\Entity\ContractPartner;
use App\Domain\Entity\ContractServiceLocation;
use App\Domain\Entity\Enum\PartnerRole;
use App\Domain\Entity\ServiceLocation;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<Contract>
 */
class ContractRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, Contract> */
    private array $contractsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, Contract::class);
    }

    public function save(Contract $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
        ]);

        $this->contractsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId): ?Contract
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
        ]);

        if (array_key_exists(key: $key, array: $this->contractsByUniqueContraints)) {
            return $this->contractsByUniqueContraints[$key];
        }

        $contract = $this->findOneBy(criteria: [
            'extId' => $extId,
        ]);

        if (null !== $contract) {
            $this->contractsByUniqueContraints[$key] = $contract;
        }

        return $contract;
    }

    public function reset(): void
    {
        $this->contractsByUniqueContraints = [];
    }

    public function findOneByIdAndBusinessPartner(string $id, string $businessPartnerId): ?Contract
    {
        $qb = $this->createQueryBuilder(alias: 'contract')
            ->innerJoin(join: ContractPartner::class, alias: 'contractPartners', conditionType: Join::WITH, condition: 'contract.extId = contractPartners.extContractId')
            ->andWhere('contract.id = :id')
            ->andWhere('contractPartners.businessPartnerId = :businessPartnerId')
            ->andWhere('contractPartners.role = :role')
            ->setParameter(key: 'id', value: $id)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'role', value: PartnerRole::CLIENT);

        /** @var Contract|null $result */
        $result = $qb->getQuery()->getOneOrNullResult();

        return $result;
    }

    /**
     * @return array<Contract>
     */
    public function findAllByBusinessPartner(string $businessPartnerId): array
    {
        $qb = $this->createQueryBuilder(alias: 'contract')
            ->innerJoin(join: ContractPartner::class, alias: 'contractPartners', conditionType: Join::WITH, condition: 'contract.extId = contractPartners.extContractId')
            ->andWhere('contractPartners.businessPartnerId = :businessPartnerId')
            ->andWhere('contractPartners.role = :role')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'role', value: PartnerRole::CLIENT)
            ->select('DISTINCT contract')
            ->orderBy(sort: 'contract.extId', order: 'ASC');

        /** @var array<Contract> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @return array<Contract>
     */
    public function findAllByBusinessPartnerAndUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): array
    {
        $serviceLocationId = $userSearchCriteria->filters['serviceLocationId']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'contract')
            ->innerJoin(
                join: ContractPartner::class,
                alias: 'contractPartners',
                conditionType: Join::WITH,
                condition: 'contract.extId = contractPartners.extContractId')
            ->innerJoin(
                join: ContractServiceLocation::class,
                alias: 'contractServiceLocation',
                conditionType: Join::WITH,
                condition: 'contract.extId = contractServiceLocation.extContractId')
            ->andWhere('contractPartners.businessPartnerId = :businessPartnerId')
            ->andWhere('contractPartners.role = :role')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'role', value: PartnerRole::CLIENT)
            ->select('DISTINCT contract')
            ->orderBy(sort: 'contract.extId', order: 'ASC');

        if (is_string(value: $serviceLocationId)) {
            $qb = $qb
                ->innerJoin(
                    join: ServiceLocation::class,
                    alias: 'serviceLocation',
                    conditionType: Join::WITH,
                    condition: 'contractServiceLocation.extServiceLocationId = serviceLocation.extId'
                )
                ->andWhere('serviceLocation.id = :serviceLocationId')
                ->setParameter(key: 'serviceLocationId', value: $serviceLocationId);
        }

        /** @var array<Contract> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @implements \IteratorAggregate<array-key, Contract>
     *
     * @return Paginator<Contract>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): Paginator
    {
        $serviceLocationId = $userSearchCriteria->filters['serviceLocationId']->value ?? null;
        $search = $userSearchCriteria->filters['search']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'contract')
                ->select('DISTINCT contract')
                ->innerJoin(join: ContractPartner::class, alias: 'contractPartners', conditionType: Join::WITH, condition: 'contract.extId = contractPartners.extContractId')
                ->andWhere('contractPartners.businessPartnerId = :businessPartnerId')
                ->andWhere('contractPartners.role = :role')
                ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
                ->setParameter(key: 'role', value: PartnerRole::CLIENT);

        if (is_string(value: $serviceLocationId)) {
            $qb = $qb->andWhere('contract.extServiceLocationId = :serviceLocationId')
                ->setParameter(key: 'serviceLocationId', value: $serviceLocationId);
        }

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb = $qb->andWhere('(contract.extId LIKE :search OR contract.description LIKE :search)')
                ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Contract> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }
}
