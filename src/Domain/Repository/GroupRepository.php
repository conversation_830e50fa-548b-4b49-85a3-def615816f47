<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\BusinessPartner;
use App\Domain\Entity\Group;
use App\Domain\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;

/**
 * @extends ServiceEntityRepository<Group>
 */
class GroupRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Group::class);
    }

    public function add(Group $group): void
    {
        $this->getEntityManager()->persist($group);
    }

    public function remove(Group $group): void
    {
        $this->getEntityManager()->remove($group);
    }

    /**
     * @return Group[]
     */
    public function findByUser(User $user): array
    {
        /** @var array<int, Group> $result */
        $result = $this->createQueryBuilder(alias: 'g')
            ->innerJoin(join: 'g.users', alias: 'user', conditionType: Join::WITH, condition: 'user = :user')
            ->setParameter(key: 'user', value: $user)
            ->getQuery()
            ->getResult();

        return $result;
    }

    /**
     * @param array<int, string> $groups
     *
     * @return array<int, Group>
     */
    public function findByIds(array $groups): array
    {
        /** @var array<int, Group> $result */
        $result = $this->createQueryBuilder(alias: 'g')
            ->andWhere('g.id IN (:groups)')
            ->setParameter(key: 'groups', value: $groups)
            ->getQuery()
            ->getResult();

        return $result;
    }

    /**
     * @param array<int, string> $groups
     *
     * @return array<int, Group>
     */
    public function findByIdsAndBusinessPartner(array $groups, BusinessPartner $businessPartner): array
    {
        /** @var array<int, Group> */
        return $this->createQueryBuilder(alias: 'g')
            ->andWhere('g.id IN (:groups)')
            ->andWhere('g.businessPartner = :businessPartner')
            ->setParameter(key: 'groups', value: $groups)
            ->setParameter(key: 'businessPartner', value: $businessPartner)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return array<int, Group>
     */
    public function findByBusinessPartnerId(string $businessPartnerId): array
    {
        /** @var array<int, Group> $result */
        $result = $this->createQueryBuilder(alias: 'g')
            ->andWhere('g.businessPartner = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->getQuery()
            ->getResult();

        return $result;
    }

    public function findOneByIdAndBusinessPartner(string $id, BusinessPartner $businessPartner): ?Group
    {
        /** @var ?Group */
        return $this->createQueryBuilder(alias: 'g')
                    ->andWhere('g.id = :id')
                    ->andWhere('g.businessPartner = :businessPartner')
                    ->setParameter(key: 'id', value: $id)
                    ->setParameter(key: 'businessPartner', value: $businessPartner)
                    ->getQuery()
                    ->getOneOrNullResult();
    }

    /**
     * @return array<Group>
     */
    public function findAllByBusinessPartner(BusinessPartner $businessPartner): array
    {
        /** @var array<Group> */
        return $this->createQueryBuilder(alias: 'g')
                    ->andWhere('g.businessPartner = :businessPartner')
                    ->setParameter(key: 'businessPartner', value: $businessPartner)
                    ->getQuery()
                    ->getResult();
    }

    /**
     * @implements \IteratorAggregate<array-key, Group>
     *
     * @return Paginator<Group>
     *
     * @throws QueryException
     */
    public function findAllByUserSearchCriteriaAndBusinessPartner(UserSearchCriteria $userSearchCriteria, BusinessPartner $businessPartner): Paginator
    {
        $search = $userSearchCriteria->filters['search']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'g')
            ->andWhere('g.businessPartner = :businessPartner')
            ->setParameter(key: 'businessPartner', value: $businessPartner);

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb = $qb->andWhere('g.name LIKE :search')
                ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Group> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }
}
