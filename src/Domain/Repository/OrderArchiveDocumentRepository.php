<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\OrderArchiveDocument;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<OrderArchiveDocument>
 */
class OrderArchiveDocumentRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, OrderArchiveDocument> */
    private array $orderArchiveDocumentsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, OrderArchiveDocument::class);
    }

    public function save(OrderArchiveDocument $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getOrderId(),
            $entity->getExtDocumentId(),
        ]);

        $this->orderArchiveDocumentsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $orderId, string $extDocumentId): ?OrderArchiveDocument
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $orderId,
            $extDocumentId,
        ]);

        if (array_key_exists(key: $key, array: $this->orderArchiveDocumentsByUniqueContraints)) {
            return $this->orderArchiveDocumentsByUniqueContraints[$key];
        }

        $orderArchiveDocument = $this->findOneBy(criteria: [
            'orderId' => $orderId,
            'extDocumentId' => $extDocumentId,
        ]);

        if (null !== $orderArchiveDocument) {
            $this->orderArchiveDocumentsByUniqueContraints[$key] = $orderArchiveDocument;
        }

        return $orderArchiveDocument;
    }

    public function reset(): void
    {
        $this->orderArchiveDocumentsByUniqueContraints = [];
    }
}
