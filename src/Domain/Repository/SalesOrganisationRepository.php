<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\SalesOrganisation;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<SalesOrganisation>
 */
class SalesOrganisationRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /** @var array<string, SalesOrganisation> */
    private array $salesOrganisationsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, SalesOrganisation::class);
    }

    public function save(SalesOrganisation $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtBusinessPartnerId(),
        ]);

        $this->salesOrganisationsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId, string $extBusinessPartnerId): ?SalesOrganisation
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extBusinessPartnerId,
        ]);

        if (array_key_exists(key: $key, array: $this->salesOrganisationsByUniqueContraints)) {
            return $this->salesOrganisationsByUniqueContraints[$key];
        }

        $salesOrganisation = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extBusinessPartnerId' => $extBusinessPartnerId,
        ]);

        if (null !== $salesOrganisation) {
            $this->salesOrganisationsByUniqueContraints[$key] = $salesOrganisation;
        }

        return $salesOrganisation;
    }

    public function reset(): void
    {
        $this->salesOrganisationsByUniqueContraints = [];
    }
}
