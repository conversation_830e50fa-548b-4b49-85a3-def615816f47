<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Contract;
use App\Domain\Entity\ContractPartner;
use App\Domain\Entity\ContractPosition;
use App\Domain\Entity\Enum\PartnerRole;
use App\Domain\Entity\Order;
use App\Domain\Entity\Service;
use App\Domain\Entity\ServiceLocation;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<Order>
 */
class OrderRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /**
     * @var array<string, Order>
     */
    private array $ordersByUniqueConstraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, Order::class);
    }

    public function save(Order $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtObjectId(),
        ]);

        $this->ordersByUniqueConstraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extObjectId): ?Order
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extObjectId,
        ]);

        if (array_key_exists(key: $key, array: $this->ordersByUniqueConstraints)) {
            return $this->ordersByUniqueConstraints[$key];
        }

        $order = $this->findOneBy(criteria: [
            'extObjectId' => $extObjectId,
        ]);

        if (null != $order) {
            $this->ordersByUniqueConstraints[$key] = $order;
        }

        return $order;
    }

    public function reset(): void
    {
        $this->ordersByUniqueConstraints = [];
    }

    public function findOneByIdAndBusinessPartnerId(string $id, string $businessPartnerId): ?Order
    {
        $qb = $this->createQueryBuilder(alias: 'o')
            ->andWhere('o.id = :id')
            ->andWhere('o.businessPartnerId = :businessPartnerId')
            ->setParameter(key: 'id', value: $id)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        /** @var ?Order $result */
        $result = $qb->getQuery()->getOneOrNullResult();

        return $result;
    }

    /**
     * @return array<Order>
     */
    public function findAllByExtContractIdAndBusinessPartnerNull(string $extContractId): array
    {
        /** @var array<Order> $result */
        $result = $this->createQueryBuilder(alias: 'o')
            ->innerJoin(join: Service::class, alias: 'service', conditionType: Join::WITH, condition: 'o.extServiceId = service.extId AND o.extServicePosId = service.extPosId')
            ->innerJoin(join: Contract::class, alias: 'contract', conditionType: Join::WITH, condition: 'service.extContractId = contract.extId')
            ->where('contract.extId = :extContractId')
            ->andWhere('o.businessPartnerId is null')
            ->setParameter(key: 'extContractId', value: $extContractId)
            ->getQuery()
            ->getResult();

        return $result;
    }

    /**
     * @return Paginator<Order>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteria(
        UserSearchCriteria $userSearchCriteria,
        string $businessPartnerId,
    ): Paginator {
        $serviceLocationId = $userSearchCriteria->filters['serviceLocationId']->value ?? null;
        $contractId = $userSearchCriteria->filters['contractId']->value ?? null;
        $search = $userSearchCriteria->filters['search']->value ?? null;
        $dateFrom = $userSearchCriteria->filters['dateFrom']->value ?? null;
        $dateTo = $userSearchCriteria->filters['dateTo']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'o')
            ->andWhere('o.businessPartnerId = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        if (null !== $serviceLocationId || null !== $contractId || null !== $search) {
            $qb->join(join: Service::class, alias: 'service', conditionType: Join::WITH, condition: 'o.extServiceId = service.extId AND o.extServicePosId = service.extPosId');
        }

        if (null !== $serviceLocationId) {
            $qb->join(join: ServiceLocation::class, alias: 'serviceLocation', conditionType: Join::WITH, condition: 'serviceLocation.extId = service.extServiceLocationId')
                ->andWhere('serviceLocation.id = :serviceLocationId')
                ->setParameter(key: 'serviceLocationId', value: $serviceLocationId);
        }

        if (null !== $contractId) {
            $qb->join(join: Contract::class, alias: 'contract', conditionType: Join::WITH, condition: 'contract.extId = service.extContractId')
                ->andWhere('contract.id = :contractId')
                ->setParameter(key: 'contractId', value: $contractId);
        }

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb->innerJoin(join: Contract::class, alias: 'contract', conditionType: Join::WITH, condition: 'service.extContractId = contract.extId')
                ->innerJoin(
                    join: ContractPosition::class,
                    alias: 'position',
                    conditionType: Join::WITH,
                    condition: 'contract.id = position.contractId AND service.extContainerMaterialId =  position.extMaterialId'
                )
                ->andWhere('position.materialText LIKE :search')
                ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        if (null !== $dateFrom) {
            $qb->andWhere('o.orderDate >= :dateFrom')
                ->setParameter(key: 'dateFrom', value: $dateFrom);
        }

        if (null !== $dateTo) {
            $qb->andWhere('o.orderDate <= :dateTo')
                ->setParameter(key: 'dateTo', value: $dateTo);
        }

        $qb->orderBy(sort: 'o.orderDate', order: 'DESC');

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Order> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }

    /**
     * @implements \IteratorAggregate<array-key, Order>
     *
     * @return Paginator<Order>
     *
     * @throws QueryException
     */
    public function findAllByUpcomingDates(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): Paginator
    {
        $currentDate = new \DateTimeImmutable();

        $qb = $this->createQueryBuilder(alias: 'o')
            ->andWhere('o.orderDate >= :currentDate')
            ->andWhere('o.businessPartnerId = :businessPartnerId')
            ->orderBy(sort: 'o.orderDate', order: 'ASC')
            ->setParameter(key: 'currentDate', value: $currentDate)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Order> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }

    /**
     * @implements \IteratorAggregate<array-key, Order>
     *
     * @return Paginator<Order>
     *
     * @throws QueryException
     */
    public function findAllByPastDates(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): Paginator
    {
        $currentDate = new \DateTimeImmutable();

        $qb = $this->createQueryBuilder(alias: 'o')
            ->andWhere('o.orderDate <= :currentDate')
            ->andWhere('o.businessPartnerId = :businessPartnerId')
            ->orderBy(sort: 'o.orderDate', order: 'DESC')
            ->setParameter(key: 'currentDate', value: $currentDate)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Order> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }

    /**
     * @implements \IteratorAggregate<array-key, Order>
     *
     * @return Paginator<Order>
     *
     * @throws QueryException
     * @throws \DateMalformedStringException
     */
    public function findOrderDateByUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): Paginator
    {
        $serviceLocationId = $userSearchCriteria->filters['serviceLocationId']->value ?? null;
        $contractId = $userSearchCriteria->filters['contractId']->value ?? null;
        $dateFrom = $userSearchCriteria->filters['dateFrom']->value ?? null;
        $dateTo = $userSearchCriteria->filters['dateTo']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'o')
            ->leftJoin(
                join: Service::class,
                alias: 'services',
                conditionType: Join::WITH,
                condition: 'o.extServiceId = services.extId')
            ->andWhere('o.businessPartnerId = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        if (is_string(value: $serviceLocationId)) {
            $qb->leftJoin(
                join: ServiceLocation::class,
                alias: 'serviceLocations',
                conditionType: Join::WITH,
                condition: 'services.extServiceLocationId = serviceLocations.extId')
                ->andWhere('serviceLocations.id = :serviceLocationId')
                ->setParameter(key: 'serviceLocationId', value: $serviceLocationId);
        }

        if (is_string(value: $contractId)) {
            $qb->leftJoin(
                join: Contract::class,
                alias: 'contracts',
                conditionType: Join::WITH,
                condition: 'services.extContractId = contracts.extId')
                ->andWhere('contracts.id = :contractId')
                ->setParameter(key: 'contractId', value: $contractId);
        }

        if (is_string(value: $dateFrom)) {
            $dateObject = new \DateTime(datetime: $dateFrom);
            $qb = $qb->andWhere('o.orderDate >= :startOfDay')
                ->setParameter(key: 'startOfDay', value: $dateObject->setTime(hour: 0, minute: 0));
        }

        if (is_string(value: $dateTo)) {
            $dateObject = new \DateTime(datetime: $dateTo);
            $qb = $qb->andWhere('o.orderDate <= :endOfDay')
                ->setParameter(key: 'endOfDay', value: $dateObject->setTime(hour: 23, minute: 59, second: 59));
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Order> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }

    /**
     * @return Paginator<Order>
     *
     * @throws QueryException
     */
    public function findOrderOverviewByUserSearchCriteria(
        UserSearchCriteria $userSearchCriteria,
        string $businessPartnerId,
        PartnerRole $partnerRole = PartnerRole::CLIENT,
    ): Paginator {
        $search = $userSearchCriteria->filters['search']->value ?? null;
        $status = $userSearchCriteria->filters['status']->value ?? null;
        $startDate = $userSearchCriteria->filters['startDate']->value ?? null;
        $endDate = $userSearchCriteria->filters['endDate']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'o')
            ->andWhere('o.businessPartnerId = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        if (null !== $search || null !== $startDate || null !== $endDate) {
            $qb->leftJoin(
                join: Service::class,
                alias: 'service',
                conditionType: Join::WITH,
                condition: 'o.extServiceId = service.extId AND o.extServicePosId = service.extPosId',
            );
        }

        if (null !== $status) {
            $qb->andWhere('o.orderStatus = :status')
                ->setParameter(key: 'status', value: $status);
        }

        if (null !== $startDate) {
            $qb->andWhere('service.startDate >= :startDate')
                ->setParameter(key: 'startDate', value: $startDate);
        }

        if (null !== $endDate) {
            $qb->andWhere('service.endDate <= :endDate')
                ->setParameter(key: 'endDate', value: $endDate);
        }

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb->leftJoin(
                join: ContractPartner::class,
                alias: 'partner',
                conditionType: Join::WITH,
                condition: 'partner.extContractId = service.extContractId AND partner.businessPartnerId = o.businessPartnerId',
            )
                ->andWhere('partner.role = :contractPartnerRole')
                ->setParameter(key: 'contractPartnerRole', value: $partnerRole);

            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->like(x: 'service.extContractId', y: ':search'),
                $qb->expr()->like(x: 'o.email', y: ':search'),
                $qb->expr()->like(x: 'partner.extId', y: ':search')
            ))->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Order> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }
}
