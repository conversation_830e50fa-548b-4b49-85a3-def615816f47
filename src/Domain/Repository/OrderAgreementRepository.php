<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\OrderAgreement;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OrderAgreement>
 */
class OrderAgreementRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, OrderAgreement::class);
    }

    /**
     * @return array<OrderAgreement>
     */
    public function findAllByServiceTypeAndNull(string $serviceType): array
    {
        /** @var array<OrderAgreement> */
        return $this->createQueryBuilder(alias: 'orderAgreement')
            ->andWhere('orderAgreement.serviceType = :serviceType or orderAgreement.serviceType is null')
            ->setParameter(key: 'serviceType', value: $serviceType)
            ->getQuery()
            ->getResult();
    }
}
