<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\WasteStatistic;
use App\Domain\Entity\WasteStatisticLocation;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<WasteStatisticLocation>
 */
class WasteStatisticLocationRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, WasteStatisticLocation> */
    private array $wasteStatisticLocationsByUniqueConstraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, WasteStatisticLocation::class);
    }

    public function add(WasteStatisticLocation $wasteStatisticLocation): void
    {
        $this->getEntityManager()->persist($wasteStatisticLocation);
    }

    public function save(WasteStatisticLocation $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
        ]);

        $this->wasteStatisticLocationsByUniqueConstraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extId): ?WasteStatisticLocation
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
        ]);

        if (array_key_exists(key: $key, array: $this->wasteStatisticLocationsByUniqueConstraints)) {
            return $this->wasteStatisticLocationsByUniqueConstraints[$key];
        }

        $wasteStatisticLocation = $this->findOneBy(criteria: [
            'extId' => $extId,
        ]);

        if (null !== $wasteStatisticLocation) {
            $this->wasteStatisticLocationsByUniqueConstraints[$key] = $wasteStatisticLocation;
        }

        return $wasteStatisticLocation;
    }

    public function reset(): void
    {
        $this->wasteStatisticLocationsByUniqueConstraints = [];
    }

    public function findOneByExtWasteStatisticLocationIdAndBusinessPartner(string $extWasteStatisticLocationId, string $businessPartnerId): ?WasteStatisticLocation
    {
        /** @var ?WasteStatisticLocation */
        return $this->createQueryBuilder(alias: 'wasteStatisticLocation')
            ->innerJoin(
                join: WasteStatistic::class,
                alias: 'wasteStatistic',
                conditionType: Join::WITH,
                condition: 'wasteStatisticLocation.extId = wasteStatistic.extWasteStatisticLocationId', )
            ->andWhere('wasteStatisticLocation.extId = :extWasteStatisticLocationId')
            ->andWhere('wasteStatistic.extBusinessPartnerId = :businessPartnerId')
            ->setParameter(key: 'extWasteStatisticLocationId', value: $extWasteStatisticLocationId)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @implements \IteratorAggregate<array-key, WasteStatisticLocation>
     *
     * @return Paginator<WasteStatisticLocation>
     *
     * @throws QueryException
     * @throws \DateMalformedStringException
     */
    public function findAllByUserSearchCriteriaAndBusinessPartner(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): Paginator
    {
        $dateFrom = $userSearchCriteria->filters['dateFrom']->value ?? null;
        $dateTo = $userSearchCriteria->filters['dateTo']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'wasteStatisticLocation')
            ->join(
                join: WasteStatistic::class,
                alias: 'wasteStatistic',
                conditionType: Join::WITH,
                condition: 'wasteStatisticLocation.extId = wasteStatistic.extWasteStatisticLocationId')
            ->andWhere('wasteStatistic.extBusinessPartnerId = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        if (is_string(value: $dateFrom)) {
            $dateObject = new \DateTimeImmutable(datetime: $dateFrom);
            $qb = $qb->andWhere('wasteStatistic.serviceDate >= :startOfDay')
                ->setParameter(key: 'startOfDay', value: $dateObject->setTime(hour: 0, minute: 0)->format(format: 'Y-m-d H:i:s'));
        }

        if (is_string(value: $dateTo)) {
            $dateObject = new \DateTimeImmutable(datetime: $dateTo);
            $qb = $qb->andWhere('wasteStatistic.serviceDate <= :endOfDay')
                ->setParameter(key: 'endOfDay', value: $dateObject->setTime(hour: 23, minute: 59, second: 59)->format(format: 'Y-m-d H:i:s'));
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<WasteStatisticLocation> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }
}
