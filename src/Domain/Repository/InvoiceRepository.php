<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Invoice;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<Invoice>
 */
class InvoiceRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /** @var array<string, Invoice> */
    private array $invoicesByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, Invoice::class);
    }

    public function save(Invoice $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
        ]);

        $this->invoicesByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId): ?Invoice
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
        ]);

        if (array_key_exists(key: $key, array: $this->invoicesByUniqueContraints)) {
            return $this->invoicesByUniqueContraints[$key];
        }

        $invoice = $this->findOneBy(criteria: [
            'extId' => $extId,
        ]);

        if (null !== $invoice) {
            $this->invoicesByUniqueContraints[$key] = $invoice;
        }

        return $invoice;
    }

    public function reset(): void
    {
        $this->invoicesByUniqueContraints = [];
    }

    /**
     * @implements \IteratorAggregate<array-key, Invoice>
     *
     * @return Paginator<Invoice>
     *
     * @throws QueryException
     * @throws \DateMalformedStringException
     */
    public function findByUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): Paginator
    {
        $dateFrom = $userSearchCriteria->filters['dateFrom']->value ?? null;
        $dateTo = $userSearchCriteria->filters['dateTo']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'invoice')
                ->andWhere('invoice.businessPartnerId = :businessPartnerId')
                ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        if (is_string(value: $dateFrom)) {
            $dateObject = new \DateTimeImmutable(datetime: $dateFrom);
            $qb = $qb->andWhere('invoice.billingDate >= :startOfDay')
                ->setParameter(key: 'startOfDay', value: $dateObject->setTime(hour: 0, minute: 0));
        }

        if (is_string(value: $dateTo)) {
            $dateObject = new \DateTimeImmutable(datetime: $dateTo);
            $qb = $qb->andWhere('invoice.billingDate <= :endOfDay')
                ->setParameter(key: 'endOfDay', value: $dateObject->setTime(hour: 23, minute: 59, second: 59));
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Invoice> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }
}
