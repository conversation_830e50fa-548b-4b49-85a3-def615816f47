<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\MaterialServiceType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MaterialServiceType>
 */
class MaterialServiceTypeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MaterialServiceType::class);
    }

    /**
     * @return array<MaterialServiceType>
     */
    public function findAllByExtMaterialId(string $extMaterialId): array
    {
        /** @var array<MaterialServiceType> $result */
        $result = $this->createQueryBuilder(alias: 'materialService')
            ->where('materialService.extMaterialId = :extMaterialId')
            ->setParameter(key: 'extMaterialId', value: $extMaterialId)
            ->getQuery()
            ->getResult();

        return $result;
    }
}
