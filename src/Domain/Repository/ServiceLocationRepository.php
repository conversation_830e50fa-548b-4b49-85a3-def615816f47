<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Contract;
use App\Domain\Entity\ContractPartner;
use App\Domain\Entity\ContractServiceLocation;
use App\Domain\Entity\ServiceLocation;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<ServiceLocation>
 */
class ServiceLocationRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, ServiceLocation> */
    private array $serviceLocationsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, ServiceLocation::class);
    }

    public function save(ServiceLocation $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
        ]);

        $this->serviceLocationsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId): ?ServiceLocation
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
        ]);

        if (array_key_exists(key: $key, array: $this->serviceLocationsByUniqueContraints)) {
            return $this->serviceLocationsByUniqueContraints[$key];
        }

        $serviceLocation = $this->findOneBy(criteria: [
            'extId' => $extId,
        ]);

        if (null !== $serviceLocation) {
            $this->serviceLocationsByUniqueContraints[$key] = $serviceLocation;
        }

        return $serviceLocation;
    }

    public function reset(): void
    {
        $this->serviceLocationsByUniqueContraints = [];
    }

    public function findOneByExtIdAndExtBusinessPartnerId(string $extId, string $extBusinessPartnerId): ?ServiceLocation
    {
        $qb = $this->createQueryBuilder(alias: 'serviceLocation')
            ->innerJoin(
                join: ContractServiceLocation::class,
                alias: 'contractServiceLocation',
                conditionType: Join::WITH,
                condition: 'serviceLocation.extId = contractServiceLocation.extServiceLocationId')
            ->andWhere('serviceLocation.extId = :extId')
            ->andWhere('contractServiceLocation.extBusinessPartnerId = :extBusinessPartnerId')
            ->setParameter(key: 'extBusinessPartnerId', value: $extBusinessPartnerId)
            ->setParameter(key: 'extId', value: $extId);

        /** @var ServiceLocation $result */
        $result = $qb->getQuery()->getOneOrNullResult();

        return $result;
    }

    /**
     * @return array<ServiceLocation>
     */
    public function findAllByBusinessPartner(string $businessPartnerExtId): array
    {
        $qb = $this->createQueryBuilder(alias: 'serviceLocation')
            ->innerJoin(
                join: ContractServiceLocation::class,
                alias: 'contractServiceLocation',
                conditionType: Join::WITH,
                condition: 'serviceLocation.extId = contractServiceLocation.extServiceLocationId')
            ->andWhere('contractServiceLocation.extBusinessPartnerId = :businessPartnerExtId')
            ->setParameter(key: 'businessPartnerExtId', value: $businessPartnerExtId)
            ->select('DISTINCT serviceLocation')
            ->orderBy(sort: 'serviceLocation.name', order: 'ASC');

        /** @var array<ServiceLocation> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /** @return array<ServiceLocation> */
    public function findAllByBusinessPartnerAndContract(string $businessPartnerId): array
    {
        $qb = $this->getContractJoin(businessPartnerId: $businessPartnerId);

        /** @var array<ServiceLocation> */
        return $qb->getQuery()->getResult();
    }

    /**
     * @return array<ServiceLocation>
     */
    public function findAllByBusinessPartnerAndUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $businessPartnerExtId): array
    {
        $contractId = $userSearchCriteria->filters['contractId']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'serviceLocation')
            ->innerJoin(
                join: ContractServiceLocation::class,
                alias: 'contractServiceLocation',
                conditionType: Join::WITH,
                condition: 'serviceLocation.extId = contractServiceLocation.extServiceLocationId')
            ->andWhere('contractServiceLocation.extBusinessPartnerId = :businessPartnerExtId')
            ->setParameter(key: 'businessPartnerExtId', value: $businessPartnerExtId)
            ->select('DISTINCT serviceLocation')
            ->orderBy(sort: 'serviceLocation.name', order: 'ASC');

        if (is_string(value: $contractId)) {
            $qb->innerJoin(
                join: Contract::class,
                alias: 'contract',
                conditionType: Join::WITH,
                condition: 'contractServiceLocation.extContractId = contract.extId'
            );
            $qb->andWhere('contract.id = :contractId');
            $qb->setParameter(key: 'contractId', value: $contractId);
        }

        /** @var array<ServiceLocation> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @implements \IteratorAggregate<array-key, ServiceLocation>
     *
     * @return Paginator<ServiceLocation>
     *
     * @throws QueryException
     */
    public function findAllByUserSearchCriteriaAndContractAndBusinessPartner(
        UserSearchCriteria $userSearchCriteria,
        string $businessPartnerId,
    ): Paginator {
        $search = $userSearchCriteria->filters['search']->value ?? null;

        $qb = $this->getContractJoin(businessPartnerId: $businessPartnerId);

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb = $qb->andWhere('serviceLocation.extId LIKE :search')
                ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<ServiceLocation> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }

    private function getContractJoin(string $businessPartnerId): QueryBuilder
    {
        return $this->createQueryBuilder(alias: 'serviceLocation')
            ->innerJoin(
                join: ContractServiceLocation::class,
                alias: 'contractServiceLocation',
                conditionType: Join::WITH,
                condition: 'contractServiceLocation.extServiceLocationId = serviceLocation.extId'
            )
            ->innerJoin(
                join: ContractPartner::class,
                alias: 'contractPartners',
                conditionType: Join::WITH,
                condition: 'contractServiceLocation.extContractId = contractPartners.extContractId')
            ->andWhere('contractPartners.businessPartnerId = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);
    }
}
