<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\SupervisionDocumentPartner;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<SupervisionDocumentPartner>
 */
class SupervisionDocumentPartnerRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /** @var array<string, SupervisionDocumentPartner> */
    private array $supervisionDocumentPartnersByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, SupervisionDocumentPartner::class);
    }

    public function save(SupervisionDocumentPartner $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtCheckId(),
            $entity->getType(),
            $entity->getExtSupervisionDocumentId(),
        ]);

        $this->supervisionDocumentPartnersByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extId, string $extCheckId, string $type, string $extSupervisionDocumentId): ?SupervisionDocumentPartner
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extCheckId,
            $type,
            $extSupervisionDocumentId,
        ]);

        if (array_key_exists(key: $key, array: $this->supervisionDocumentPartnersByUniqueContraints)) {
            return $this->supervisionDocumentPartnersByUniqueContraints[$key];
        }

        $SupervisionDocumentPartner = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extCheckId' => $extCheckId,
            'type' => $type,
            'extSupervisionDocumentId' => $extSupervisionDocumentId,
        ]);

        if (null !== $SupervisionDocumentPartner) {
            $this->supervisionDocumentPartnersByUniqueContraints[$key] = $SupervisionDocumentPartner;
        }

        return $SupervisionDocumentPartner;
    }

    public function reset(): void
    {
        $this->supervisionDocumentPartnersByUniqueContraints = [];
    }

    /**
     * @return array<SupervisionDocumentPartner>
     */
    public function findAllByExtSupervisionDocumentId(string $id): array
    {
        $qb = $this->createQueryBuilder(alias: 'supervision_document_partner')
        ->andWhere('supervision_document_partner.extSupervisionDocumentId = :id')
        ->setParameter(key: 'id', value: $id);

        /** @var array<SupervisionDocumentPartner> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }
}
