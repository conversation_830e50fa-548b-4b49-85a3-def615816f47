<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\WasteStatistic;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<WasteStatistic>
 */
class WasteStatisticRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, WasteStatistic> */
    private array $wasteStatisticsByUniqueConstraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, WasteStatistic::class);
    }

    public function add(WasteStatistic $wasteStatistic): void
    {
        $this->getEntityManager()->persist($wasteStatistic);
    }

    public function save(WasteStatistic $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtBusinessPartnerId(),
            $entity->getExtWasteStatisticLocationId() ?? null,
            $entity->getExtContractId() ?? null,
        ]);

        $this->wasteStatisticsByUniqueConstraints[$key] = $entity;
    }

    public function findByUniqueConstraints(
        string $extBusinessPartnerId,
        ?string $extWasteStatisticLocationId,
        ?string $extContractId,
    ): ?WasteStatistic {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extBusinessPartnerId,
            $extWasteStatisticLocationId ?? null,
            $extContractId ?? null,
        ]);

        if (array_key_exists(key: $key, array: $this->wasteStatisticsByUniqueConstraints)) {
            return $this->wasteStatisticsByUniqueConstraints[$key];
        }

        $wasteStatistic = $this->findOneBy(criteria: [
            'extBusinessPartnerId' => $extBusinessPartnerId,
            'extWasteStatisticLocationId' => $extWasteStatisticLocationId ?? null,
            'extContractId' => $extContractId ?? null,
        ]);

        if (null !== $wasteStatistic) {
            $this->wasteStatisticsByUniqueConstraints[$key] = $wasteStatistic;
        }

        return $wasteStatistic;
    }

    public function reset(): void
    {
        $this->wasteStatisticsByUniqueConstraints = [];
    }

    /**
     * @return array<WasteStatistic>
     */
    public function findAllByExtWasteStatisticLocationIdAndBusinessPartner(
        string $extWasteStatisticLocationId,
        string $businessPartnerId,
    ): array {
        /** @var array<WasteStatistic> */
        return $this->createQueryBuilder(alias: 'wasteStatistic')
            ->andWhere('wasteStatistic.extWasteStatisticLocationId = :extWasteStatisticLocationId')
            ->andWhere('wasteStatistic.extBusinessPartnerId = :businessPartnerId')
            ->setParameter(key: 'extWasteStatisticLocationId', value: $extWasteStatisticLocationId)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->getQuery()
            ->getResult();
    }

    public function getTotalAmountPerYearAndBusinessPartner(int $year, string $businessPartnerId): float
    {
        $startOfYear = new \DateTimeImmutable()->setDate(year: $year, month: 01, day: 01)->setTime(hour: 0, minute: 0);
        $endOfYear = new \DateTimeImmutable()->setDate(year: $year, month: 12, day: 31)->setTime(hour: 23, minute: 59, second: 59);

        return (float) $this->createQueryBuilder(alias: 'wasteStatistic')
                ->select('SUM(wasteStatistic.amount) AS totalAmount')
                ->andWhere('wasteStatistic.extBusinessPartnerId = :businessPartnerId')
                ->andWhere('wasteStatistic.orderDate >= :startOfYear')
                ->andWhere('wasteStatistic.orderDate <= :endOfYear')
                ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
                ->setParameter(key: 'startOfYear', value: $startOfYear)
                ->setParameter(key: 'endOfYear', value: $endOfYear)
                ->getQuery()
                ->getSingleScalarResult();
    }

    public function getTotalAmountByUserSearchCriteriaBusinessPartnerAndExtWasteStatisticLocationId(
        UserSearchCriteria $userSearchCriteria,
        string $businessPartnerId,
        string $extWasteStatisticLocationId,
    ): float {
        $qb = $this->getDateFilter(userSearchCriteria: $userSearchCriteria)
            ->select('SUM(wasteStatistic.amount) AS totalAmount')
            ->andWhere('wasteStatistic.extBusinessPartnerId = :businessPartnerId')
            ->andWhere('wasteStatistic.extWasteStatisticLocationId = :extWasteStatisticLocationId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'extWasteStatisticLocationId', value: $extWasteStatisticLocationId);

        return (float) $qb->getQuery()->getSingleScalarResult();
    }

    /**
     * @return array<WasteStatistic>
     */
    public function findAllByUserSearchCriteriaAndBusinessPartner(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): array
    {
        /** @var array<WasteStatistic> */
        return $this->getDateFilter(userSearchCriteria: $userSearchCriteria)
            ->andWhere('wasteStatistic.extBusinessPartnerId = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->getQuery()
            ->getResult();
    }

    private function getDateFilter(UserSearchCriteria $userSearchCriteria): QueryBuilder
    {
        $dateFrom = $userSearchCriteria->filters['dateFrom']->value ?? null;
        $dateTo = $userSearchCriteria->filters['dateTo']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'wasteStatistic');

        if (is_string(value: $dateFrom)) {
            $dateObject = new \DateTimeImmutable(datetime: $dateFrom);
            $qb = $qb->andWhere('wasteStatistic.serviceDate >= :startOfDay')
                ->setParameter(key: 'startOfDay', value: $dateObject->setTime(hour: 0, minute: 0)->format(format: 'Y-m-d H:i:s'));
        }

        if (is_string(value: $dateTo)) {
            $dateObject = new \DateTimeImmutable(datetime: $dateTo);
            $qb = $qb->andWhere('wasteStatistic.serviceDate <= :endOfDay')
                ->setParameter(key: 'endOfDay', value: $dateObject->setTime(hour: 23, minute: 59, second: 59)->format(format: 'Y-m-d H:i:s'));
        }

        return $qb;
    }
}
