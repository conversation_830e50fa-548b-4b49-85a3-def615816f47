<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\OrderActivity;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OrderActivity>
 */
final class OrderActivityRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, OrderActivity::class);
    }
}
