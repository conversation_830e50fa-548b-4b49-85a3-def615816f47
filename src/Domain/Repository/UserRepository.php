<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;

/**
 * @extends ServiceEntityRepository<User>
 */
class UserRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, User::class);
    }

    public function add(User $entity): void
    {
        $this->getEntityManager()->persist($entity);
    }

    public function remove(User $entity): void
    {
        $this->getEntityManager()->remove($entity);
    }

    public function getByUsername(string $username): ?User
    {
        /** @var User|null $result */
        $result = $this->createQueryBuilder(alias: 'user')
            ->andWhere('user.username = :username')
            ->setParameter(key: 'username', value: $username)
            ->getQuery()
            ->getOneOrNullResult();

        return $result;
    }

    /**
     * @return Paginator<User>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteriaAndBusinessPartnerIdForPortal(
        UserSearchCriteria $userSearchCriteria,
        string $businessPartnerId,
        bool $searchInUser = false,
        bool $searchInPermission = false,
        bool $searchInGroup = false,
    ): Paginator {
        $qb = $this->createQueryBuilder(alias: 'user')
            ->innerJoin(
                join: 'user.businessPartners',
                alias: 'businessPartner',
                conditionType: Join::WITH,
                condition: 'businessPartner.id = :businessPartnerId'
            )
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        $this->addQueryByUserSearchCriteria(
            queryBuilder: $qb,
            userSearchCriteria: $userSearchCriteria,
            searchInUser: $searchInUser,
            searchInPermission: $searchInPermission,
            searchInGroup: $searchInGroup,
        );

        return $this->addPagination(
            queryBuilder: $qb,
            userSearchCriteria: $userSearchCriteria,
        );
    }

    /**
     * @return Paginator<User>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteriaForManagement(UserSearchCriteria $userSearchCriteria): Paginator
    {
        $queryBuilder = $this->createQueryBuilder(alias: 'user');

        $this->addQueryByUserSearchCriteria(
            $queryBuilder,
            userSearchCriteria: $userSearchCriteria,
            searchInUser: true,
            searchInPermission: true,
        );

        return $this->addPagination(
            queryBuilder: $queryBuilder,
            userSearchCriteria: $userSearchCriteria,
        );
    }

    public function findByIdAndBusinessPartnerId(string $id, string $businessPartnerId): ?User
    {
        /** @var ?User $result */
        $result = $this->createQueryBuilder(alias: 'user')
            ->innerJoin(
                join: 'user.businessPartners',
                alias: 'businessPartner',
                conditionType: Join::WITH,
                condition: 'businessPartner.id = :businessPartnerId',
            )
            ->andWhere('user.id = :id')
            ->setParameter(key: 'id', value: $id)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->getQuery()
            ->getOneOrNullResult();

        return $result;
    }

    private function addQueryByUserSearchCriteria(
        QueryBuilder $queryBuilder,
        UserSearchCriteria $userSearchCriteria,
        bool $searchInUser = false,
        bool $searchInPermission = false,
        bool $searchInGroup = false,
    ): void {
        $search = $userSearchCriteria->filters['search']->value ?? null;

        if (!is_string(value: $search) || '' === trim(string: $search)
            || (!$searchInUser && !$searchInPermission && !$searchInGroup)
        ) {
            return;
        }

        $queryBuilder->setParameter(key: 'search', value: '%'.$search.'%');
        $orX = $queryBuilder->expr()->orX();

        if ($searchInUser) {
            $orX->addMultiple(args: [
                $queryBuilder->expr()->like(x: 'user.username', y: ':search'),
                $queryBuilder->expr()->like(x: 'user.firstname', y: ':search'),
                $queryBuilder->expr()->like(x: 'user.lastname', y: ':search'),
            ]);
        }

        if ($searchInPermission) {
            $orX->add(arg: $queryBuilder->expr()->like(x: 'user.permissions', y: ':search'));
        }

        if ($searchInGroup) {
            $queryBuilder->leftJoin(join: 'user.groups', alias: 'g');
            $orX->add(arg: $queryBuilder->expr()->like(x: 'g.name', y: ':search'));
        }

        $queryBuilder->andWhere($orX);
    }

    /**
     * @return Paginator<User>
     *
     * @throws QueryException
     */
    private function addPagination(
        QueryBuilder $queryBuilder,
        UserSearchCriteria $userSearchCriteria,
    ): Paginator {
        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $queryBuilder->addCriteria(criteria: $criteria);

        /** @var Paginator<User> $paginator */
        $paginator = new Paginator(query: $queryBuilder, fetchJoinCollection: false);

        return $paginator;
    }
}
