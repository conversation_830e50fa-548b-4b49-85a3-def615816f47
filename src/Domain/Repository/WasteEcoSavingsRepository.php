<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\WasteEcoSavings;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;

/**
 * @extends ServiceEntityRepository<WasteEcoSavings>
 */
class WasteEcoSavingsRepository extends ServiceEntityRepository implements HasBusinessPartner
{
    use BusinessPartnerTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WasteEcoSavings::class);
    }

    public function add(WasteEcoSavings $entity): void
    {
        $this->getEntityManager()->persist($entity);
    }

    public function getTotalSavingsPerYearByBusinessPartnerId(string $year, string $businessPartnerId): float
    {
        $qb = $this->createQueryBuilder(alias: 'wasteEcoSavings')
            ->select('wasteEcoSavings.co2Savings AS savings')
            ->andWhere('wasteEcoSavings.businessPartnerId = :businessPartnerId')
            ->andWhere('wasteEcoSavings.year = :year')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'year', value: $year);

        /** @var array<int, string> $result */
        $result = $qb->getQuery()->getResult(hydrationMode: AbstractQuery::HYDRATE_SCALAR_COLUMN);

        return array_sum(array: $result);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function getTotalSavingsByBusinessPartnerId(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): float
    {
        $qb = $this->getDateFilter(userSearchCriteria: $userSearchCriteria)
            ->select('wasteEcoSavings.co2Savings AS savings')
            ->andWhere('wasteEcoSavings.businessPartnerId = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        /** @var array<int, string> $result */
        $result = $qb->getQuery()->getResult(hydrationMode: AbstractQuery::HYDRATE_SCALAR_COLUMN);

        return array_sum(array: $result);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function getTotalEmissionsByBusinessPartnerId(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): float
    {
        $qb = $this->getDateFilter(userSearchCriteria: $userSearchCriteria)
            ->select('wasteEcoSavings.co2Emissions AS emissions')
            ->andWhere('wasteEcoSavings.businessPartnerId = :businessPartnerId')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        /** @var array<int, string> $result */
        $result = $qb->getQuery()->getResult(hydrationMode: AbstractQuery::HYDRATE_SCALAR_COLUMN);

        return array_sum(array: $result);
    }

    /**
     * @return array<WasteEcoSavings>
     *
     * @throws \DateMalformedStringException
     */
    public function findAllByProductHierarchyAndBusinessPartnerId(UserSearchCriteria $userSearchCriteria, string $productHierarchy, string $businessPartnerId): array
    {
        $qb = $this->getDateFilter(userSearchCriteria: $userSearchCriteria)
            ->andWhere('wasteEcoSavings.businessPartnerId = :businessPartnerId')
            ->andWhere('wasteEcoSavings.productHierarchy = :productHierarchy')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'productHierarchy', value: $productHierarchy);

        /** @var array<WasteEcoSavings> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function getDateFilter(UserSearchCriteria $userSearchCriteria): QueryBuilder
    {
        $dateFrom = $userSearchCriteria->filters['dateFrom']->value ?? null;
        $dateTo = $userSearchCriteria->filters['dateTo']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'wasteEcoSavings');

        if (is_string(value: $dateFrom)) {
            $dateObject = new \DateTimeImmutable(datetime: $dateFrom);
            $yearMonth = $dateObject->format(format: 'Ym');

            $qb->andWhere('(CAST_AS_INTEGER(wasteEcoSavings.year) * 100 + CAST_AS_INTEGER(wasteEcoSavings.month)) >= :yearMonthFrom')
                ->setParameter(key: 'yearMonthFrom', value: $yearMonth);
        }

        if (is_string(value: $dateTo)) {
            $dateObject = new \DateTimeImmutable(datetime: $dateTo);
            $yearMonth = $dateObject->format(format: 'Ym');

            $qb->andWhere('(CAST_AS_INTEGER(wasteEcoSavings.year) * 100 + CAST_AS_INTEGER(wasteEcoSavings.month)) <= :yearMonthTo')
                ->setParameter(key: 'yearMonthTo', value: $yearMonth);
        }

        return $qb;
    }
}
