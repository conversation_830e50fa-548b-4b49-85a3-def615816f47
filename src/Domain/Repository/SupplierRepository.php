<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Contract;
use App\Domain\Entity\Supplier;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;

/**
 * @extends ServiceEntityRepository<Supplier>
 */
class SupplierRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
    ) {
        parent::__construct(
            $registry, Supplier::class);
    }

    public function add(Supplier $entity): void
    {
        $this->getEntityManager()->persist($entity);
    }

    /**
     * @implements \IteratorAggregate<array-key, Supplier>
     *
     * @return Paginator<Supplier>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteria(UserSearchCriteria $userSearchCriteria): Paginator
    {
        $region = $userSearchCriteria->filters['region']->value ?? null;
        $search = $userSearchCriteria->filters['search']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'supplier');

        if (is_string(value: $region)) {
            $qb = $qb->andWhere('supplier.region = :region')
                ->setParameter(key: 'region', value: $region);
        }

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb = $qb->andWhere('(supplier.location LIKE :search OR supplier.description LIKE :search OR supplier.number LIKE :search)')
            ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<Supplier> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }

    public function findByContractId(string $contractId): ?Supplier
    {
        /** @var ?Supplier $result */
        $result = $this->createQueryBuilder(alias: 'supplier')
            ->innerJoin(
                join: Contract::class,
                alias: 'contract',
                conditionType: Join::WITH,
                condition: 'supplier.vkorg = contract.extSalesOrganisationId',
            )
            ->andWhere('contract.id = :contractId')
            ->setParameter(key: 'contractId', value: $contractId)
            ->getQuery()
            ->getOneOrNullResult();

        return $result;
    }
}
