<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\BankAccount;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<BankAccount>
 */
class BankAccountRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /** @var array<string, BankAccount> */
    private array $bankAccountsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, BankAccount::class);
    }

    public function save(BankAccount $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtBusinessPartnerId(),
            $entity->getIban(),
        ]);

        $this->bankAccountsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extBusinessPartnerId, string $iban): ?BankAccount
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extBusinessPartnerId,
            $iban,
        ]);

        if (array_key_exists(key: $key, array: $this->bankAccountsByUniqueContraints)) {
            return $this->bankAccountsByUniqueContraints[$key];
        }

        $bankAccount = $this->findOneBy(criteria: [
            'extBusinessPartnerId' => $extBusinessPartnerId,
            'iban' => $iban,
        ]);

        if (null !== $bankAccount) {
            $this->bankAccountsByUniqueContraints[$key] = $bankAccount;
        }

        return $bankAccount;
    }

    public function reset(): void
    {
        $this->bankAccountsByUniqueContraints = [];
    }
}
