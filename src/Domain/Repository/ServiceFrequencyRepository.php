<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\ServiceFrequency;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<ServiceFrequency>
 */
class ServiceFrequencyRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, ServiceFrequency> */
    private array $serviceFrequenciesByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, ServiceFrequency::class);
    }

    public function save(ServiceFrequency $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtPosId(),
        ]);

        $this->serviceFrequenciesByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extId, string $extPosId): ?ServiceFrequency
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extPosId,
        ]);

        if (array_key_exists(key: $key, array: $this->serviceFrequenciesByUniqueContraints)) {
            return $this->serviceFrequenciesByUniqueContraints[$key];
        }

        $serviceFrequency = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extPosId' => $extPosId,
        ]);

        if (null !== $serviceFrequency) {
            $this->serviceFrequenciesByUniqueContraints[$key] = $serviceFrequency;
        }

        return $serviceFrequency;
    }

    public function reset(): void
    {
        $this->serviceFrequenciesByUniqueContraints = [];
    }
}
