<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\ContactPerson;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<ContactPerson>
 */
class ContactPersonRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /** @var array<string, ContactPerson> */
    private array $contactPersonsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, ContactPerson::class);
    }

    public function save(ContactPerson $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtBusinessPartnerId(),
            $entity->getExtSalesOrganisationId(),
            $entity->getRole(),
        ]);

        $this->contactPersonsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId, string $extBusinessPartnerId, string $extSalesOrganisationId, string $role): ?ContactPerson
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extBusinessPartnerId,
            $extSalesOrganisationId,
            $role,
        ]);

        if (array_key_exists(key: $key, array: $this->contactPersonsByUniqueContraints)) {
            return $this->contactPersonsByUniqueContraints[$key];
        }

        $contactPerson = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extBusinessPartnerId' => $extBusinessPartnerId,
            'extSalesOrganisationId' => $extSalesOrganisationId,
            'role' => $role,
        ]);

        if (null !== $contactPerson) {
            $this->contactPersonsByUniqueContraints[$key] = $contactPerson;
        }

        return $contactPerson;
    }

    public function reset(): void
    {
        $this->contactPersonsByUniqueContraints = [];
    }
}
