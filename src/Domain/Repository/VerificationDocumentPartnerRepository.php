<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\VerificationDocumentPartner;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<VerificationDocumentPartner>
 */
class VerificationDocumentPartnerRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /** @var array<string, VerificationDocumentPartner> */
    private array $verificationDocumentPartnersByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, VerificationDocumentPartner::class);
    }

    public function save(VerificationDocumentPartner $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtCheckId(),
            $entity->getType(),
            $entity->getExtVerificationDocumentId(),
            $entity->getExtVerificationDocumentVersion(),
        ]);

        $this->verificationDocumentPartnersByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId, ?string $extCheckId, string $type, string $extVerificationDocumentId, string $extVerificationDocumentVersion): ?VerificationDocumentPartner
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extCheckId,
            $type,
            $extVerificationDocumentId,
            $extVerificationDocumentVersion,
        ]);

        if (array_key_exists(key: $key, array: $this->verificationDocumentPartnersByUniqueContraints)) {
            return $this->verificationDocumentPartnersByUniqueContraints[$key];
        }

        $verificationDocumentPartner = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extCheckId' => $extCheckId,
            'type' => $type,
            'extVerificationDocumentId' => $extVerificationDocumentId,
            'extVerificationDocumentVersion' => $extVerificationDocumentVersion,
        ]);

        if (null !== $verificationDocumentPartner) {
            $this->verificationDocumentPartnersByUniqueContraints[$key] = $verificationDocumentPartner;
        }

        return $verificationDocumentPartner;
    }

    public function reset(): void
    {
        $this->verificationDocumentPartnersByUniqueContraints = [];
    }

    /**
     * @return array<VerificationDocumentPartner>
     */
    public function findAllByExtVerificationDocumentId(string $extVerificationDocumentId): array
    {
        $qb = $this->createQueryBuilder(alias: 'verificationDocumentPartner')
                ->where('verificationDocumentPartner.extVerificationDocumentId = :extVerificationDocumentId')
                ->setParameter(key: 'extVerificationDocumentId', value: $extVerificationDocumentId);

        /** @var array<VerificationDocumentPartner> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }
}
