<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Contract;
use App\Domain\Entity\ContractPartner;
use App\Domain\Entity\Enum\PartnerRole;
use App\Domain\Entity\Service;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<ContractPartner>
 */
class ContractPartnerRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /** @var array<string, ContractPartner> */
    private array $contractPersonsByUniqueConstraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, ContractPartner::class);
    }

    public function save(ContractPartner $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtContractId(),
            $entity->getExtContractPosId(),
            $entity->getRole(),
        ]);

        $this->contractPersonsByUniqueConstraints[$key] = $entity;
    }

    public function findByUniqueConstraint(?string $extId, string $extContractId, ?string $extContractPosId, string $role): ?ContractPartner
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extContractId,
            $extContractPosId,
            $role,
        ]);

        if (array_key_exists(key: $key, array: $this->contractPersonsByUniqueConstraints)) {
            return $this->contractPersonsByUniqueConstraints[$key];
        }

        $contactPerson = $this->findOneBy(criteria: [
            'extId' => $extId ?? '',
            'extContractId' => $extContractId,
            'extContractPosId' => $extContractPosId,
            'role' => $role,
        ]);

        if (null !== $contactPerson) {
            $this->contractPersonsByUniqueConstraints[$key] = $contactPerson;
        }

        return $contactPerson;
    }

    public function reset(): void
    {
        $this->contractPersonsByUniqueConstraints = [];
    }

    /**
     * @return array<int, ContractPartner>
     */
    public function findByExtServiceIdAndExtPosId(string $extServiceId, int $extPosId): array
    {
        /** @var array<int, ContractPartner> $result */
        $result = $this->createQueryBuilder(alias: 'contractPartner')
            ->innerJoin(join: Service::class, alias: 'service', conditionType: Join::WITH, condition: 'service.extContractId = contractPartner.extContractId')
            ->andWhere('service.extId = :serviceId')
            ->andWhere('service.extPosId = :posId')
            ->setParameter(key: 'serviceId', value: $extServiceId)
            ->setParameter(key: 'posId', value: $extPosId)
            ->getQuery()
            ->getResult();

        return $result;
    }

    /**
     * @return array<int, ContractPartner>
     */
    public function findByContractAndBusinessPartner(string $contractId, string $businessPartnerId): array
    {
        /** @var array<int, ContractPartner> $result */
        $result = $this->createQueryBuilder(alias: 'contractPartner')
            ->innerJoin(join: Contract::class, alias: 'contract', conditionType: Join::WITH, condition: 'contract.extId = contractPartner.extContractId')
            ->andWhere('contract.id = :contractId')
            ->andWhere('contractPartner.businessPartnerId = :businessPartnerId')
            ->andWhere('contractPartner.contractPosId is not null')
            ->setParameter(key: 'contractId', value: $contractId)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->getQuery()
            ->getResult();

        return $result;
    }

    public function findOneByExtContractIdBusinessPartnerAndRole(
        string $extContractId,
        string $businessPartnerId,
        string $contractPartnerRole = PartnerRole::CLIENT->value,
    ): ?ContractPartner {
        /** @var ?ContractPartner $result */
        $result = $this->createQueryBuilder(alias: 'contractPartner')
            ->andWhere('contractPartner.extContractId = :contractId')
            ->andWhere('contractPartner.businessPartnerId = :businessPartnerId')
            ->andWhere('contractPartner.role = :contractPartnerRole')
            ->andWhere('contractPartner.contractPosId is not null')
            ->setParameter(key: 'contractId', value: $extContractId)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'contractPartnerRole', value: $contractPartnerRole)
            ->getQuery()
            ->getOneOrNullResult();

        return $result;
    }
}
