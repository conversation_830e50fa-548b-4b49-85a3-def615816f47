<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Route;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<Route>
 */
class RouteRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, Route> */
    private array $routesByUniqueConstraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, Route::class);
    }

    public function save(Route $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
        ]);

        $this->routesByUniqueConstraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId): ?Route
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
        ]);

        if (array_key_exists(key: $key, array: $this->routesByUniqueConstraints)) {
            return $this->routesByUniqueConstraints[$key];
        }

        $route = $this->findOneBy(criteria: [
            'extId' => $extId,
        ]);

        if (null !== $route) {
            $this->routesByUniqueConstraints[$key] = $route;
        }

        return $route;
    }

    public function reset(): void
    {
        $this->routesByUniqueConstraints = [];
    }
}
