<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\BusinessPartner;
use App\Domain\Entity\ContractPartner;
use App\Domain\Entity\Enum\PartnerRole;
use App\Domain\Entity\Service;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<BusinessPartner>
 */
class BusinessPartnerRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, BusinessPartner> */
    private array $businessPartnersByUniqueConstraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, BusinessPartner::class);
    }

    public function save(BusinessPartner $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
        ]);

        $this->businessPartnersByUniqueConstraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extId): ?BusinessPartner
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
        ]);

        if (array_key_exists(key: $key, array: $this->businessPartnersByUniqueConstraints)) {
            return $this->businessPartnersByUniqueConstraints[$key];
        }

        $businessPartner = $this->findOneBy(criteria: [
            'extId' => $extId,
        ]);

        if (null !== $businessPartner) {
            $this->businessPartnersByUniqueConstraints[$key] = $businessPartner;
        }

        return $businessPartner;
    }

    public function reset(): void
    {
        $this->businessPartnersByUniqueConstraints = [];
    }

    /**
     * @return Paginator<BusinessPartner>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteriaForManagement(UserSearchCriteria $userSearchCriteria): Paginator
    {
        $queryBuilder = $this->createQueryBuilder(alias: 'businessPartner');

        $search = $userSearchCriteria->filters['search']->value ?? null;

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $queryBuilder->andWhere($queryBuilder->expr()->orX(
                $queryBuilder->expr()->like(x: 'businessPartner.extId', y: ':search'),
                $queryBuilder->expr()->like(x: 'businessPartner.name', y: ':search'),
            ))->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $queryBuilder->addCriteria(criteria: $criteria);

        /** @var Paginator<BusinessPartner> $paginator */
        $paginator = new Paginator(query: $queryBuilder, fetchJoinCollection: false);

        return $paginator;
    }

    public function findByExtServiceIdAndPos(
        string $extServiceId,
        string $extServicePosId,
    ): ?BusinessPartner {
        /** @var ?BusinessPartner $result */
        $result = $this->createQueryBuilder(alias: 'businessPartner')
            ->innerJoin(
                join: ContractPartner::class,
                alias: 'partner',
                conditionType: Join::WITH,
                condition: 'partner.businessPartnerId = businessPartner.id',
            )
            ->innerJoin(
                join: Service::class,
                alias: 'service',
                conditionType: Join::WITH,
                condition: 'service.extContractId = partner.extContractId',
            )
            ->andWhere('service.extId = :extId')
            ->andWhere('service.extPosId = :extPosId')
            ->andWhere('partner.role = :role')
            ->setParameter(key: 'extId', value: $extServiceId)
            ->setParameter(key: 'extPosId', value: $extServicePosId)
            ->setParameter(key: 'role', value: PartnerRole::CLIENT->value)
            ->getQuery()
            ->getOneOrNullResult();

        return $result;
    }
}
