<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\SupervisionDocumentSignData;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<SupervisionDocumentSignData>
 */
class SupervisionDocumentSignDataRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, SupervisionDocumentSignData> */
    private array $supervisionDocumentSignDataByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, SupervisionDocumentSignData::class);
    }

    public function save(SupervisionDocumentSignData $entity, string $extSignDataId): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getId(),
            $extSignDataId,
        ]);

        $this->supervisionDocumentSignDataByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $supervisionDocumentId, string $extSignDataId): ?SupervisionDocumentSignData
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $supervisionDocumentId,
            $extSignDataId,
        ]);

        if (array_key_exists(key: $extSignDataId, array: $this->supervisionDocumentSignDataByUniqueContraints)) {
            return $this->supervisionDocumentSignDataByUniqueContraints[$key];
        }

        $supervisionDocumentEntity = $this->findOneBy(criteria: [
            'supervisionDocumentId' => $supervisionDocumentId,
            'extSignDataId' => $extSignDataId,
        ]);

        if (null !== $supervisionDocumentEntity) {
            $this->supervisionDocumentSignDataByUniqueContraints[$key] = $supervisionDocumentEntity;
        }

        return $supervisionDocumentEntity;
    }

    public function reset(): void
    {
        $this->supervisionDocumentSignDataByUniqueContraints = [];
    }

    /**
     * @return array<SupervisionDocumentSignData>
     */
    public function findAllBySupervisionDocumentId(string $id): array
    {
        $qb = $this->createQueryBuilder(alias: 'supervision_document_sign_data')
            ->andWhere('supervision_document_sign_data.supervisionDocumentId = :id')
            ->setParameter(key: 'id', value: $id);

        /** @var array<SupervisionDocumentSignData> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }
}
