<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Service;
use App\Domain\Service\UniqueContraintGenerator;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<Service>
 */
class ServiceRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, Service> */
    private array $servicesByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, Service::class);
    }

    public function save(Service $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtPosId(),
            $entity->getEndDate(),
        ]);

        $this->servicesByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(?string $extId, ?string $extPosId, string $endDate): ?Service
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extPosId,
            $endDate,
        ]);

        if (array_key_exists(key: $key, array: $this->servicesByUniqueContraints)) {
            return $this->servicesByUniqueContraints[$key];
        }

        $service = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extPosId' => $extPosId,
            'endDate' => DatetimeUtil::convertToDate(date: $endDate),
        ]);

        if (null !== $service) {
            $this->servicesByUniqueContraints[$key] = $service;
        }

        return $service;
    }

    public function reset(): void
    {
        $this->servicesByUniqueContraints = [];
    }

    public function findOneByExtIdAndExtPosId(string $extId, string $extPosId): ?Service
    {
        /** @var ?Service $result */
        $result = $this->createQueryBuilder(alias: 'service')
            ->andWhere('service.extId = :extId')
            ->andWhere('service.extPosId = :extPosId')
            ->setParameter(key: 'extId', value: $extId)
            ->setParameter(key: 'extPosId', value: $extPosId)
            ->getQuery()
            ->getOneOrNullResult();

        return $result;
    }

    /**
     * @return array<Service>
     */
    public function findAllByExtContractId(string $extContractId): array
    {
        /** @var array<Service> $result */
        $result = $this->createQueryBuilder(alias: 'service')
            ->where('service.extContractId = :extContractId')
            ->setParameter(key: 'extContractId', value: $extContractId)
            ->getQuery()
            ->getResult();

        return $result;
    }
}
