<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\ServiceProduct;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;

/**
 * @extends ServiceEntityRepository<ServiceProduct>
 */
class ServiceProductRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, ServiceProduct::class);
    }

    public function add(ServiceProduct $entity): void
    {
        $this->getEntityManager()->persist($entity);
    }

    public function remove(ServiceProduct $entity): void
    {
        $this->getEntityManager()->remove($entity);
    }

    /**
     * @implements \IteratorAggregate<array-key, ServiceProduct>
     *
     * @return Paginator<ServiceProduct>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteria(UserSearchCriteria $userSearchCriteria): Paginator
    {
        $qb = $this->createQueryBuilder(alias: 'serviceProduct');
        $serviceType = $userSearchCriteria->filters['serviceType']->value ?? null;
        $serviceProduct = $userSearchCriteria->filters['serviceProduct']->value ?? null;
        $search = $userSearchCriteria->filters['search']->value ?? null;

        if (null !== $serviceType) {
            $qb->andWhere('serviceProduct.serviceType = :serviceType')
                ->setParameter(key: 'serviceType', value: $serviceType);
        }

        if (null !== $serviceProduct) {
            $qb->andWhere('serviceProduct.serviceProduct = :serviceProduct')
                ->setParameter(key: 'serviceProduct', value: $serviceProduct);
        }

        if (is_string(value: $search)) {
            $qb->andWhere('serviceProduct.container LIKE :search')
                ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<ServiceProduct> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }
}
