<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Enum\NoticeTextType;
use App\Domain\Entity\NoticeText;
use App\Domain\Service\UniqueContraintGenerator;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<NoticeText>
 */
class NoticeTextRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, NoticeText> */
    private array $noticeTextsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, NoticeText::class);
    }

    public function save(NoticeText $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtPosId(),
            $entity->getEndDate(),
            $entity->getType(),
        ]);

        $this->noticeTextsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(?string $extId, ?string $extPosId, string $endDate, string $type): ?NoticeText
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extPosId,
            $endDate,
            $type,
        ]);

        if (array_key_exists(key: $key, array: $this->noticeTextsByUniqueContraints)) {
            return $this->noticeTextsByUniqueContraints[$key];
        }

        $noticeText = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extPosId' => $extPosId,
            'endDate' => DatetimeUtil::convertToDate(date: $endDate),
            'type' => $type,
        ]);

        if (null !== $noticeText) {
            $this->noticeTextsByUniqueContraints[$key] = $noticeText;
        }

        return $noticeText;
    }

    public function reset(): void
    {
        $this->noticeTextsByUniqueContraints = [];
    }

    public function findOnyByServiceIdAndType(string $serviceId, NoticeTextType $noticeTextType): ?NoticeText
    {
        /** @var ?NoticeText $result */
        $result = $this->createQueryBuilder(alias: 'noticeText')
            ->andWhere('noticeText.serviceId = :serviceId')
            ->andWhere('noticeText.type = :noticeTextType')
            ->setParameter(key: 'serviceId', value: $serviceId)
            ->setParameter(key: 'noticeTextType', value: $noticeTextType)
            ->getQuery()
            ->getOneOrNullResult();

        return $result;
    }
}
