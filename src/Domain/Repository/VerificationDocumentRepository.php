<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\VerificationDocument;
use App\Domain\Entity\VerificationDocumentPartner;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<VerificationDocument>
 */
class VerificationDocumentRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, VerificationDocument> */
    private array $verificationDocumentsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, VerificationDocument::class);
    }

    public function save(VerificationDocument $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtVersion(),
        ]);

        $this->verificationDocumentsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId, string $extVersion): ?VerificationDocument
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extVersion,
        ]);

        if (array_key_exists(key: $key, array: $this->verificationDocumentsByUniqueContraints)) {
            return $this->verificationDocumentsByUniqueContraints[$key];
        }

        $verificationDocument = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extVersion' => $extVersion,
        ]);

        if (null !== $verificationDocument) {
            $this->verificationDocumentsByUniqueContraints[$key] = $verificationDocument;
        }

        return $verificationDocument;
    }

    public function reset(): void
    {
        $this->verificationDocumentsByUniqueContraints = [];
    }

    public function findOneByIdAndBusinessPartner(string $id, string $businessPartnerId): ?VerificationDocument
    {
        $qb = $this->createQueryBuilder(alias: 'verificationDocument')
            ->innerJoin(
                join: VerificationDocumentPartner::class,
                alias: 'verificationDocumentPartners',
                conditionType: Join::WITH,
                condition: 'verificationDocument.extId = verificationDocumentPartners.extVerificationDocumentId')
            ->andWhere('verificationDocument.id = :id')
            ->andWhere('verificationDocumentPartners.businessPartnerId = :businessPartnerId')
            ->setParameter(key: 'id', value: $id)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        /** @var VerificationDocument|null $result */
        $result = $qb->getQuery()->getOneOrNullResult();

        return $result;
    }

    /**
     * @return array<VerificationDocument>
     */
    public function findAllByBusinessPartner(string $businessPartnerId): array
    {
        $qb = $this->createQueryBuilder(alias: 'verificationDocument')
            ->innerJoin(
                join: VerificationDocumentPartner::class,
                alias: 'verificationDocumentPartners',
                conditionType: Join::WITH,
                condition: 'verificationDocument.extId = verificationDocumentPartners.extVerificationDocumentId')
            ->andWhere('verificationDocumentPartners.businessPartnerId = :businessPartnerId')
            ->select('DISTINCT verificationDocument')
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        /** @var array<VerificationDocument> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @implements \IteratorAggregate<array-key, VerificationDocument>
     *
     * @return Paginator<VerificationDocument>
     *
     * @throws QueryException
     * @throws \DateMalformedStringException
     */
    public function findByUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $businessPartnerId): Paginator
    {
        $search = $userSearchCriteria->filters['search']->value ?? null;
        $status = $userSearchCriteria->filters['status']->value ?? null;
        $dateFrom = $userSearchCriteria->filters['dateFrom']->value ?? null;
        $dateTo = $userSearchCriteria->filters['dateTo']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'verificationDocument')
                ->innerJoin(
                    join: VerificationDocumentPartner::class,
                    alias: 'verificationDocumentPartners',
                    conditionType: Join::WITH,
                    condition: 'verificationDocument.extId = verificationDocumentPartners.extVerificationDocumentId')
                ->andWhere('verificationDocumentPartners.businessPartnerId = :businessPartnerId')
                ->setParameter(key: 'businessPartnerId', value: $businessPartnerId);

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb = $qb->andWhere(
                'verificationDocument.extId LIKE :search
                OR verificationDocument.avvId LIKE :search')
                ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        if (is_string(value: $status)) {
            $qb = $qb->andWhere('verificationDocument.status = :status')
                ->setParameter(key: 'status', value: $status);
        }

        if (is_string(value: $dateFrom)) {
            $dateObject = new \DateTime(datetime: $dateFrom);
            $qb = $qb->andWhere('verificationDocument.endDate >= :startOfDay')
                ->setParameter(key: 'startOfDay', value: $dateObject->setTime(hour: 0, minute: 0));
        }

        if (is_string(value: $dateTo)) {
            $dateObject = new \DateTime(datetime: $dateTo);
            $qb = $qb->andWhere('verificationDocument.endDate <= :endOfDay')
                ->setParameter(key: 'endOfDay', value: $dateObject->setTime(hour: 23, minute: 59, second: 59));
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<VerificationDocument> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }
}
