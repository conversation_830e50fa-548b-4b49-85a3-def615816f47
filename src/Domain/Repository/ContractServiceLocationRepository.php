<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Contract;
use App\Domain\Entity\ContractServiceLocation;
use App\Domain\Entity\Enum\ServiceType;
use App\Domain\Entity\Service;
use App\Domain\Entity\ServiceLocation;
use App\Domain\Service\UniqueContraintGenerator;
use App\Infrastructure\SapEurope\Util\DatetimeUtil;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<ContractServiceLocation>
 */
class ContractServiceLocationRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, ContractServiceLocation> */
    private array $contractServiceLocationsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, ContractServiceLocation::class);
    }

    public function save(ContractServiceLocation $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtPosId(),
            $entity->getValidTo(),
        ]);

        $this->contractServiceLocationsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extId, string $extPosId, string $validTo, string $validToTime): ?ContractServiceLocation
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extPosId,
            $validTo,
        ]);

        if (array_key_exists(key: $key, array: $this->contractServiceLocationsByUniqueContraints)) {
            return $this->contractServiceLocationsByUniqueContraints[$key];
        }

        $date = DatetimeUtil::convertToDate(
            date: $validTo,
            time: $validToTime
        );

        $contractServiceLocation = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extPosId' => $extPosId,
            'validTo' => $date,
        ]);

        if (null !== $contractServiceLocation) {
            $this->contractServiceLocationsByUniqueContraints[$key] = $contractServiceLocation;
        }

        return $contractServiceLocation;
    }

    public function reset(): void
    {
        $this->contractServiceLocationsByUniqueContraints = [];
    }

    /**
     * @return array<ContractServiceLocation>
     */
    public function findAllByExtServiceLocationId(string $extServiceLocationId): array
    {
        /** @var array<ContractServiceLocation> $result */
        $result = $this->createQueryBuilder(alias: 'contractServiceLocation')
            ->andWhere('contractServiceLocation.extServiceLocationId = :extServiceLocationId')
            ->setParameter(key: 'extServiceLocationId', value: $extServiceLocationId)
            ->getQuery()
            ->getResult();

        return $result;
    }

    /**
     * @return array<ContractServiceLocation>
     */
    public function findByContractIdAndBusinessPartner(string $contractId, string $businessPartnerId): array
    {
        $qb = $this->createQueryBuilder(alias: 'contractServiceLocation')
            ->andWhere('contractServiceLocation.extContractId = :contractId')
            ->andWhere('contractServiceLocation.extBusinessPartnerId = :businessPartnerId')
            ->setParameter(key: 'contractId', value: $contractId)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->orderBy(sort: 'contractServiceLocation.extPosId', order: 'ASC');

        /** @var array<ContractServiceLocation> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @implements \IteratorAggregate<array-key, ContractServiceLocation>
     *
     * @return Paginator<ContractServiceLocation>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $contractId, string $businessPartnerId): Paginator
    {
        $search = $userSearchCriteria->filters['search']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'contractServiceLocation')
            ->select('DISTINCT contractServiceLocation')
            ->andWhere('contractServiceLocation.extContractId = :contractId')
            ->andWhere('contractServiceLocation.extBusinessPartnerId = :businessPartnerId')
            ->setParameter(key: 'contractId', value: $contractId)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->orderBy(sort: 'contractServiceLocation.extPosId', order: 'ASC');

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb = $qb->andWhere('(contractServiceLocation.extId LIKE :search OR contractServiceLocation.extContainerMaterialId LIKE :search OR contractServiceLocation.extServiceLocationId LIKE :search)')
                ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<ContractServiceLocation> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }

    public function countEquipmentByContractIdServiceTypeAndServiceLocationId(
        string $contractId,
        string $serviceLocationId,
        ServiceType $serviceType,
    ): int {
        return (int) $this->createQueryBuilder(alias: 'location')
             ->select('COUNT(DISTINCT location.equipment)')
             ->innerJoin(join: Contract::class, alias: 'contract', conditionType: Join::WITH, condition: 'location.extContractId = contract.extId')
             ->innerJoin(join: ServiceLocation::class, alias: 'serviceLocation', conditionType: Join::WITH, condition: 'location.extServiceLocationId = serviceLocation.extId')
             ->innerJoin(join: Service::class, alias: 'service', conditionType: Join::WITH, condition: 'service.extServiceLocationId = serviceLocation.extId')
             ->andWhere('contract.id = :contractId')
             ->andWhere('serviceLocation.id = :serviceLocationId')
             ->andWhere('service.serviceType = :serviceType')
             ->setParameter(key: 'contractId', value: $contractId)
             ->setParameter(key: 'serviceLocationId', value: $serviceLocationId)
             ->setParameter(key: 'serviceType', value: $serviceType->value)
             ->getQuery()
             ->getSingleScalarResult();
    }
}
