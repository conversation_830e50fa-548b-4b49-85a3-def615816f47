<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\SignData;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<SignData>
 */
class SignDataRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, SignData> */
    private array $signDataByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, SignData::class);
    }

    public function save(SignData $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getSequence(),
            $entity->getExtId(),
            $entity->getCount(),
        ]);

        $this->signDataByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $sequence, string $extId, string $count): ?SignData
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $sequence,
            $extId,
            $count,
        ]);

        if (array_key_exists(key: $key, array: $this->signDataByUniqueContraints)) {
            return $this->signDataByUniqueContraints[$key];
        }

        $signData = $this->findOneBy(criteria: [
            'sequence' => $sequence,
            'extId' => $extId,
            'count' => $count,
        ]);

        if (null !== $signData) {
            $this->signDataByUniqueContraints[$key] = $signData;
        }

        return $signData;
    }

    public function reset(): void
    {
        $this->signDataByUniqueContraints = [];
    }

    /**
     * @return array<SignData>
     */
    public function findAllByExtId(string $id): array
    {
        $qb = $this->createQueryBuilder(alias: 'sign_data')
            ->andWhere('sign_data.extId = :id')
            ->setParameter(key: 'id', value: $id);

        /** @var array<SignData> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }
}
