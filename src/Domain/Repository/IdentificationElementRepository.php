<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\IdentificationElement;
use App\Domain\Repository\Interface\HasBusinessPartner;
use App\Domain\Repository\Trait\BusinessPartnerTrait;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<IdentificationElement>
 */
class IdentificationElementRepository extends ServiceEntityRepository implements ResetInterface, HasBusinessPartner
{
    use BusinessPartnerTrait;

    /** @var array<string, IdentificationElement> */
    private array $identificationElementsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, IdentificationElement::class);
    }

    public function save(IdentificationElement $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtBusinessPartnerId(),
            $entity->getExtIdentificationCategory(),
            $entity->getExtIdentificationNumber(),
        ]);

        $this->identificationElementsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extBusinessPartnerId, string $extIdentificationCategory, string $extIdentificationNumber): ?IdentificationElement
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extBusinessPartnerId,
            $extIdentificationCategory,
            $extIdentificationNumber,
        ]);

        if (array_key_exists(key: $key, array: $this->identificationElementsByUniqueContraints)) {
            return $this->identificationElementsByUniqueContraints[$key];
        }

        $identificationElement = $this->findOneBy(criteria: [
            'extBusinessPartnerId' => $extBusinessPartnerId,
            'extIdentificationCategory' => $extIdentificationCategory,
            'extIdentificationNumber' => $extIdentificationNumber,
        ]);

        if (null !== $identificationElement) {
            $this->identificationElementsByUniqueContraints[$key] = $identificationElement;
        }

        return $identificationElement;
    }

    public function reset(): void
    {
        $this->identificationElementsByUniqueContraints = [];
    }
}
