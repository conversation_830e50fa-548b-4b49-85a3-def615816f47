<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\OrderConfirmation;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<OrderConfirmation>
 */
class OrderConfirmationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OrderConfirmation::class);
    }

    public function add(OrderConfirmation $entity): void
    {
        $this->getEntityManager()->persist($entity);
    }

    public function remove(OrderConfirmation $entity): void
    {
        $this->getEntityManager()->remove($entity);
    }
}
