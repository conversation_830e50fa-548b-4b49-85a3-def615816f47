<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\InvoiceContract;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<InvoiceContract>
 */
class InvoiceContractRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, InvoiceContract> */
    private array $invoiceContractsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, InvoiceContract::class);
    }

    public function save(InvoiceContract $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtInvoiceId(),
        ]);

        $this->invoiceContractsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueContraints(string $extId, string $extInvoiceId): ?InvoiceContract
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extInvoiceId,
        ]);

        if (array_key_exists(key: $key, array: $this->invoiceContractsByUniqueContraints)) {
            return $this->invoiceContractsByUniqueContraints[$key];
        }

        $invoiceContract = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extInvoiceId' => $extInvoiceId,
        ]);

        if (null !== $invoiceContract) {
            $this->invoiceContractsByUniqueContraints[$key] = $invoiceContract;
        }

        return $invoiceContract;
    }

    public function reset(): void
    {
        $this->invoiceContractsByUniqueContraints = [];
    }
}
