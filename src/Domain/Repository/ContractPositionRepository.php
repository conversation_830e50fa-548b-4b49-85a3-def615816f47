<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\Contract;
use App\Domain\Entity\ContractPartner;
use App\Domain\Entity\ContractPosition;
use App\Domain\Entity\Enum\PartnerRole;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\QueryException;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use PreZero\ApiBundle\UserCriteria\UserSearchCriteria;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<ContractPosition>
 */
class ContractPositionRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, ContractPosition> */
    private array $contractPositionsByUniqueConstraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, ContractPosition::class);
    }

    public function save(ContractPosition $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtPosId(),
        ]);

        $this->contractPositionsByUniqueConstraints[$key] = $entity;
    }

    public function findByUniqueConstraint(string $extId, string $extPosId): ?ContractPosition
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extPosId,
        ]);

        if (array_key_exists(key: $key, array: $this->contractPositionsByUniqueConstraints)) {
            return $this->contractPositionsByUniqueConstraints[$key];
        }

        $contactPerson = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extPosId' => $extPosId,
        ]);

        if (null !== $contactPerson) {
            $this->contractPositionsByUniqueConstraints[$key] = $contactPerson;
        }

        return $contactPerson;
    }

    public function reset(): void
    {
        $this->contractPositionsByUniqueConstraints = [];
    }

    /**
     * @return array<ContractPosition>
     */
    public function findByContractIdAndBusinessPartner(string $contractId, string $businessPartnerId): array
    {
        $qb = $this->createQueryBuilder(alias: 'contractPosition')
            ->innerJoin(join: ContractPartner::class, alias: 'contractPartners', conditionType: Join::WITH, condition: 'contractPosition.extId = contractPartners.extContractId')
            ->andWhere('contractPosition.extId = :contractId') // TODO we should clarify in method parameter that its extId
            ->andWhere('contractPartners.businessPartnerId = :businessPartnerId')
            ->andWhere('contractPartners.role = :role')
            ->setParameter(key: 'contractId', value: $contractId)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'role', value: PartnerRole::CLIENT)
            ->orderBy(sort: 'contractPosition.extPosId', order: 'ASC');

        /** @var array<ContractPosition> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }

    /**
     * @implements \IteratorAggregate<array-key, ContractPosition>
     *
     * @return Paginator<ContractPosition>
     *
     * @throws QueryException
     */
    public function findByUserSearchCriteria(UserSearchCriteria $userSearchCriteria, string $contractId, string $businessPartnerId): Paginator
    {
        $search = $userSearchCriteria->filters['search']->value ?? null;

        $qb = $this->createQueryBuilder(alias: 'contractPosition')
            ->select('DISTINCT contractPosition')
            ->innerJoin(join: ContractPartner::class, alias: 'contractPartners', conditionType: Join::WITH, condition: 'contractPosition.extId = contractPartners.extContractId')
            ->andWhere('contractPosition.extId = :contractId')
            ->andWhere('contractPartners.businessPartnerId = :businessPartnerId')
            ->andWhere('contractPartners.role = :role')
            ->setParameter(key: 'contractId', value: $contractId)
            ->setParameter(key: 'businessPartnerId', value: $businessPartnerId)
            ->setParameter(key: 'role', value: PartnerRole::CLIENT)
            ->orderBy(sort: 'contractPosition.extPosId', order: 'ASC');

        if (is_string(value: $search) && '' !== trim(string: $search)) {
            $qb = $qb->andWhere('(contractPosition.extPosId LIKE :search OR contractPosition.materialText LIKE :search OR contractPosition.extMaterialId LIKE :search)')
                ->setParameter(key: 'search', value: '%'.$search.'%');
        }

        $offset = ($userSearchCriteria->page - 1) * $userSearchCriteria->perPage;

        $criteria = Criteria::create()
            ->setFirstResult(firstResult: $offset)
            ->setMaxResults(maxResults: $userSearchCriteria->perPage);

        $qb->addCriteria(criteria: $criteria);

        /** @var Paginator<ContractPosition> $paginator */
        $paginator = new Paginator(query: $qb, fetchJoinCollection: false);

        return $paginator;
    }

    public function findOneByContractIdAndMaterialType(
        string $contractId,
        string $materialType,
        bool $display = true,
    ): ?ContractPosition {
        /** @var ?array<int, ContractPosition> $result */
        $result = $this->createQueryBuilder(alias: 'contractPosition')
            ->andWhere('contractPosition.contractId = :contractId')
            ->andWhere('contractPosition.materialType = :materialType')
            ->andWhere('contractPosition.display = :display')
            ->setParameter(key: 'contractId', value: $contractId)
            ->setParameter(key: 'materialType', value: $materialType)
            ->setParameter(key: 'display', value: $display)
            ->getQuery()
            ->getResult();

        return $result[0] ?? null;
    }

    /**
     * @return array<ContractPosition>
     */
    public function findAllByContractIdAndMaterialType(
        string $contractId,
        string $materialType,
        bool $display = true,
    ): array {
        /** @var array<ContractPosition> $result */
        $result = $this->createQueryBuilder(alias: 'contractPosition')
            ->andWhere('contractPosition.contractId = :contractId')
            ->andWhere('contractPosition.materialType = :materialType')
            ->andWhere('contractPosition.display = :display')
            ->setParameter(key: 'contractId', value: $contractId)
            ->setParameter(key: 'materialType', value: $materialType)
            ->setParameter(key: 'display', value: $display)
            ->getQuery()
            ->getResult();

        return $result;
    }

    public function findOneByExtContractIdAndExtMaterialId(
        string $extContractId,
        string $extMaterialId,
        bool $display = true,
        ?string $materialType = null,
    ): ?ContractPosition {
        $qb = $this->createQueryBuilder(alias: 'contractPosition')
            ->innerJoin(join: Contract::class, alias: 'contract', conditionType: Join::WITH, condition: 'contract.id = contractPosition.contractId')
            ->andWhere('contract.extId = :extContractId')
            ->andWhere('contractPosition.extMaterialId = :extMaterialId')
            ->andWhere('contractPosition.display = :display')
            ->setParameter(key: 'extContractId', value: $extContractId)
            ->setParameter(key: 'extMaterialId', value: $extMaterialId)
            ->setParameter(key: 'display', value: $display);

        if (null !== $materialType) {
            $qb->andWhere('contractPosition.materialType = :materialType')
                ->setParameter(key: 'materialType', value: $materialType);
        }

        /** @var ?ContractPosition $result */
        $result = $qb->getQuery()->getOneOrNullResult();

        return $result;
    }
}
