<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\ArchiveDocument;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<ArchiveDocument>
 */
class ArchiveDocumentRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, ArchiveDocument> */
    private array $archiveDocumentsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, ArchiveDocument::class);
    }

    public function save(ArchiveDocument $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getExtId(),
            $entity->getExtObject(),
            $entity->getExtObjectId(),
        ]);

        $this->archiveDocumentsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $extId, string $extObject, string $extObjectId): ?ArchiveDocument
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $extId,
            $extObject,
            $extObjectId,
        ]);

        if (array_key_exists(key: $key, array: $this->archiveDocumentsByUniqueContraints)) {
            return $this->archiveDocumentsByUniqueContraints[$key];
        }

        $archiveDocument = $this->findOneBy(criteria: [
            'extId' => $extId,
            'extObject' => $extObject,
            'extObjectId' => $extObjectId,
        ]);

        if (null !== $archiveDocument) {
            $this->archiveDocumentsByUniqueContraints[$key] = $archiveDocument;
        }

        return $archiveDocument;
    }

    public function reset(): void
    {
        $this->archiveDocumentsByUniqueContraints = [];
    }
}
