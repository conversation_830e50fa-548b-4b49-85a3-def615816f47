<?php

declare(strict_types=1);

namespace App\Domain\Repository;

use App\Domain\Entity\InvoiceArchiveDocument;
use App\Domain\Service\UniqueContraintGenerator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Contracts\Service\ResetInterface;

/**
 * @extends ServiceEntityRepository<InvoiceArchiveDocument>
 */
class InvoiceArchiveDocumentRepository extends ServiceEntityRepository implements ResetInterface
{
    /** @var array<string, InvoiceArchiveDocument> */
    private array $invoiceArchiveDocumentsByUniqueContraints = [];

    public function __construct(
        ManagerRegistry $registry,
        private readonly UniqueContraintGenerator $generator,
    ) {
        parent::__construct($registry, InvoiceArchiveDocument::class);
    }

    public function save(InvoiceArchiveDocument $entity): void
    {
        $this->getEntityManager()->persist($entity);

        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $entity->getInvoiceId(),
            $entity->getExtDocumentId(),
        ]);

        $this->invoiceArchiveDocumentsByUniqueContraints[$key] = $entity;
    }

    public function findByUniqueConstraints(string $invoiceId, string $extDocumentId): ?InvoiceArchiveDocument
    {
        $key = $this->generator->generateUniqueContraintKey(contraints: [
            $invoiceId,
            $extDocumentId,
        ]);

        if (array_key_exists(key: $key, array: $this->invoiceArchiveDocumentsByUniqueContraints)) {
            return $this->invoiceArchiveDocumentsByUniqueContraints[$key];
        }

        $invoice = $this->findOneBy(criteria: [
            'invoiceId' => $invoiceId,
            'extDocumentId' => $extDocumentId,
        ]);

        if (null !== $invoice) {
            $this->invoiceArchiveDocumentsByUniqueContraints[$key] = $invoice;
        }

        return $invoice;
    }

    public function reset(): void
    {
        $this->invoiceArchiveDocumentsByUniqueContraints = [];
    }
}
