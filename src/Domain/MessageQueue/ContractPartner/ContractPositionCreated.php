<?php

declare(strict_types=1);

namespace App\Domain\MessageQueue\ContractPartner;

use App\Domain\Entity\Enum\Tenant;
use App\Domain\MessageQueue\RequireTenantIdentifier;
use Symfony\Component\Messenger\Attribute\AsMessage;

#[AsMessage('async')]
readonly class ContractPositionCreated implements RequireTenantIdentifier
{
    public function __construct(
        private string $extId,
        private string $extContractId,
        private ?string $extContractPosId,
        private string $role,
        private Tenant $tenant,
    ) {
    }

    public function getExtContractPositionId(): string
    {
        return $this->extId;
    }

    public function getExtContractId(): string
    {
        return $this->extContractId;
    }

    public function getExtContractPosId(): ?string
    {
        return $this->extContractPosId;
    }

    public function getRole(): string
    {
        return $this->role;
    }

    public function getTenant(): Tenant
    {
        return $this->tenant;
    }
}
