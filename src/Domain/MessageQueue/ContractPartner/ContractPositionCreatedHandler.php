<?php

declare(strict_types=1);

namespace App\Domain\MessageQueue\ContractPartner;

use App\Domain\Repository\BusinessPartnerRepository;
use App\Domain\Repository\ContractPartnerRepository;
use App\Domain\Repository\OrderRepository;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class ContractPositionCreatedHandler
{
    public function __construct(
        private BusinessPartnerRepository $businessPartnerRepository,
        private ContractPartnerRepository $repository,
        private OrderRepository $orderRepository,
    ) {
    }

    public function __invoke(ContractPositionCreated $message): void
    {
        $contractPartner = $this->repository->findOneBy(criteria: [
            'extId' => $message->getExtContractPositionId(),
            'extContractId' => $message->getExtContractId(),
            'extContractPosId' => $message->getExtContractPosId(),
            'role' => $message->getRole(),
        ]);

        if (null === $contractPartner) {
            return;
        }

        // set BusinessPartnerId for ContractPartner
        $extBusinessPartnerId = $contractPartner->getExtId();
        if (null == $extBusinessPartnerId) {
            $contractPartner->setBusinessPartnerId(businessPartnerId: null);
            $this->repository->save(entity: $contractPartner);

            return;
        }

        $businessPartner = $this->businessPartnerRepository->findByUniqueConstraints(extId: $extBusinessPartnerId);
        if (null == $businessPartner) {
            $contractPartner->setBusinessPartnerId(businessPartnerId: null);
            $this->repository->save(entity: $contractPartner);

            return;
        }

        $contractPartner->setBusinessPartnerId(businessPartnerId: $businessPartner->getId());
        $this->repository->save(entity: $contractPartner);

        $orders = $this->orderRepository->findAllByExtContractIdAndBusinessPartnerNull(
            extContractId: $contractPartner->getExtContractId(),
        );

        foreach ($orders as $order) {
            $order->setBusinessPartnerId(businessPartnerId: $businessPartner->getId());
            $this->orderRepository->save(entity: $order);
        }
    }
}
