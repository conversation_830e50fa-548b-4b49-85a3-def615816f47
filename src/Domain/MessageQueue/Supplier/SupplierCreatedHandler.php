<?php

declare(strict_types=1);

namespace App\Domain\MessageQueue\Supplier;

use App\Domain\Repository\SupplierRepository;
use App\Infrastructure\Here\Geocode;
use App\Infrastructure\Here\HereClient;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class SupplierCreatedHandler
{
    public function __construct(
        private HereClient $geocodeClient,
        private SupplierRepository $supplierRepository,
    ) {
    }

    public function __invoke(SupplierCreated $message): void
    {
        $supplier = $this->supplierRepository->find(id: $message->getSupplierId());

        if (null === $supplier) {
            return;
        }

        $geocode = Geocode::fromResponse(response: $this->geocodeClient->getGeocode(entity: $supplier));

        $supplier->setFederalState(federalState: $geocode->state);

        $this->supplierRepository->add(entity: $supplier);
    }
}
