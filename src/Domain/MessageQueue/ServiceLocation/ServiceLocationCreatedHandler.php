<?php

declare(strict_types=1);

namespace App\Domain\MessageQueue\ServiceLocation;

use App\Domain\Repository\ServiceLocationRepository;
use App\Infrastructure\Here\Geocode;
use App\Infrastructure\Here\HereClient;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class ServiceLocationCreatedHandler
{
    public function __construct(
        private HereClient $geocodeClient,
        private ServiceLocationRepository $serviceLocationRepository,
    ) {
    }

    public function __invoke(ServiceLocationCreated $message): void
    {
        $serviceLocation = $this->serviceLocationRepository->find(id: $message->getServiceLocationId());

        if (null === $serviceLocation) {
            return;
        }

        $geocode = Geocode::fromResponse(response: $this->geocodeClient->getGeocode(entity: $serviceLocation));

        $serviceLocation->setFederalState(federalState: $geocode->state);

        $this->serviceLocationRepository->save(entity: $serviceLocation);
    }
}
