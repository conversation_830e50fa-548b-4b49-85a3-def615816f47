<?php

declare(strict_types=1);

namespace App\Domain\MessageQueue\ServiceLocation;

use App\Domain\Entity\Enum\Tenant;
use App\Domain\MessageQueue\RequireTenantIdentifier;
use Symfony\Component\Messenger\Attribute\AsMessage;

#[AsMessage('async')]
readonly class ServiceLocationCreated implements RequireTenantIdentifier
{
    public function __construct(
        private string $id,
        private Tenant $tenant,
    ) {
    }

    public function getServiceLocationId(): string
    {
        return $this->id;
    }

    public function getTenant(): Tenant
    {
        return $this->tenant;
    }
}
