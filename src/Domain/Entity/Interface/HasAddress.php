<?php

declare(strict_types=1);

namespace App\Domain\Entity\Interface;

interface HasAddress
{
    public function getStreet(): string;

    public function setStreet(string $street): void;

    public function getHouseNumber(): string;

    public function setHouseNumber(string $houseNumber): void;

    public function getPostalCode(): string;

    public function setPostalCode(string $postalCode): void;

    public function getCity(): string;

    public function setCity(string $city): void;

    public function getDistrict(): ?string;

    public function setDistrict(?string $district): void;

    public function getCountry(): string;

    public function setCountry(string $country): void;

    public function getFederalState(): ?string;

    public function setFederalState(?string $federalState): void;

    public function getAddress(): string;
}
