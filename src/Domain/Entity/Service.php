<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ServiceRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ServiceRepository::class)]
#[ORM\Table(name: 'service')]
#[ORM\Index(fields: ['extId', 'extPosId'])]
#[ORM\Index(fields: ['extContractId'])]
#[ORM\Index(fields: ['extServiceLocationId'])]
#[ORM\Index(fields: ['extRouteId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extPosId', 'endDate'])]
#[UniqueEntity(fields: ['extId', 'extPosId', 'endDate'])]
class Service implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $extId;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $extPosId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $startDate;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $endDate;

    #[ORM\Column(length: 1)]
    private string $period;

    #[ORM\Column(length: 10)]
    private string $extContractId;

    #[ORM\Column(length: 6)]
    private string $extContractPosId;

    #[ORM\Column(length: 2)]
    private string $serviceType;

    #[ORM\Column(length: 40)]
    private string $extContainerMaterialId;

    #[ORM\Column(length: 40)]
    private string $extWasteMaterialId;

    #[ORM\Column(length: 30)]
    private string $extServiceLocationId;

    #[ORM\Column]
    private int $containerCount;

    #[ORM\Column(length: 18)]
    private string $equipment;

    #[ORM\Column(length: 10)]
    private string $extRouteId;

    #[ORM\Column(length: 1)]
    private string $serviceFrequency;

    #[ORM\Column(length: 1)]
    private string $dailyFrequency;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $weeklyFrequency;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $monthlyFrequency;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $monthlyFrequencyDay;

    #[ORM\Column(length: 1, nullable: true)]
    private ?string $weekday;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $link;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $mimeType;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $timeto;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $timefr;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtId(): ?string
    {
        return $this->extId;
    }

    public function setExtId(?string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtPosId(): ?string
    {
        return $this->extPosId;
    }

    public function setExtPosId(?string $extPosId): void
    {
        $this->extPosId = $extPosId;
    }

    public function getStartDate(): \DateTimeImmutable
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeImmutable $startDate): void
    {
        $this->startDate = $startDate;
    }

    public function getEndDate(): \DateTimeImmutable
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeImmutable $endDate): void
    {
        $this->endDate = $endDate;
    }

    public function getPeriod(): string
    {
        return $this->period;
    }

    public function setPeriod(string $period): void
    {
        $this->period = $period;
    }

    public function getExtContractId(): string
    {
        return $this->extContractId;
    }

    public function setExtContractId(string $extContractId): void
    {
        $this->extContractId = $extContractId;
    }

    public function getExtContractPosId(): string
    {
        return $this->extContractPosId;
    }

    public function setExtContractPosId(string $extContractPosId): void
    {
        $this->extContractPosId = $extContractPosId;
    }

    public function getServiceType(): string
    {
        return $this->serviceType;
    }

    public function setServiceType(string $serviceType): void
    {
        $this->serviceType = $serviceType;
    }

    public function getExtContainerMaterialId(): string
    {
        return $this->extContainerMaterialId;
    }

    public function setExtContainerMaterialId(string $extContainerMaterialId): void
    {
        $this->extContainerMaterialId = $extContainerMaterialId;
    }

    public function getExtWasteMaterialId(): string
    {
        return $this->extWasteMaterialId;
    }

    public function setExtWasteMaterialId(string $extWasteMaterialId): void
    {
        $this->extWasteMaterialId = $extWasteMaterialId;
    }

    public function getExtServiceLocationId(): string
    {
        return $this->extServiceLocationId;
    }

    public function setExtServiceLocationId(string $extServiceLocationId): void
    {
        $this->extServiceLocationId = $extServiceLocationId;
    }

    public function getContainerCount(): int
    {
        return $this->containerCount;
    }

    public function setContainerCount(int $containerCount): void
    {
        $this->containerCount = $containerCount;
    }

    public function getEquipment(): string
    {
        return $this->equipment;
    }

    public function setEquipment(string $equipment): void
    {
        $this->equipment = $equipment;
    }

    public function getExtRouteId(): string
    {
        return $this->extRouteId;
    }

    public function setExtRouteId(string $extRouteId): void
    {
        $this->extRouteId = $extRouteId;
    }

    public function getServiceFrequency(): string
    {
        return $this->serviceFrequency;
    }

    public function setServiceFrequency(string $serviceFrequency): void
    {
        $this->serviceFrequency = $serviceFrequency;
    }

    public function getDailyFrequency(): string
    {
        return $this->dailyFrequency;
    }

    public function setDailyFrequency(string $dailyFrequency): void
    {
        $this->dailyFrequency = $dailyFrequency;
    }

    public function getWeeklyFrequency(): ?string
    {
        return $this->weeklyFrequency;
    }

    public function setWeeklyFrequency(?string $weeklyFrequency): void
    {
        $this->weeklyFrequency = $weeklyFrequency;
    }

    public function getMonthlyFrequency(): ?string
    {
        return $this->monthlyFrequency;
    }

    public function setMonthlyFrequency(?string $monthlyFrequency): void
    {
        $this->monthlyFrequency = $monthlyFrequency;
    }

    public function getMonthlyFrequencyDay(): ?string
    {
        return $this->monthlyFrequencyDay;
    }

    public function setMonthlyFrequencyDay(?string $monthlyFrequencyDay): void
    {
        $this->monthlyFrequencyDay = $monthlyFrequencyDay;
    }

    public function getWeekday(): ?string
    {
        return $this->weekday;
    }

    public function setWeekday(?string $weekday): void
    {
        $this->weekday = $weekday;
    }

    public function getLink(): ?string
    {
        return $this->link;
    }

    public function setLink(?string $link): void
    {
        $this->link = $link;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(?string $mimeType): void
    {
        $this->mimeType = $mimeType;
    }

    public function getTimeto(): ?\DateTimeImmutable
    {
        return $this->timeto;
    }

    public function setTimeto(?\DateTimeImmutable $timeto): void
    {
        $this->timeto = $timeto;
    }

    public function getTimefr(): ?\DateTimeImmutable
    {
        return $this->timefr;
    }

    public function setTimefr(?\DateTimeImmutable $timefr): void
    {
        $this->timefr = $timefr;
    }
}
