<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Index(fields: ['extSignDataId'])]
#[ORM\Index(fields: ['supervisionDocumentId'])]
#[ORM\UniqueConstraint(fields: ['supervisionDocumentId', 'extSignDataId'])]
class SupervisionDocumentSignData implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(type: Types::GUID, nullable: false)]
    private string $supervisionDocumentId;

    #[ORM\Column(length: 70, nullable: false)]
    private string $extSignDataId;

    public function __construct()
    {
        $this->init();
    }

    public function getSupervisionDocumentId(): string
    {
        return $this->supervisionDocumentId;
    }

    public function setSupervisionDocumentId(string $supervisionDocumentId): void
    {
        $this->supervisionDocumentId = $supervisionDocumentId;
    }

    public function getExtSignDataId(): string
    {
        return $this->extSignDataId;
    }

    public function setExtSignDataId(string $extSignDataId): void
    {
        $this->extSignDataId = $extSignDataId;
    }
}
