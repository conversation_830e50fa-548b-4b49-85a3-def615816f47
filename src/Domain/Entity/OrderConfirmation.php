<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\OrderConfirmationRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OrderConfirmationRepository::class)]
#[ORM\Table(name: 'order_confirmation')]
#[ORM\Index(fields: ['tenant'])]
class OrderConfirmation implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\ManyToOne(targetEntity: Supplier::class, inversedBy: 'orderConfirmations')]
    private Supplier $supplier;

    #[ORM\Column(length: 50)]
    private string $email;

    #[ORM\Column(length: 50)]
    private string $name;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getSupplier(): Supplier
    {
        return $this->supplier;
    }

    public function setSupplier(Supplier $supplier): void
    {
        $this->supplier = $supplier;
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }
}
