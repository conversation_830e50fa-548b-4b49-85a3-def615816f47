<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\MaterialServiceTypeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: MaterialServiceTypeRepository::class)]
#[ORM\Table(name: 'material_service_type')]
#[ORM\Index(fields: ['extMaterialId'])]
class MaterialServiceType implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10)]
    private string $extMaterialId;

    #[ORM\Column(length: 10)]
    private string $serviceType;

    public function __construct()
    {
        $this->init();
    }

    public function getExtMaterialId(): string
    {
        return $this->extMaterialId;
    }

    public function setExtMaterialId(string $extMaterialId): void
    {
        $this->extMaterialId = $extMaterialId;
    }

    public function getServiceType(): string
    {
        return $this->serviceType;
    }

    public function setServiceType(string $serviceType): void
    {
        $this->serviceType = $serviceType;
    }
}
