<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Index(fields: ['extSignDataId'])]
#[ORM\Index(fields: ['verificationDocumentId'])]
#[ORM\UniqueConstraint(fields: ['verificationDocumentId', 'extSignDataId'])]
class VerificationDocumentSignData implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(type: Types::GUID, nullable: false)]
    private string $verificationDocumentId;

    #[ORM\Column(length: 70, nullable: false)]
    private string $extSignDataId;

    public function __construct()
    {
        $this->init();
    }

    public function getVerificationDocumentId(): string
    {
        return $this->verificationDocumentId;
    }

    public function setVerificationDocumentId(string $verificationDocumentId): void
    {
        $this->verificationDocumentId = $verificationDocumentId;
    }

    public function getExtSignDataId(): string
    {
        return $this->extSignDataId;
    }

    public function setExtSignDataId(string $extSignDataId): void
    {
        $this->extSignDataId = $extSignDataId;
    }
}
