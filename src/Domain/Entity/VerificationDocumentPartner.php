<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasAddress;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\AddressTrait;
use App\Domain\Entity\Trait\BusinessPartnerTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\VerificationDocumentPartnerRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: VerificationDocumentPartnerRepository::class)]
#[ORM\Table(name: 'verification_document_partner')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['extBusinessPartnerId'])]
#[ORM\Index(fields: ['extVerificationDocumentId'])]
#[ORM\Index(fields: ['verificationDocumentId'])]
#[ORM\Index(fields: ['businessPartnerId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extCheckId', 'type', 'extVerificationDocumentId', 'extVerificationDocumentVersion'])]
#[UniqueEntity(fields: ['extId', 'extCheckId', 'type', 'extVerificationDocumentId', 'extVerificationDocumentVersion'])]
class VerificationDocumentPartner implements EntityInterface, HasTenant, HasAddress, HasBusinessPartner
{
    use CommonTrait;
    use TenantTrait;
    use AddressTrait;
    use BusinessPartnerTrait;

    #[ORM\Column(length: 50)]
    private string $extDocId;

    #[ORM\Column(length: 9)]
    private string $extId;

    #[ORM\Column(length: 1, nullable: true)]
    private ?string $extCheckId;

    #[ORM\Column(length: 6)]
    private string $type;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    #[ORM\Column(length: 10)]
    private string $extVerificationDocumentId;

    #[ORM\Column(length: 2)]
    private string $extVerificationDocumentVersion;

    #[ORM\Column(type: Types::GUID)]
    private string $verificationDocumentId;

    #[ORM\Column(length: 35)]
    private string $name;

    #[ORM\Column(length: 35, nullable: true)]
    private ?string $contactPerson;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $telephone;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $fax;

    #[ORM\Column(length: 241, nullable: true)]
    private ?string $email;

    #[ORM\Column]
    private bool $roleAllowed;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtDocId(): string
    {
        return $this->extDocId;
    }

    public function setExtDocId(string $extDocId): void
    {
        $this->extDocId = $extDocId;
    }

    public function getExtCheckId(): ?string
    {
        return $this->extCheckId;
    }

    public function setExtCheckId(?string $extCheckId): void
    {
        $this->extCheckId = $extCheckId;
    }

    public function getVerificationDocumentId(): string
    {
        return $this->verificationDocumentId;
    }

    public function setVerificationDocumentId(string $verificationDocumentId): void
    {
        $this->verificationDocumentId = $verificationDocumentId;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }

    public function getExtVerificationDocumentId(): string
    {
        return $this->extVerificationDocumentId;
    }

    public function setExtVerificationDocumentId(string $extVerificationDocumentId): void
    {
        $this->extVerificationDocumentId = $extVerificationDocumentId;
    }

    public function getExtVerificationDocumentVersion(): string
    {
        return $this->extVerificationDocumentVersion;
    }

    public function setExtVerificationDocumentVersion(string $extVerificationDocumentVersion): void
    {
        $this->extVerificationDocumentVersion = $extVerificationDocumentVersion;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getContactPerson(): ?string
    {
        return $this->contactPerson;
    }

    public function setContactPerson(?string $contactPerson): void
    {
        $this->contactPerson = $contactPerson;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(?string $telephone): void
    {
        $this->telephone = $telephone;
    }

    public function getFax(): ?string
    {
        return $this->fax;
    }

    public function setFax(?string $fax): void
    {
        $this->fax = $fax;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function getRoleAllowed(): bool
    {
        return $this->roleAllowed;
    }

    public function setRoleAllowed(bool $roleAllowed): void
    {
        $this->roleAllowed = $roleAllowed;
    }
}
