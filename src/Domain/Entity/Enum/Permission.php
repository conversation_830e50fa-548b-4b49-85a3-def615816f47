<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum Permission: string
{
    case USER_MANAGEMENT = 'permission.user.management';
    case CONTRACT_WITH_PRICES = 'permission.contract.with.prices';
    case CONTRACT_WITHOUT_PRICES = 'permission.contract.without.prices';
    case INVOICE = 'permission.invoice';
    case ORDER_CREATE = 'permission.order.create';
    case EANV = 'permission.eanv';
    case WASTE_STATISTIC = 'permission.waste.statistic';
    case CO2_REPORT = 'permission.co2.report';
}
