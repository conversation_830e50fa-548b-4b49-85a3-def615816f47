<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum VerificationDocumentStatus: string
{
    case DEFAULT = 'NEW'; // Default New
    case SEND_CUSTOMER = 'EKUN'; // EN Versand Kunde
    case SIGNED_CUSTOMER = 'EKSG'; // EN Kunde signiert
    case SEND_DISPOSAL_SITE = 'EANL'; // EN Versand Anlage
    case SIGNED_DISPOSAL_SITE = 'EASG'; // EN Anlage signiert
    case SEND_AUTHORITY = 'EBEH'; // EN Versand Behörde
    case SIGNED_AUTHORITY = 'EBBA'; // EN Behöre bearbeitet
    case APPROVED = 'EGEN'; // EN genehmigt
    case BLOCKED = 'ESPR'; // EN gesperrt
    case EXPIRED = 'EAGL'; // EN abgelaufen

    public function getFrontendStatus(): ?string
    {
        return match ($this) {
            self::SEND_CUSTOMER => 'SIGNATURE_REQUIRED',
            self::SIGNED_CUSTOMER => 'IN_PROGRESS',
            self::SEND_DISPOSAL_SITE => 'IN_PROGRESS',
            self::SIGNED_DISPOSAL_SITE => 'IN_PROGRESS',
            self::SEND_AUTHORITY => 'IN_PROGRESS',
            self::SIGNED_AUTHORITY => 'IN_PROGRESS',
            self::APPROVED => 'APPROVED',
            self::BLOCKED => 'BLOCKED',
            self::EXPIRED => 'EXPIRED',
            default => null,
        };
    }
}
