<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum OrderDelay: string
{
    case ORDER_DELAY_12 = '12';
    case ORDER_DELAY_24 = '24';
    case ORDER_DELAY_36 = '36';
    case ORDER_DELAY_48 = '48';
    case ORDER_DELAY_60 = '60';
    case ORDER_DELAY_72 = '72';

    public function toInterval(): \DateInterval
    {
        return match ($this) {
            self::ORDER_DELAY_12 => new \DateInterval(duration: 'PT'.self::ORDER_DELAY_12->value.'H'),
            self::ORDER_DELAY_24 => new \DateInterval(duration: 'PT'.self::ORDER_DELAY_24->value.'H'),
            self::ORDER_DELAY_36 => new \DateInterval(duration: 'PT'.self::ORDER_DELAY_36->value.'H'),
            self::ORDER_DELAY_48 => new \DateInterval(duration: 'PT'.self::ORDER_DELAY_48->value.'H'),
            self::ORDER_DELAY_60 => new \DateInterval(duration: 'PT'.self::ORDER_DELAY_60->value.'H'),
            self::ORDER_DELAY_72 => new \DateInterval(duration: 'PT'.self::ORDER_DELAY_72->value.'H'),
        };
    }
}
