<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum Tenant: string
{
    case EUROPE = 'pz';
    case NETHERLANDS = 'pznl';

    public const array LIST = [
        self::EUROPE,
        self::NETHERLANDS,
    ];

    public function isEurope(): bool
    {
        return self::EUROPE === $this;
    }

    public function isNetherlands(): bool
    {
        return self::NETHERLANDS === $this;
    }

    public function getName(): string
    {
        return match ($this) {
            self::EUROPE => 'PreZero',
            self::NETHERLANDS => 'PreZero NL',
        };
    }
}
