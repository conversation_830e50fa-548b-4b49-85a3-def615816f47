<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum SupervisionDocumentStatus: string
{
    case DELETED_BEF_1 = 'DELETED_BEF.1'; // Gelöschter Begleitschein
    case DELETED_BEF_2 = 'DELETED_BEF.2'; // Gelöschter Begleitschein
    case DELETED_BEF_3 = 'DELETED_BEF.3'; // Gelöschter Begleitschein
    case DELETED_ENT = 'DELETED_ENT'; // Gelöschter Begleitschein
    case DELETED_ERZ = 'DELETED_ERZ'; // Gelöschter Begleitschein
    case ERROR_BEF_1 = 'ERROR_BEF.1'; // Fehlerhafter Begleitschein
    case ERROR_BEF_2 = 'ERROR_BEF.2'; // Fehlerhafter Begleitschein
    case ERROR_BEF_3 = 'ERROR_BEF.3'; // Fehlerhafter Begleitschein
    case ERROR_ENT = 'ERROR_ENT'; // Fehlerhafter Begleitschein
    case ERROR_ERZ = 'ERROR_ERZ'; // Fehlerhafter Begleitschein
    case FOREIGN_BEF_1 = 'FOREIGN_BEF.1'; // Signaturaufforderung Beförderer
    case FOREIGN_BEF_2 = 'FOREIGN_BEF.2'; // Signaturaufforderung Beförderer
    case FOREIGN_BEF_3 = 'FOREIGN_BEF.3'; // Signaturaufforderung Beförderer
    case FOREIGN_ENT = 'FOREIGN_ENT'; // Signaturaufforderung Entsorger
    case FOREIGN_ERZ = 'FOREIGN_ERZ'; // Signaturaufforderung Erzeuger
    case INWORK_BEF_1 = 'INWORK_BEF.1'; // Signaturaufforderung Beförderer
    case INWORK_BEF_2 = 'INWORK_BEF.2'; // Signaturaufforderung Beförderer
    case INWORK_BEF_3 = 'INWORK_BEF.3'; // Signaturaufforderung Beförderer
    case INWORK_ENT = 'INWORK_ENT'; // Signaturaufforderung Entsorger
    case INWORK_ERZ = 'INWORK_ERZ'; // Signaturaufforderung Erzeuger
    case RECEIVED_BEF_1 = 'RECEIVED_BEF.1'; // Signaturaufforderung Beförderer
    case RECEIVED_BEF_2 = 'RECEIVED_BEF.2'; // Signaturaufforderung Beförderer
    case RECEIVED_BEF_3 = 'RECEIVED_BEF.3'; // Signaturaufforderung Beförderer
    case RECEIVED_ENT = 'RECEIVED_ENT'; // Erledigter Begleitschein
    case RECEIVED_ERZ = 'RECEIVED_ERZ'; // Signaturaufforderung Erzeuger
    case RELEASED_BEF_1 = 'RELEASED_BEF.1'; // Signaturaufforderung Beförderer
    case RELEASED_BEF_2 = 'RELEASED_BEF.2'; // Signaturaufforderung Beförderer
    case RELEASED_BEF_3 = 'RELEASED_BEF.3'; // Signaturaufforderung Beförderer
    case RELEASED_ENT = 'RELEASED_ENT'; // Erledigter Begleitschein
    case RELEASED_ERZ = 'RELEASED_ERZ'; // Signaturaufforderung Erzeuger
    case SIGNED_BEF_1 = 'SIGNED_BEF.1'; // Signaturaufforderung Entsorger
    case SIGNED_BEF_2 = 'SIGNED_BEF.2'; // Signaturaufforderung Entsorger
    case SIGNED_BEF_3 = 'SIGNED_BEF.3'; // Signaturaufforderung Entsorger
    case SIGNED_ENT = 'SIGNED_ENT'; // Erledigter Begleitschein
    case SIGNED_ERZ = 'SIGNED_ERZ'; // Signaturaufforderung Beförderer

    public function getFrontendStatus(): ?string
    {
        return match ($this) {
            self::ERROR_BEF_1 => 'ERROR',
            self::ERROR_BEF_2 => 'ERROR',
            self::ERROR_BEF_3 => 'ERROR',
            self::ERROR_ENT => 'ERROR',
            self::ERROR_ERZ => 'ERROR',
            self::FOREIGN_BEF_1 => 'SIGN_REQUEST_BEF',
            self::FOREIGN_BEF_2 => 'SIGN_REQUEST_BEF',
            self::FOREIGN_BEF_3 => 'SIGN_REQUEST_BEF',
            self::FOREIGN_ENT => 'SIGN_REQUEST_ENT',
            self::FOREIGN_ERZ => 'SIGN_REQUEST_ERZ',
            self::INWORK_BEF_1 => 'SIGN_REQUEST_BEF',
            self::INWORK_BEF_2 => 'SIGN_REQUEST_BEF',
            self::INWORK_BEF_3 => 'SIGN_REQUEST_BEF',
            self::INWORK_ENT => 'SIGN_REQUEST_ENT',
            self::INWORK_ERZ => 'SIGN_REQUEST_ERZ',
            self::RECEIVED_BEF_1 => 'SIGN_REQUEST_BEF',
            self::RECEIVED_BEF_2 => 'SIGN_REQUEST_BEF',
            self::RECEIVED_BEF_3 => 'SIGN_REQUEST_BEF',
            self::RECEIVED_ENT => 'DONE',
            self::RECEIVED_ERZ => 'SIGN_REQUEST_ERZ',
            self::RELEASED_BEF_1 => 'SIGN_REQUEST_BEF',
            self::RELEASED_BEF_2 => 'SIGN_REQUEST_BEF',
            self::RELEASED_BEF_3 => 'SIGN_REQUEST_BEF',
            self::RELEASED_ENT => 'DONE',
            self::RELEASED_ERZ => 'SIGN_REQUEST_ERZ',
            self::SIGNED_BEF_1 => 'SIGN_REQUEST_ENT',
            self::SIGNED_BEF_2 => 'SIGN_REQUEST_ENT',
            self::SIGNED_BEF_3 => 'SIGN_REQUEST_ENT',
            self::SIGNED_ENT => 'DONE',
            self::SIGNED_ERZ => 'SIGN_REQUEST_BEF',
            default => null,
        };
    }
}
