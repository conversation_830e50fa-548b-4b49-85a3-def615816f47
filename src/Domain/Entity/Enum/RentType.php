<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum RentType: string
{
    case RENT_TYPE_02 = '02';
    case RENT_TYPE_03 = '03';
    case RENT_TYPE_04 = '04';
    case RENT_TYPE_06 = '06';
    case RENT_TYPE_07 = '07';
    case RENT_TYPE_08 = '08';
    case RENT_TYPE_09 = '09';
    case RENT_TYPE_10 = '10';
    case RENT_TYPE_11 = '11';
    case RENT_TYPE_12 = '12';
    case RENT_TYPE_13 = '13';
    case RENT_TYPE_14 = '14';
    case RENT_TYPE_15 = '15';
    case RENT_TYPE_16 = '16';
    case RENT_TYPE_17 = '17';
    case RENT_TYPE_18 = '18';
}
