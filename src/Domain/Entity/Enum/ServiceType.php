<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum ServiceType: string
{
    case SERVICE_TYPE_10 = '10';
    case SERVICE_TYPE_11 = '11';
    case SERVICE_TYPE_12 = '12';
    case SERVICE_TYPE_13 = '13';
    case SERVICE_TYPE_14 = '14';
    case SERVICE_TYPE_15 = '15';
    case SERVICE_TYPE_16 = '16';
    case SERVICE_TYPE_19 = '19';
    case SERVICE_TYPE_22 = '22';
    case SERVICE_TYPE_31 = '31';
    case SERVICE_TYPE_32 = '32';
    case SERVICE_TYPE_33 = '33';
    case SERVICE_TYPE_35 = '35';

    public static function getMaxContainerCount(self $serviceType): ?int
    {
        return match ($serviceType) {
            self::SERVICE_TYPE_10 => 3,
            self::SERVICE_TYPE_19, self::SERVICE_TYPE_33 => 99,
            default => null,
        };
    }
}
