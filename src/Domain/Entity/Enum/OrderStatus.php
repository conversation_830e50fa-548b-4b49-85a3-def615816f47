<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum OrderStatus: string
{
    case CREATED = '1';
    case DISPATCHED = '2';
    case DELIVERED = '3';
    case BILLED = '4';
    case CANCELED = '5';

    public function getFrontendStatus(): string
    {
        return match ($this) {
            self::CREATED => 'order_status_created',
            self::DISPATCHED => 'order_status_dispatched',
            self::DELIVERED => 'order_status_delivered',
            self::BILLED => 'order_status_billed',
            self::CANCELED => 'order_status_canceled',
        };
    }
}
