<?php

declare(strict_types=1);

namespace App\Domain\Entity\Enum;

enum Locale: string
{
    case de_DE = 'de_DE';
    case en_GB = 'en_GB';
    case nl_NL = 'nl_NL';
    case pl_PL = 'pl_PL';

    public function getCountryLabel(): string
    {
        return match ($this) {
            self::de_DE => 'Deutschland (deutsch)',
            self::en_GB => 'United Kingdom (english)',
            self::nl_NL => 'Netherlands (dutch)',
            self::pl_PL => 'Poland (polish)',
        };
    }
}
