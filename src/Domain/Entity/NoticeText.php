<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\NoticeTextRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: NoticeTextRepository::class)]
#[ORM\Table(name: 'notice_text')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['serviceId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extPosId', 'endDate', 'type'])]
#[UniqueEntity(fields: ['extId', 'extPosId', 'endDate', 'type'])]
class NoticeText implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $extId;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $extPosId;

    #[ORM\Column(type: Types::GUID)]
    private string $serviceId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $endDate;

    #[ORM\Column(length: 5)]
    private string $type;

    #[ORM\Column(length: 50)]
    private string $content;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): ?string
    {
        return $this->extId;
    }

    public function setExtId(?string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtPosId(): ?string
    {
        return $this->extPosId;
    }

    public function setExtPosId(?string $extPosId): void
    {
        $this->extPosId = $extPosId;
    }

    public function getServiceId(): string
    {
        return $this->serviceId;
    }

    public function setServiceId(string $serviceId): void
    {
        $this->serviceId = $serviceId;
    }

    public function getEndDate(): \DateTimeImmutable
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeImmutable $endDate): void
    {
        $this->endDate = $endDate;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function setContent(string $content): void
    {
        $this->content = $content;
    }
}
