<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ServiceProductRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ServiceProductRepository::class)]
#[ORM\Table(name: 'service_product')]
#[ORM\Index(fields: ['tenant'])]
class ServiceProduct implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 6)]
    private string $serviceProduct;

    #[ORM\Column(length: 8)]
    private string $container;

    #[ORM\Column(length: 50)]
    private string $serviceType;

    public function __construct()
    {
        $this->init();
    }

    public function getServiceProduct(): string
    {
        return $this->serviceProduct;
    }

    public function setServiceProduct(string $serviceProduct): void
    {
        $this->serviceProduct = $serviceProduct;
    }

    public function getContainer(): string
    {
        return $this->container;
    }

    public function setContainer(string $container): void
    {
        $this->container = $container;
    }

    public function getServiceType(): string
    {
        return $this->serviceType;
    }

    public function setServiceType(string $serviceType): void
    {
        $this->serviceType = $serviceType;
    }
}
