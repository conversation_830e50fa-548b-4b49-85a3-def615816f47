<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\BusinessPartnerTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ContactPersonRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ContactPersonRepository::class)]
#[ORM\Table(name: 'contact_person')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['extBusinessPartnerId'])]
#[ORM\Index(fields: ['extSalesOrganisationId'])]
#[ORM\Index(fields: ['businessPartnerId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extBusinessPartnerId', 'extSalesOrganisationId', 'role'])]
#[UniqueEntity(fields: ['extId', 'extBusinessPartnerId', 'extSalesOrganisationId', 'role'])]
class ContactPerson implements EntityInterface, HasTenant, HasBusinessPartner
{
    use CommonTrait;
    use TenantTrait;
    use BusinessPartnerTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    #[ORM\Column(length: 4)]
    private string $extSalesOrganisationId;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $function;

    #[ORM\Column(length: 2)]
    private string $role;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $title;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $lastName;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $firstName;

    #[ORM\Column(length: 3, nullable: true)]
    private ?string $country;

    #[ORM\Column(length: 241, nullable: true)]
    private ?string $email;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $telephone;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $mobilePhone;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }

    public function getExtSalesOrganisationId(): string
    {
        return $this->extSalesOrganisationId;
    }

    public function setExtSalesOrganisationId(string $extSalesOrganisationId): void
    {
        $this->extSalesOrganisationId = $extSalesOrganisationId;
    }

    public function getFunction(): ?string
    {
        return $this->function;
    }

    public function setFunction(?string $function): void
    {
        $this->function = $function;
    }

    public function getRole(): string
    {
        return $this->role;
    }

    public function setRole(string $role): void
    {
        $this->role = $role;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(?string $lastName): void
    {
        $this->lastName = $lastName;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName): void
    {
        $this->firstName = $firstName;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): void
    {
        $this->country = $country;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(?string $telephone): void
    {
        $this->telephone = $telephone;
    }

    public function getMobilePhone(): ?string
    {
        return $this->mobilePhone;
    }

    public function setMobilePhone(?string $mobilePhone): void
    {
        $this->mobilePhone = $mobilePhone;
    }
}
