<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ContractServiceLocationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ContractServiceLocationRepository::class)]
#[ORM\Table(name: 'contract_service_location')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['extContractId'])]
#[ORM\Index(fields: ['extServiceLocationId'])]
#[ORM\Index(fields: ['extBusinessPartnerId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extPosId', 'validTo'])]
#[UniqueEntity(fields: ['extId', 'extPosId', 'validTo'])]
class ContractServiceLocation implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 19)]
    private string $extId;

    #[ORM\Column(length: 10)]
    private string $extPosId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $validTo;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $validFrom;

    #[ORM\Column(length: 10)]
    private string $extContractId;

    #[ORM\Column(length: 6, nullable: true)]
    private ?string $extContractPosId;

    #[ORM\Column(length: 18)]
    private string $equipment;

    #[ORM\Column(length: 18)]
    private string $serialNumber;

    #[ORM\Column(length: 40)]
    private string $extContainerMaterialId;

    #[ORM\Column(length: 30)]
    private string $extServiceLocationId;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $timeto;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $timefr;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtPosId(): string
    {
        return $this->extPosId;
    }

    public function setExtPosId(string $extPosId): void
    {
        $this->extPosId = $extPosId;
    }

    public function getValidTo(): \DateTimeImmutable
    {
        return $this->validTo;
    }

    public function setValidTo(\DateTimeImmutable $validTo): void
    {
        $this->validTo = $validTo;
    }

    public function getValidFrom(): \DateTimeImmutable
    {
        return $this->validFrom;
    }

    public function setValidFrom(\DateTimeImmutable $validFrom): void
    {
        $this->validFrom = $validFrom;
    }

    public function getExtContractId(): string
    {
        return $this->extContractId;
    }

    public function setExtContractId(string $extContractId): void
    {
        $this->extContractId = $extContractId;
    }

    public function getExtContractPosId(): ?string
    {
        return $this->extContractPosId;
    }

    public function setExtContractPosId(?string $extContractPosId): void
    {
        $this->extContractPosId = $extContractPosId;
    }

    public function getEquipment(): string
    {
        return $this->equipment;
    }

    public function setEquipment(string $equipment): void
    {
        $this->equipment = $equipment;
    }

    public function getSerialNumber(): string
    {
        return $this->serialNumber;
    }

    public function setSerialNumber(string $serialNumber): void
    {
        $this->serialNumber = $serialNumber;
    }

    public function getExtContainerMaterialId(): string
    {
        return $this->extContainerMaterialId;
    }

    public function setExtContainerMaterialId(string $extContainerMaterialId): void
    {
        $this->extContainerMaterialId = $extContainerMaterialId;
    }

    public function getExtServiceLocationId(): string
    {
        return $this->extServiceLocationId;
    }

    public function setExtServiceLocationId(string $extServiceLocationId): void
    {
        $this->extServiceLocationId = $extServiceLocationId;
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }

    public function getTimeto(): ?\DateTimeImmutable
    {
        return $this->timeto;
    }

    public function setTimeto(?\DateTimeImmutable $timeto): void
    {
        $this->timeto = $timeto;
    }

    public function getTimefr(): ?\DateTimeImmutable
    {
        return $this->timefr;
    }

    public function setTimefr(?\DateTimeImmutable $timefr): void
    {
        $this->timefr = $timefr;
    }
}
