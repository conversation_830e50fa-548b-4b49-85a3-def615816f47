<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Division;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\BusinessPartnerTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\WasteEcoSavingsRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: WasteEcoSavingsRepository::class)]
#[ORM\Table(name: 'waste_eco_savings')]
#[ORM\Index(fields: ['businessPartnerId'])]
class WasteEcoSavings implements HasTenant, HasBusinessPartner
{
    use CommonTrait;
    use TenantTrait;
    use BusinessPartnerTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 1)]
    private string $division = Division::SPARTE_PREZERO->value;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    #[ORM\Column(length: 4)]
    private string $year;

    #[ORM\Column(length: 2)]
    private string $month;

    #[ORM\Column(length: 20)]
    private string $productHierarchy;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $co2Savings = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $co2EmissionsTransport = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $co2EmissionsRecycling = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $co2Emissions = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $totalWeight = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $invoiceAmount;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $billingAmount;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getDivision(): string
    {
        return $this->division;
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }

    public function getYear(): string
    {
        return $this->year;
    }

    public function setYear(string $year): void
    {
        $this->year = $year;
    }

    public function getMonth(): string
    {
        return $this->month;
    }

    public function setMonth(string $month): void
    {
        $this->month = $month;
    }

    public function getProductHierarchy(): string
    {
        return $this->productHierarchy;
    }

    public function setProductHierarchy(string $productHierarchy): void
    {
        $this->productHierarchy = $productHierarchy;
    }

    public function getCo2Savings(): ?string
    {
        return $this->co2Savings;
    }

    public function setCo2Savings(?string $co2Savings): void
    {
        $this->co2Savings = $co2Savings;
    }

    public function getCo2EmissionsTransport(): ?string
    {
        return $this->co2EmissionsTransport;
    }

    public function setCo2EmissionTransport(?string $co2EmissionsTransport): void
    {
        $this->co2EmissionsTransport = $co2EmissionsTransport;
    }

    public function getCo2EmissionsRecycling(): ?string
    {
        return $this->co2EmissionsRecycling;
    }

    public function setCo2EmissionsRecycling(?string $co2EmissionsRecycling): void
    {
        $this->co2EmissionsRecycling = $co2EmissionsRecycling;
    }

    public function getCo2Emissions(): ?string
    {
        return $this->co2Emissions;
    }

    public function setCo2Emissions(?string $co2Emissions): void
    {
        $this->co2Emissions = $co2Emissions;
    }

    public function getTotalWeight(): ?string
    {
        return $this->totalWeight;
    }

    public function setTotalWeight(?string $totalWeight): void
    {
        $this->totalWeight = $totalWeight;
    }

    public function getInvoiceAmount(): ?string
    {
        return $this->invoiceAmount;
    }

    public function setInvoiceAmount(?string $invoiceAmount): void
    {
        $this->invoiceAmount = $invoiceAmount;
    }

    public function getBillingAmount(): ?string
    {
        return $this->billingAmount;
    }

    public function setBillingAmount(?string $billingAmount): void
    {
        $this->billingAmount = $billingAmount;
    }
}
