<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\OrderArchiveDocumentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OrderArchiveDocumentRepository::class)]
#[ORM\Index(fields: ['orderId'])]
#[ORM\Index(fields: ['extDocumentId'])]
#[ORM\UniqueConstraint(fields: ['orderId', 'extDocumentId'])]
class OrderArchiveDocument implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(type: Types::GUID, nullable: false)]
    private string $orderId;

    #[ORM\Column(length: 40, nullable: false)]
    private string $extDocumentId;

    public function __construct()
    {
        $this->init();
    }

    public function getOrderId(): string
    {
        return $this->orderId;
    }

    public function setOrderId(string $orderId): void
    {
        $this->orderId = $orderId;
    }

    public function getExtDocumentId(): string
    {
        return $this->extDocumentId;
    }

    public function setExtDocumentId(string $extDocumentId): void
    {
        $this->extDocumentId = $extDocumentId;
    }
}
