<?php

declare(strict_types=1);

namespace App\Domain\Entity\Trait;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

trait BusinessPartnerTrait
{
    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $businessPartnerId = null;

    public function hasBusinessPartnerId(): bool
    {
        return isset($this->businessPartnerId);
    }

    public function getBusinessPartnerId(): ?string
    {
        return $this->businessPartnerId;
    }

    public function setBusinessPartnerId(?string $businessPartnerId): void
    {
        $this->businessPartnerId = $businessPartnerId;
    }
}
