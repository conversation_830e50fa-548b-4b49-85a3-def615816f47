<?php

declare(strict_types=1);

namespace App\Domain\Entity\Trait;

use App\Domain\Entity\Enum\Tenant;
use Doctrine\ORM\Mapping as ORM;

trait TenantTrait
{
    #[ORM\Column(length: 5, nullable: false)]
    private Tenant $tenant;

    public function getTenant(): Tenant
    {
        return $this->tenant;
    }

    public function hasTenant(): bool
    {
        return isset($this->tenant);
    }

    public function setTenant(Tenant $tenant): self
    {
        $this->tenant = $tenant;

        return $this;
    }
}
