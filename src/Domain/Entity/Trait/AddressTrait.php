<?php

declare(strict_types=1);

namespace App\Domain\Entity\Trait;

use Doctrine\ORM\Mapping as ORM;

trait AddressTrait
{
    #[ORM\Column(length: 60)]
    private string $street;

    #[ORM\Column(length: 10)]
    private string $houseNumber;

    #[ORM\Column(length: 10)]
    private string $postalCode;

    #[ORM\Column(length: 40)]
    private string $city;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $district = null;

    #[ORM\Column(length: 3)]
    private string $country;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $federalState = null;

    public function getStreet(): string
    {
        return $this->street;
    }

    public function setStreet(string $street): void
    {
        $this->street = $street;
    }

    public function getHouseNumber(): string
    {
        return $this->houseNumber;
    }

    public function setHouseNumber(string $houseNumber): void
    {
        $this->houseNumber = $houseNumber;
    }

    public function getPostalCode(): string
    {
        return $this->postalCode;
    }

    public function setPostalCode(string $postalCode): void
    {
        $this->postalCode = $postalCode;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function setCity(string $city): void
    {
        $this->city = $city;
    }

    public function getDistrict(): ?string
    {
        return $this->district;
    }

    public function setDistrict(?string $district): void
    {
        $this->district = $district;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country): void
    {
        $this->country = $country;
    }

    public function getFederalState(): ?string
    {
        return $this->federalState;
    }

    public function setFederalState(?string $federalState): void
    {
        $this->federalState = $federalState;
    }

    public function getAddress(): string
    {
        return sprintf('%s %s, %s %s',
            $this->street,
            $this->houseNumber,
            $this->postalCode,
            $this->city,
        );
    }
}
