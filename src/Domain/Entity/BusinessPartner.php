<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasAddress;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\AddressTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\BusinessPartnerRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: BusinessPartnerRepository::class)]
#[ORM\Table(name: 'business_partner')]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId'])]
#[UniqueEntity(fields: ['extId'])]
class BusinessPartner implements EntityInterface, HasTenant, HasAddress
{
    use CommonTrait;
    use TenantTrait;
    use AddressTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 1)]
    private string $type;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $industryCode;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $title;

    #[ORM\Column(length: 120)]
    private string $name;

    #[ORM\Column(length: 241, nullable: true)]
    private ?string $email;

    #[ORM\Column(length: 30)]
    private string $telephone;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $mobilePhone;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $taxNumber;

    #[ORM\Column(nullable: true)]
    private ?bool $deleted;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $orderBlock;

    /**
     * @var Collection<int, User>
     */
    #[ORM\ManyToMany(targetEntity: User::class, mappedBy: 'businessPartners', cascade: ['persist'])]
    private Collection $users;

    /**
     * @var Collection<int, Group>
     */
    #[ORM\OneToMany(targetEntity: Group::class, mappedBy: 'businessPartner', cascade: ['persist'])]
    private Collection $groups;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
        $this->users = new ArrayCollection();
        $this->groups = new ArrayCollection();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getIndustryCode(): ?string
    {
        return $this->industryCode;
    }

    public function setIndustryCode(?string $industryCode): void
    {
        $this->industryCode = $industryCode;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function getTelephone(): string
    {
        return $this->telephone;
    }

    public function setTelephone(string $telephone): void
    {
        $this->telephone = $telephone;
    }

    public function getMobilePhone(): ?string
    {
        return $this->mobilePhone;
    }

    public function setMobilePhone(?string $mobilePhone): void
    {
        $this->mobilePhone = $mobilePhone;
    }

    public function getTaxNumber(): ?string
    {
        return $this->taxNumber;
    }

    public function setTaxNumber(?string $taxNumber): void
    {
        $this->taxNumber = $taxNumber;
    }

    public function isDeleted(): ?bool
    {
        return $this->deleted;
    }

    public function setDeleted(?bool $deleted): void
    {
        $this->deleted = $deleted;
    }

    public function getOrderBlock(): ?string
    {
        return $this->orderBlock;
    }

    public function setOrderBlock(?string $orderBlock): void
    {
        $this->orderBlock = $orderBlock;
    }

    /**
     * @return Collection<int, Group>
     */
    public function getGroups(): Collection
    {
        return $this->groups;
    }

    /**
     * @return Collection<int, User>
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(User $user): void
    {
        if (!$this->users->contains($user)) {
            $this->users->add($user);
            $user->addBusinessPartner(businessPartner: $this);
        }
    }

    public function removeUser(User $user): void
    {
        if ($this->users->contains($user)) {
            $this->users->removeElement($user);
            $user->removeBusinessPartner(businessPartner: $this);
        }
    }

    public function clearUsers(): void
    {
        foreach ($this->users as $user) {
            $this->removeUser(user: $user);
        }
    }
}
