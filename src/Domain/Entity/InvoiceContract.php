<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\InvoiceContractRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: InvoiceContractRepository::class)]
#[ORM\Table(name: 'invoice_contract')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['invoiceId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extInvoiceId'])]
#[UniqueEntity(fields: ['extId', 'extInvoiceId'])]
class InvoiceContract implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 10)]
    private string $extInvoiceId;

    #[ORM\Column(type: Types::GUID)]
    private string $invoiceId;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtInvoiceId(): string
    {
        return $this->extInvoiceId;
    }

    public function setExtInvoiceId(string $extInvoiceId): void
    {
        $this->extInvoiceId = $extInvoiceId;
    }

    public function getInvoiceId(): string
    {
        return $this->invoiceId;
    }

    public function setInvoiceId(string $invoiceId): void
    {
        $this->invoiceId = $invoiceId;
    }
}
