<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\WasteStatisticRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: WasteStatisticRepository::class)]
#[ORM\Table(name: 'waste_statistic')]
#[ORM\Index(fields: ['extBusinessPartnerId'])]
#[ORM\Index(fields: ['extContractId'])]
#[ORM\Index(fields: ['extWasteStatisticLocationId'])]
#[ORM\Index(fields: ['wasteStatisticLocationId'])]
#[ORM\Index(fields: ['tenant'])]
#[UniqueEntity(fields: ['extBusinessPartnerId', 'extContractId', 'extWasteStatisticLocationId'])]
class WasteStatistic implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $extContractId;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $extWasteStatisticLocationId;

    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $wasteStatisticLocationId;

    #[ORM\Column(length: 250)]
    private string $avvText;

    #[ORM\Column(length: 10)]
    private string $avvId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $orderDate;

    #[ORM\Column(length: 250)]
    private string $materialText;

    #[ORM\Column(length: 10)]
    private string $extMaterialId;

    #[ORM\Column(length: 10)]
    private float $amount;

    #[ORM\Column(length: 3)]
    private string $unitOm;

    #[ORM\Column(length: 10)]
    private float $net;

    #[ORM\Column(length: 5)]
    private string $currency;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }

    public function getExtContractId(): ?string
    {
        return $this->extContractId;
    }

    public function setExtContractId(?string $extContractId): void
    {
        $this->extContractId = $extContractId;
    }

    public function getExtWasteStatisticLocationId(): ?string
    {
        return $this->extWasteStatisticLocationId;
    }

    public function setExtWasteStatisticLocationId(?string $extWasteStatisticLocationId): void
    {
        $this->extWasteStatisticLocationId = $extWasteStatisticLocationId;
    }

    public function getWasteStatisticLocationId(): ?string
    {
        return $this->wasteStatisticLocationId;
    }

    public function setWasteStatisticLocationId(?string $wasteStatisticLocationId): void
    {
        $this->wasteStatisticLocationId = $wasteStatisticLocationId;
    }

    public function getAvvText(): string
    {
        return $this->avvText;
    }

    public function setAvvText(string $avvText): void
    {
        $this->avvText = $avvText;
    }

    public function getAvvId(): string
    {
        return $this->avvId;
    }

    public function setAvvId(string $avvId): void
    {
        $this->avvId = $avvId;
    }

    public function getOrderDate(): \DateTimeImmutable
    {
        return $this->orderDate;
    }

    public function setOrderDate(\DateTimeImmutable $orderDate): void
    {
        $this->orderDate = $orderDate;
    }

    public function getMaterialText(): string
    {
        return $this->materialText;
    }

    public function setMaterialText(string $materialText): void
    {
        $this->materialText = $materialText;
    }

    public function getExtMaterialId(): string
    {
        return $this->extMaterialId;
    }

    public function setExtMaterialId(string $extMaterialId): void
    {
        $this->extMaterialId = $extMaterialId;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): void
    {
        $this->amount = $amount;
    }

    public function getUnitOm(): string
    {
        return $this->unitOm;
    }

    public function setUnitOm(string $unitOm): void
    {
        $this->unitOm = $unitOm;
    }

    public function getNet(): float
    {
        return $this->net;
    }

    public function setNet(float $net): void
    {
        $this->net = $net;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): void
    {
        $this->currency = $currency;
    }
}
