<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\GroupRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: GroupRepository::class)]
#[ORM\Table(name: '`group`')]
class Group implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column]
    private string $name;

    /**
     * @var Collection<int, User>
     */
    #[ORM\ManyToMany(targetEntity: User::class, inversedBy: 'groups', cascade: ['persist'])]
    #[ORM\JoinTable(name: 'user_group')]
    private Collection $users;

    /**
     * @var Collection<int, ServiceLocation>
     */
    #[ORM\ManyToMany(targetEntity: ServiceLocation::class, cascade: ['persist'])]
    #[ORM\JoinTable(
        name: 'service_location_group',
        joinColumns: [new ORM\JoinColumn(name: 'group_id', referencedColumnName: 'id', nullable: false)],
        inverseJoinColumns: [new ORM\JoinColumn(name: 'service_location_id', referencedColumnName: 'id', nullable: false)]
    )]
    private Collection $serviceLocations;

    #[ORM\ManyToOne(targetEntity: BusinessPartner::class, inversedBy: 'groups')]
    private BusinessPartner $businessPartner;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
        $this->users = new ArrayCollection();
        $this->serviceLocations = new ArrayCollection();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return Collection<int, User>
     */
    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function addUser(User $user): void
    {
        if (!$this->users->contains($user)) {
            $this->users->add($user);
            $user->addGroup(group: $this);
        }
    }

    public function removeUser(User $user): void
    {
        if ($this->users->contains($user)) {
            $this->users->removeElement($user);
            $user->removeGroup(group: $this);
        }
    }

    public function clearUsers(): void
    {
        foreach ($this->users as $user) {
            $this->removeUser(user: $user);
        }
    }

    /**
     * @return Collection<int, ServiceLocation>
     */
    public function getServiceLocations(): Collection
    {
        return $this->serviceLocations;
    }

    public function addServiceLocation(ServiceLocation $serviceLocation): void
    {
        if (!$this->serviceLocations->contains($serviceLocation)) {
            $this->serviceLocations->add($serviceLocation);
            $serviceLocation->addGroup(group: $this);
        }
    }

    public function removeServiceLocation(ServiceLocation $serviceLocation): void
    {
        if ($this->serviceLocations->contains($serviceLocation)) {
            $this->serviceLocations->removeElement($serviceLocation);
            $serviceLocation->removeGroup(group: $this);
        }
    }

    public function clearServiceLocations(): void
    {
        foreach ($this->serviceLocations as $serviceLocation) {
            $this->removeServiceLocation(serviceLocation: $serviceLocation);
        }
    }

    public function getBusinessPartner(): BusinessPartner
    {
        return $this->businessPartner;
    }

    public function setBusinessPartner(BusinessPartner $businessPartner): void
    {
        $this->businessPartner = $businessPartner;
    }
}
