<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\SupervisionDocumentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: SupervisionDocumentRepository::class)]
#[ORM\Table(name: 'supervision_document')]
#[ORM\Index(fields: ['extOrderId'])]
#[ORM\Index(fields: ['extSalesOrganisationId'])]
#[ORM\Index(fields: ['extVerificationDocumentId'])]
#[ORM\Index(fields: ['certificateNumber'])]
#[ORM\Index(fields: ['extOrderObjectId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId'])]
#[ORM\UniqueConstraint(fields: ['extDocId'])]
#[UniqueEntity(fields: ['extId'])]
#[UniqueEntity(fields: ['extDocId'])]
class SupervisionDocument implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 15)]
    private string $extId;

    #[ORM\Column(length: 50)]
    private string $extDocId;

    #[ORM\Column(length: 10)]
    private string $extVerificationDocumentId;

    #[ORM\Column(length: 2)]
    private string $extVerificationDocumentVersion;

    #[ORM\Column(length: 20)]
    private string $certificateNumber;

    #[ORM\Column(length: 10)]
    private string $extRouteId;

    #[ORM\Column(length: 254)]
    private string $description;

    #[ORM\Column(length: 6)]
    private string $avvId;

    #[ORM\Column(length: 255)]
    private string $avvDescription;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $extWasteMaterialId;

    #[ORM\Column]
    private float $amount;

    #[ORM\Column(length: 3)]
    private string $amountUnitOm;

    #[ORM\Column(length: 22)]
    private string $extOrderObjectId;

    #[ORM\Column(length: 20)]
    private string $extOrderId;

    #[ORM\Column(length: 4)]
    private string $extOrderPosId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $orderDate;

    #[ORM\Column(length: 4)]
    private string $extSalesOrganisationId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $createdDate;

    #[ORM\Column(length: 6)]
    private string $role;

    #[ORM\Column(length: 30)]
    private string $status;

    #[ORM\Column(length: 6, nullable: true)]
    private ?string $dataStatus;

    #[ORM\Column(nullable: true)]
    private ?string $value;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $mimeType;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtDocId(): string
    {
        return $this->extDocId;
    }

    public function setExtDocId(string $extDocId): void
    {
        $this->extDocId = $extDocId;
    }

    public function getExtVerificationDocumentId(): string
    {
        return $this->extVerificationDocumentId;
    }

    public function setExtVerificationDocumentId(string $extVerificationDocumentId): void
    {
        $this->extVerificationDocumentId = $extVerificationDocumentId;
    }

    public function getExtVerificationDocumentVersion(): string
    {
        return $this->extVerificationDocumentVersion;
    }

    public function setExtVerificationDocumentVersion(string $extVerificationDocumentVersion): void
    {
        $this->extVerificationDocumentVersion = $extVerificationDocumentVersion;
    }

    public function getCertificateNumber(): string
    {
        return $this->certificateNumber;
    }

    public function setCertificateNumber(string $certificateNumber): void
    {
        $this->certificateNumber = $certificateNumber;
    }

    public function getExtRouteId(): string
    {
        return $this->extRouteId;
    }

    public function setExtRouteId(string $extRouteId): void
    {
        $this->extRouteId = $extRouteId;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getAvvId(): string
    {
        return $this->avvId;
    }

    public function setAvvId(string $avvId): void
    {
        $this->avvId = $avvId;
    }

    public function getAvvDescription(): string
    {
        return $this->avvDescription;
    }

    public function setAvvDescription(string $avvDescription): void
    {
        $this->avvDescription = $avvDescription;
    }

    public function getExtWasteMaterialId(): ?string
    {
        return $this->extWasteMaterialId;
    }

    public function setExtWasteMaterialId(?string $extWasteMaterialId): void
    {
        $this->extWasteMaterialId = $extWasteMaterialId;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): void
    {
        $this->amount = $amount;
    }

    public function getAmountUnitOm(): string
    {
        return $this->amountUnitOm;
    }

    public function setAmountUnitOm(string $amountUnitOm): void
    {
        $this->amountUnitOm = $amountUnitOm;
    }

    public function getExtOrderObjectId(): string
    {
        return $this->extOrderObjectId;
    }

    public function setExtOrderObjectId(string $extOrderObjectId): void
    {
        $this->extOrderObjectId = $extOrderObjectId;
    }

    public function getExtOrderId(): string
    {
        return $this->extOrderId;
    }

    public function setExtOrderId(string $extOrderId): void
    {
        $this->extOrderId = $extOrderId;
    }

    public function getExtOrderPosId(): string
    {
        return $this->extOrderPosId;
    }

    public function setExtOrderPosId(string $extOrderPosId): void
    {
        $this->extOrderPosId = $extOrderPosId;
    }

    public function getOrderDate(): \DateTimeImmutable
    {
        return $this->orderDate;
    }

    public function setOrderDate(\DateTimeImmutable $orderDate): void
    {
        $this->orderDate = $orderDate;
    }

    public function getExtSalesOrganisationId(): string
    {
        return $this->extSalesOrganisationId;
    }

    public function setExtSalesOrganisationId(string $extSalesOrganisationId): void
    {
        $this->extSalesOrganisationId = $extSalesOrganisationId;
    }

    public function getCreatedDate(): \DateTimeImmutable
    {
        return $this->createdDate;
    }

    public function setCreatedDate(\DateTimeImmutable $createdDate): void
    {
        $this->createdDate = $createdDate;
    }

    public function getRole(): string
    {
        return $this->role;
    }

    public function setRole(string $role): void
    {
        $this->role = $role;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function getDataStatus(): ?string
    {
        return $this->dataStatus;
    }

    public function setDataStatus(?string $dataStatus): void
    {
        $this->dataStatus = $dataStatus;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(?string $value): void
    {
        $this->value = $value;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(?string $mimeType): void
    {
        $this->mimeType = $mimeType;
    }
}
