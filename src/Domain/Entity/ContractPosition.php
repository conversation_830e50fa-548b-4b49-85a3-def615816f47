<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ContractPositionRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ContractPositionRepository::class)]
#[ORM\Table(name: 'contract_position')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['contractId', 'tenant', 'extMaterialId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extPosId'])]
#[UniqueEntity(fields: ['extId', 'extPosId'])]
class ContractPosition implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 6)]
    private string $extPosId;

    #[ORM\Column(length: 6)]
    private string $extParentPosId;

    #[ORM\Column(type: Types::GUID)]
    private string $contractId;

    #[ORM\Column]
    private float $quantity;

    #[ORM\Column(length: 3)]
    private string $unitOm;

    #[ORM\Column(nullable: true)]
    private ?float $netValue;

    #[ORM\Column(length: 5)]
    private string $currency;

    #[ORM\Column(length: 3, nullable: true)]
    private ?string $priceIndex;

    #[ORM\Column(length: 4)]
    private string $materialType;

    #[ORM\Column(length: 40)]
    private string $extMaterialId;

    #[ORM\Column(length: 4)]
    private string $materialGroup;

    #[ORM\Column(length: 200)]
    private string $materialText;

    #[ORM\Column(length: 20)]
    private string $extWasteDisposalSiteId;

    #[ORM\Column(length: 254)]
    private string $wasteDisposalSiteDescription;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $rentType;

    #[ORM\Column(length: 12, nullable: true)]
    private ?string $certificateNumber;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $certificateType;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $configName;

    #[ORM\Column(length: 70, nullable: true)]
    private ?string $configValue;

    #[ORM\Column]
    private bool $display;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtPosId(): string
    {
        return $this->extPosId;
    }

    public function setExtPosId(string $extPosId): void
    {
        $this->extPosId = $extPosId;
    }

    public function getExtParentPosId(): string
    {
        return $this->extParentPosId;
    }

    public function setExtParentPosId(string $extParentPosId): void
    {
        $this->extParentPosId = $extParentPosId;
    }

    public function getContractId(): string
    {
        return $this->contractId;
    }

    public function setContractId(string $contractId): void
    {
        $this->contractId = $contractId;
    }

    public function getQuantity(): float
    {
        return $this->quantity;
    }

    public function setQuantity(float $quantity): void
    {
        $this->quantity = $quantity;
    }

    public function getUnitOm(): string
    {
        return $this->unitOm;
    }

    public function setUnitOm(string $unitOm): void
    {
        $this->unitOm = $unitOm;
    }

    public function getNetValue(): ?float
    {
        return $this->netValue;
    }

    public function setNetValue(?float $netValue): void
    {
        $this->netValue = $netValue;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): void
    {
        $this->currency = $currency;
    }

    public function getPriceIndex(): ?string
    {
        return $this->priceIndex;
    }

    public function setPriceIndex(?string $priceIndex): void
    {
        $this->priceIndex = $priceIndex;
    }

    public function getMaterialType(): string
    {
        return $this->materialType;
    }

    public function setMaterialType(string $materialType): void
    {
        $this->materialType = $materialType;
    }

    public function getExtMaterialId(): string
    {
        return $this->extMaterialId;
    }

    public function setExtMaterialId(string $extMaterialId): void
    {
        $this->extMaterialId = $extMaterialId;
    }

    public function getMaterialGroup(): string
    {
        return $this->materialGroup;
    }

    public function setMaterialGroup(string $materialGroup): void
    {
        $this->materialGroup = $materialGroup;
    }

    public function getMaterialText(): string
    {
        return $this->materialText;
    }

    public function setMaterialText(string $materialText): void
    {
        $this->materialText = $materialText;
    }

    public function getExtWasteDisposalSiteId(): string
    {
        return $this->extWasteDisposalSiteId;
    }

    public function setExtWasteDisposalSiteId(string $extWasteDisposalSiteId): void
    {
        $this->extWasteDisposalSiteId = $extWasteDisposalSiteId;
    }

    public function getWasteDisposalSiteDescription(): string
    {
        return $this->wasteDisposalSiteDescription;
    }

    public function setWasteDisposalSiteDescription(string $wasteDisposalSiteDescription): void
    {
        $this->wasteDisposalSiteDescription = $wasteDisposalSiteDescription;
    }

    public function getRentType(): ?string
    {
        return $this->rentType;
    }

    public function setRentType(?string $rentType): void
    {
        $this->rentType = $rentType;
    }

    public function getCertificateNumber(): ?string
    {
        return $this->certificateNumber;
    }

    public function setCertificateNumber(?string $certificateNumber): void
    {
        $this->certificateNumber = $certificateNumber;
    }

    public function getCertificateType(): ?string
    {
        return $this->certificateType;
    }

    public function setCertificateType(?string $certificateType): void
    {
        $this->certificateType = $certificateType;
    }

    public function getConfigName(): ?string
    {
        return $this->configName;
    }

    public function setConfigName(?string $configName): void
    {
        $this->configName = $configName;
    }

    public function getConfigValue(): ?string
    {
        return $this->configValue;
    }

    public function setConfigValue(?string $configValue): void
    {
        $this->configValue = $configValue;
    }

    public function isDisplay(): bool
    {
        return $this->display;
    }

    public function setDisplay(bool $display): void
    {
        $this->display = $display;
    }
}
