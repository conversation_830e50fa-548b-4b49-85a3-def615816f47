<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\BusinessPartnerTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\OrderRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: OrderRepository::class)]
#[ORM\Table(name: '`order`')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['extServiceId'])]
#[ORM\Index(fields: ['businessPartnerId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extObjectId'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extPosId'])]
#[UniqueEntity(fields: ['extObjectId'])]
#[UniqueEntity(fields: ['extId', 'extPosId'])]
class Order implements EntityInterface, HasTenant, HasBusinessPartner
{
    use CommonTrait;
    use TenantTrait;
    use BusinessPartnerTrait;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $extId;

    #[ORM\Column(length: 4, nullable: true)]
    private ?string $extPosId;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $extServiceId;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $extServicePosId;

    #[ORM\Column(length: 22, nullable: true)]
    private ?string $extObjectId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $orderDate;

    #[ORM\Column(length: 1)]
    private string $orderStatus;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $certificateNumberInternal;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $certificateNumber;

    #[ORM\Column(length: 15, nullable: true)]
    private ?string $disposalDocumentUns;

    #[ORM\Column(length: 15, nullable: true)]
    private ?string $disposalDocumentBgs;

    #[ORM\Column(length: 241, nullable: true)]
    private ?string $email;

    /**
     * @var Collection<int, OrderActivity>
     */
    #[ORM\OneToMany(targetEntity: OrderActivity::class, mappedBy: 'order', cascade: ['persist', 'remove'])]
    private Collection $activities;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
        $this->activities = new ArrayCollection();
    }

    public function getExtId(): ?string
    {
        return $this->extId;
    }

    public function setExtId(?string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtPosId(): ?string
    {
        return $this->extPosId;
    }

    public function setExtPosId(?string $extPosId): void
    {
        $this->extPosId = $extPosId;
    }

    public function getExtServiceId(): ?string
    {
        return $this->extServiceId;
    }

    public function setExtServiceId(?string $extServiceId): void
    {
        $this->extServiceId = $extServiceId;
    }

    public function getExtServicePosId(): ?string
    {
        return $this->extServicePosId;
    }

    public function setExtServicePosId(?string $extServicePosId): void
    {
        $this->extServicePosId = $extServicePosId;
    }

    public function getExtObjectId(): ?string
    {
        return $this->extObjectId;
    }

    public function setExtObjectId(?string $extObjectId): void
    {
        $this->extObjectId = $extObjectId;
    }

    public function getOrderDate(): \DateTimeImmutable
    {
        return $this->orderDate;
    }

    public function setOrderDate(\DateTimeImmutable $orderDate): void
    {
        $this->orderDate = $orderDate;
    }

    public function getOrderStatus(): string
    {
        return $this->orderStatus;
    }

    public function setOrderStatus(string $orderStatus): void
    {
        $this->orderStatus = $orderStatus;
    }

    public function getCertificateNumberInternal(): ?string
    {
        return $this->certificateNumberInternal;
    }

    public function setCertificateNumberInternal(?string $certificateNumberInternal): void
    {
        $this->certificateNumberInternal = $certificateNumberInternal;
    }

    public function getCertificateNumber(): ?string
    {
        return $this->certificateNumber;
    }

    public function setCertificateNumber(?string $certificateNumber): void
    {
        $this->certificateNumber = $certificateNumber;
    }

    public function getDisposalDocumentUns(): ?string
    {
        return $this->disposalDocumentUns;
    }

    public function setDisposalDocumentUns(?string $disposalDocumentUns): void
    {
        $this->disposalDocumentUns = $disposalDocumentUns;
    }

    public function getDisposalDocumentBgs(): ?string
    {
        return $this->disposalDocumentBgs;
    }

    public function setDisposalDocumentBgs(?string $disposalDocumentBgs): void
    {
        $this->disposalDocumentBgs = $disposalDocumentBgs;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    /**
     * @return Collection<int, OrderActivity>
     */
    public function getActivities(): Collection
    {
        return $this->activities;
    }

    public function addActivity(OrderActivity $activity): void
    {
        if (!$this->activities->contains($activity)) {
            $this->activities->add($activity);
        }
    }
}
