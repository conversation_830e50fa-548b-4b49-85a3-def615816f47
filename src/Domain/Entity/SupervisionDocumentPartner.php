<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasAddress;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\AddressTrait;
use App\Domain\Entity\Trait\BusinessPartnerTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\SupervisionDocumentPartnerRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: SupervisionDocumentPartnerRepository::class)]
#[ORM\Table(name: 'supervision_document_partner')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['extSupervisionDocumentId'])]
#[ORM\Index(fields: ['supervisionDocumentId'])]
#[ORM\Index(fields: ['businessPartnerId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extCheckId', 'type', 'extSupervisionDocumentId'])]
#[UniqueEntity(fields: ['extId', 'extCheckId', 'type', 'extSupervisionDocumentId'])]
class SupervisionDocumentPartner implements EntityInterface, HasTenant, HasAddress, HasBusinessPartner
{
    use CommonTrait;
    use TenantTrait;
    use AddressTrait;
    use BusinessPartnerTrait;

    #[ORM\Column(length: 50)]
    private string $extDocId;

    #[ORM\Column(length: 9)]
    private string $extId;

    #[ORM\Column(length: 1)]
    private string $extCheckId;

    #[ORM\Column(length: 5)]
    private string $type;

    #[ORM\Column(length: 20)]
    private string $layername;

    #[ORM\Column(length: 15)]
    private string $extSupervisionDocumentId;

    #[ORM\Column(type: Types::GUID)]
    private string $supervisionDocumentId;

    #[ORM\Column(length: 70)]
    private string $name;

    #[ORM\Column(length: 35, nullable: true)]
    private ?string $contactPerson;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $telephone;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $fax;

    #[ORM\Column(length: 241, nullable: true)]
    private ?string $email;

    #[ORM\Column(length: 15, nullable: true)]
    private ?string $licencePlateNumber1;

    #[ORM\Column(length: 15, nullable: true)]
    private ?string $licencePlateNumber2;

    #[ORM\Column]
    private float $amount;

    #[ORM\Column(length: 3)]
    private string $amountUnitOm;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $takeoverDate;

    #[ORM\Column(nullable: true)]
    private ?bool $receiptFlag;

    #[ORM\Column(nullable: true)]
    private ?bool $roleAllowed;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtDocId(): string
    {
        return $this->extDocId;
    }

    public function setExtDocId(string $extDocId): void
    {
        $this->extDocId = $extDocId;
    }

    public function getExtCheckId(): string
    {
        return $this->extCheckId;
    }

    public function setExtCheckId(string $extCheckId): void
    {
        $this->extCheckId = $extCheckId;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getLayername(): string
    {
        return $this->layername;
    }

    public function setLayername(string $layername): void
    {
        $this->layername = $layername;
    }

    public function getExtSupervisionDocumentId(): string
    {
        return $this->extSupervisionDocumentId;
    }

    public function setExtSupervisionDocumentId(string $extSupervisionDocumentId): void
    {
        $this->extSupervisionDocumentId = $extSupervisionDocumentId;
    }

    public function getSupervisionDocumentId(): string
    {
        return $this->supervisionDocumentId;
    }

    public function setSupervisionDocumentId(string $supervisionDocumentId): void
    {
        $this->supervisionDocumentId = $supervisionDocumentId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getContactPerson(): ?string
    {
        return $this->contactPerson;
    }

    public function setContactPerson(?string $contactPerson): void
    {
        $this->contactPerson = $contactPerson;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(?string $telephone): void
    {
        $this->telephone = $telephone;
    }

    public function getFax(): ?string
    {
        return $this->fax;
    }

    public function setFax(?string $fax): void
    {
        $this->fax = $fax;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): void
    {
        $this->email = $email;
    }

    public function getLicencePlateNumber1(): ?string
    {
        return $this->licencePlateNumber1;
    }

    public function setLicencePlateNumber1(?string $licencePlateNumber1): void
    {
        $this->licencePlateNumber1 = $licencePlateNumber1;
    }

    public function getLicencePlateNumber2(): ?string
    {
        return $this->licencePlateNumber2;
    }

    public function setLicencePlateNumber2(?string $licencePlateNumber2): void
    {
        $this->licencePlateNumber2 = $licencePlateNumber2;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function setAmount(float $amount): void
    {
        $this->amount = $amount;
    }

    public function getAmountUnitOm(): string
    {
        return $this->amountUnitOm;
    }

    public function setAmountUnitOm(string $amountUnitOm): void
    {
        $this->amountUnitOm = $amountUnitOm;
    }

    public function getTakeoverDate(): \DateTimeImmutable
    {
        return $this->takeoverDate;
    }

    public function setTakeoverDate(\DateTimeImmutable $takeoverDate): void
    {
        $this->takeoverDate = $takeoverDate;
    }

    public function isReceiptFlag(): ?bool
    {
        return $this->receiptFlag;
    }

    public function setReceiptFlag(?bool $receiptFlag): void
    {
        $this->receiptFlag = $receiptFlag;
    }

    public function isRoleAllowed(): ?bool
    {
        return $this->roleAllowed;
    }

    public function setRoleAllowed(?bool $roleAllowed): void
    {
        $this->roleAllowed = $roleAllowed;
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }
}
