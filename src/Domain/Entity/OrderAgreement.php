<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\OrderAgreementRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OrderAgreementRepository::class)]
#[ORM\Table(name: 'order_agreement')]
#[ORM\Index(fields: ['tenant'])]
class OrderAgreement implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 30)]
    private string $name;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $serviceType;

    #[ORM\Column(nullable: true)]
    private ?bool $required;

    public function __construct()
    {
        $this->init();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getServiceType(): ?string
    {
        return $this->serviceType;
    }

    public function setServiceType(?string $serviceType): void
    {
        $this->serviceType = $serviceType;
    }

    public function getRequired(): ?bool
    {
        return $this->required;
    }

    public function setRequired(?bool $required): void
    {
        $this->required = $required;
    }
}
