<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\InvoiceArchiveDocumentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: InvoiceArchiveDocumentRepository::class)]
#[ORM\Index(fields: ['extDocumentId'])]
#[ORM\Index(fields: ['invoiceId'])]
#[ORM\UniqueConstraint(fields: ['invoiceId', 'extDocumentId'])]
class InvoiceArchiveDocument implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(type: Types::GUID, nullable: false)]
    private string $invoiceId;

    #[ORM\Column(length: 40, nullable: false)]
    private string $extDocumentId;

    public function __construct()
    {
        $this->init();
    }

    public function getInvoiceId(): string
    {
        return $this->invoiceId;
    }

    public function setInvoiceId(string $invoiceId): void
    {
        $this->invoiceId = $invoiceId;
    }

    public function getExtDocumentId(): string
    {
        return $this->extDocumentId;
    }

    public function setExtDocumentId(string $extDocumentId): void
    {
        $this->extDocumentId = $extDocumentId;
    }
}
