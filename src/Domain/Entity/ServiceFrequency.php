<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ServiceFrequencyRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ServiceFrequencyRepository::class)]
#[ORM\Table(name: 'service_frequency')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['routeId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extPosId'])]
#[UniqueEntity(fields: ['extId', 'extPosId'])]
class ServiceFrequency implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 5)]
    private string $extPosId;

    #[ORM\Column(type: Types::GUID)]
    private string $routeId;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $startDate;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $endDate;

    #[ORM\Column(length: 2)]
    private string $serviceType;

    #[ORM\Column(length: 1)]
    private string $serviceFrequency;

    #[ORM\Column(length: 1, nullable: true)]
    private ?string $dailyFrequency;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $weeklyFrequency;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $monthlyFrequency;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $monthlyFrequencyDay;

    #[ORM\Column(length: 1, nullable: true)]
    private ?string $weekday;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtPosId(): string
    {
        return $this->extPosId;
    }

    public function setExtPosId(string $extPosId): void
    {
        $this->extPosId = $extPosId;
    }

    public function getRouteId(): string
    {
        return $this->routeId;
    }

    public function setRouteId(string $routeId): void
    {
        $this->routeId = $routeId;
    }

    public function getStartDate(): \DateTimeImmutable
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeImmutable $startDate): void
    {
        $this->startDate = $startDate;
    }

    public function getEndDate(): \DateTimeImmutable
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeImmutable $endDate): void
    {
        $this->endDate = $endDate;
    }

    public function getServiceType(): string
    {
        return $this->serviceType;
    }

    public function setServiceType(string $serviceType): void
    {
        $this->serviceType = $serviceType;
    }

    public function getServiceFrequency(): string
    {
        return $this->serviceFrequency;
    }

    public function setServiceFrequency(string $serviceFrequency): void
    {
        $this->serviceFrequency = $serviceFrequency;
    }

    public function getDailyFrequency(): ?string
    {
        return $this->dailyFrequency;
    }

    public function setDailyFrequency(?string $dailyFrequency): void
    {
        $this->dailyFrequency = $dailyFrequency;
    }

    public function getWeeklyFrequency(): ?string
    {
        return $this->weeklyFrequency;
    }

    public function setWeeklyFrequency(?string $weeklyFrequency): void
    {
        $this->weeklyFrequency = $weeklyFrequency;
    }

    public function getMonthlyFrequency(): ?string
    {
        return $this->monthlyFrequency;
    }

    public function setMonthlyFrequency(?string $monthlyFrequency): void
    {
        $this->monthlyFrequency = $monthlyFrequency;
    }

    public function getMonthlyFrequencyDay(): ?string
    {
        return $this->monthlyFrequencyDay;
    }

    public function setMonthlyFrequencyDay(?string $monthlyFrequencyDay): void
    {
        $this->monthlyFrequencyDay = $monthlyFrequencyDay;
    }

    public function getWeekday(): ?string
    {
        return $this->weekday;
    }

    public function setWeekday(?string $weekday): void
    {
        $this->weekday = $weekday;
    }
}
