<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\BusinessPartnerTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\IdentificationElementRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: IdentificationElementRepository::class)]
#[ORM\Table(name: 'identification_element')]
#[ORM\Index(fields: ['extBusinessPartnerId'])]
#[ORM\Index(fields: ['businessPartnerId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extBusinessPartnerId', 'extIdentificationCategory', 'extIdentificationNumber'])]
#[UniqueEntity(fields: ['extBusinessPartnerId', 'extIdentificationCategory', 'extIdentificationNumber'])]
class IdentificationElement implements EntityInterface, HasTenant, HasBusinessPartner
{
    use CommonTrait;
    use TenantTrait;
    use BusinessPartnerTrait;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    #[ORM\Column(length: 6)]
    private string $extIdentificationCategory;

    #[ORM\Column(length: 60)]
    private string $extIdentificationNumber;

    public function __construct()
    {
        $this->init();
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }

    public function getExtIdentificationCategory(): string
    {
        return $this->extIdentificationCategory;
    }

    public function setExtIdentificationCategory(string $extIdentificationCategory): void
    {
        $this->extIdentificationCategory = $extIdentificationCategory;
    }

    public function getExtIdentificationNumber(): string
    {
        return $this->extIdentificationNumber;
    }

    public function setExtIdentificationNumber(string $extIdentificationNumber): void
    {
        $this->extIdentificationNumber = $extIdentificationNumber;
    }
}
