<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ArchiveDocumentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ArchiveDocumentRepository::class)]
#[ORM\Table(name: 'archive_document')]
#[ORM\Index(fields: ['extDocumentId'])]
#[ORM\Index(fields: ['invoiceId'])]
#[ORM\Index(fields: ['orderId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extObject', 'extObjectId'])]
#[UniqueEntity(fields: ['extId', 'extObject', 'extObjectId'])]
class ArchiveDocument implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 2)]
    private string $extId;

    #[ORM\Column(length: 40)]
    private string $extDocumentId;

    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $invoiceId;

    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $orderId;

    #[ORM\Column(length: 10)]
    private string $documentType;

    #[ORM\Column(type: Types::DATE_IMMUTABLE, length: 8)]
    private \DateTimeImmutable $storageDate;

    #[ORM\Column(type: Types::DATE_IMMUTABLE, length: 8, nullable: true)]
    private ?\DateTimeImmutable $expiryDate;

    #[ORM\Column(length: 10)]
    private string $extObject;

    #[ORM\Column(length: 50)]
    private string $extObjectId;

    #[ORM\Column(nullable: true)]
    private ?string $value;

    #[ORM\Column(length: 100)]
    private string $mimeType;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtDocumentId(): string
    {
        return $this->extDocumentId;
    }

    public function setExtDocumentId(string $extDocumentId): void
    {
        $this->extDocumentId = $extDocumentId;
    }

    public function getDocumentType(): string
    {
        return $this->documentType;
    }

    public function setDocumentType(string $documentType): void
    {
        $this->documentType = $documentType;
    }

    public function getStorageDate(): \DateTimeImmutable
    {
        return $this->storageDate;
    }

    public function setStorageDate(\DateTimeImmutable $storageDate): void
    {
        $this->storageDate = $storageDate;
    }

    public function getExpiryDate(): ?\DateTimeImmutable
    {
        return $this->expiryDate;
    }

    public function setExpiryDate(?\DateTimeImmutable $expiryDate): void
    {
        $this->expiryDate = $expiryDate;
    }

    public function getExtObject(): string
    {
        return $this->extObject;
    }

    public function setExtObject(string $extObject): void
    {
        $this->extObject = $extObject;
    }

    public function getExtObjectId(): string
    {
        return $this->extObjectId;
    }

    public function setExtObjectId(string $extObjectId): void
    {
        $this->extObjectId = $extObjectId;
    }

    public function getInvoiceId(): ?string
    {
        return $this->invoiceId;
    }

    public function setInvoiceId(?string $invoiceId): void
    {
        $this->invoiceId = $invoiceId;
    }

    public function getOrderId(): ?string
    {
        return $this->orderId;
    }

    public function setOrderId(?string $orderId): void
    {
        $this->orderId = $orderId;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(?string $value): void
    {
        $this->value = $value;
    }

    public function getMimeType(): string
    {
        return $this->mimeType;
    }

    public function setMimeType(string $mimeType): void
    {
        $this->mimeType = $mimeType;
    }
}
