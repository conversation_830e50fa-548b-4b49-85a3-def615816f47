<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Enum\Permission;
use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`user`')]
class User implements EntityInterface, UserInterface, HasTenant
{
    use TenantTrait;
    use CommonTrait;

    #[ORM\Column(unique: true)]
    private string $username;

    #[ORM\Column(nullable: true)]
    private ?string $firstname;

    #[ORM\Column(nullable: true)]
    private ?string $lastname;

    /** @var string[] */
    private array $roles = [];

    /**
     * @var array<int, Permission>
     */
    #[ORM\Column(type: 'permission_enum_array', nullable: true)]
    private array $permissions = [];

    /**
     * @var Collection<int, BusinessPartner>
     */
    #[ORM\ManyToMany(targetEntity: BusinessPartner::class, cascade: ['persist'])]
    #[ORM\JoinTable(
        name: 'user_business_partner',
        joinColumns: [new ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', nullable: false)],
        inverseJoinColumns: [new ORM\JoinColumn(name: 'business_partner_id', referencedColumnName: 'id', nullable: false)],
    )]
    private Collection $businessPartners;

    /**
     * @var Collection<int, Group>
     */
    #[ORM\ManyToMany(targetEntity: Group::class, mappedBy: 'users', cascade: ['persist'])]
    private Collection $groups;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
        $this->businessPartners = new ArrayCollection();
        $this->groups = new ArrayCollection();
    }

    public function getUserIdentifier(): string
    {
        if ('' === $this->username) {
            throw new \RuntimeException(message: 'Username cannot be empty.');
        }

        return $this->username;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function setUsername(string $username): void
    {
        $this->username = $username;
    }

    public function getFirstname(): ?string
    {
        return $this->firstname;
    }

    public function setFirstname(?string $firstname): void
    {
        $this->firstname = $firstname;
    }

    public function getLastname(): ?string
    {
        return $this->lastname;
    }

    public function setLastname(?string $lastname): void
    {
        $this->lastname = $lastname;
    }

    public function getName(): string
    {
        $name = '';

        if (null !== $this->firstname) {
            $name = $this->firstname;
        }

        if (null !== $this->lastname) {
            $name .= ' '.$this->lastname;
        }

        return trim(string: $name);
    }

    public function getRoles(): array
    {
        $roles = $this->roles;
        $roles[] = 'ROLE_USER';

        return array_unique(array: $roles);
    }

    /**
     * @param string[] $roles
     */
    public function setRoles(array $roles): void
    {
        $this->roles = $roles;
    }

    /**
     * @return array<int, Permission>
     */
    public function getPermissions(): array
    {
        return $this->permissions;
    }

    public function addPermission(Permission $permission): void
    {
        $this->permissions[] = $permission;
    }

    public function clearPermissions(): void
    {
        $this->permissions = [];
    }

    /**
     * @return Collection<int, BusinessPartner>
     */
    public function getBusinessPartners(): Collection
    {
        return $this->businessPartners;
    }

    public function addBusinessPartner(BusinessPartner $businessPartner): void
    {
        if (!$this->businessPartners->contains(element: $businessPartner)) {
            $this->businessPartners->add($businessPartner);
            $businessPartner->addUser(user: $this);
        }
    }

    public function removeBusinessPartner(BusinessPartner $businessPartner): void
    {
        if ($this->businessPartners->contains($businessPartner)) {
            $this->businessPartners->removeElement($businessPartner);
            $businessPartner->removeUser(user: $this);
        }
    }

    public function clearBusinessPartners(): void
    {
        foreach ($this->businessPartners as $businessPartner) {
            $this->removeBusinessPartner(businessPartner: $businessPartner);
        }

        $this->businessPartners->clear();
    }

    /**
     * @return Collection<int, Group>
     */
    public function getGroups(): Collection
    {
        return $this->groups;
    }

    public function addGroup(Group $group): void
    {
        if (!$this->groups->contains(element: $group)) {
            $this->groups->add($group);
            $group->addUser(user: $this);
        }
    }

    public function removeGroup(Group $group): void
    {
        if ($this->groups->contains($group)) {
            $this->groups->removeElement($group);
            $group->removeUser(user: $this);
        }
    }

    public function clearGroups(): void
    {
        foreach ($this->groups as $group) {
            $this->removeGroup(group: $group);
        }
    }

    public function eraseCredentials(): void
    {
        // TODO: Implement eraseCredentials() method.
    }
}
