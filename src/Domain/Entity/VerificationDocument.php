<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\VerificationDocumentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: VerificationDocumentRepository::class)]
#[ORM\Table(name: 'verification_document')]
#[ORM\Index(fields: ['extBusinessPartnerId'])]
#[ORM\Index(fields: ['extSalesOrganisationId'])]
#[ORM\Index(fields: ['extContractId'])]
#[ORM\Index(fields: ['certificateNumber'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extVersion'])]
#[ORM\UniqueConstraint(fields: ['extObjectId'])]
#[ORM\UniqueConstraint(fields: ['extDocId'])]
#[UniqueEntity(fields: ['extId', 'extVersion'])]
#[UniqueEntity(fields: ['extObjectId'])]
#[UniqueEntity(fields: ['extDocId'])]
class VerificationDocument implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 2)]
    private string $extVersion;

    #[ORM\Column(length: 12)]
    private string $certificateNumber;

    #[ORM\Column(length: 2)]
    private string $certificateType;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    #[ORM\Column(length: 4)]
    private string $extSalesOrganisationId;

    #[ORM\Column(length: 10)]
    private string $extContractId;

    #[ORM\Column(length: 6)]
    private string $avvId;

    #[ORM\Column(length: 255)]
    private string $avvDescription;

    #[ORM\Column(length: 30)]
    private string $extServiceLocationId;

    #[ORM\Column]
    private float $approvedAmountDocument;

    #[ORM\Column(nullable: true)]
    private ?float $approvedAmountYear;

    #[ORM\Column]
    private float $approvedRemAmountDocument;

    #[ORM\Column]
    private float $approvedRemAmountYear;

    #[ORM\Column(length: 3)]
    private string $amountUnitOm;

    #[ORM\Column(nullable: true)]
    private ?bool $deletionFlag;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $applicationDate;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $dateCustomer;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $dateDisposer;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $confirmationAuthority;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $approval;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $endDate;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE, nullable: true)]
    private ?\DateTimeImmutable $lockedDate;

    #[ORM\Column(length: 22)]
    private string $extObjectId;

    #[ORM\Column(length: 100)]
    private string $description;

    #[ORM\Column(length: 50)]
    private string $extDocId;

    #[ORM\Column(length: 40)]
    private string $status;

    #[ORM\Column(length: 100)]
    private string $statusDescription;

    #[ORM\Column(length: 6, nullable: true)]
    private ?string $dataStatus;

    #[ORM\Column(nullable: true)]
    private ?string $value;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $mimeType;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtVersion(): string
    {
        return $this->extVersion;
    }

    public function setExtVersion(string $extVersion): void
    {
        $this->extVersion = $extVersion;
    }

    public function getCertificateNumber(): string
    {
        return $this->certificateNumber;
    }

    public function setCertificateNumber(string $certificateNumber): void
    {
        $this->certificateNumber = $certificateNumber;
    }

    public function getCertificateType(): string
    {
        return $this->certificateType;
    }

    public function setCertificateType(string $certificateType): void
    {
        $this->certificateType = $certificateType;
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }

    public function getExtSalesOrganisationId(): string
    {
        return $this->extSalesOrganisationId;
    }

    public function setExtSalesOrganisationId(string $extSalesOrganisationId): void
    {
        $this->extSalesOrganisationId = $extSalesOrganisationId;
    }

    public function getExtContractId(): string
    {
        return $this->extContractId;
    }

    public function setExtContractId(string $extContractId): void
    {
        $this->extContractId = $extContractId;
    }

    public function getAvvId(): string
    {
        return $this->avvId;
    }

    public function setAvvId(string $avvId): void
    {
        $this->avvId = $avvId;
    }

    public function getAvvDescription(): string
    {
        return $this->avvDescription;
    }

    public function setAvvDescription(string $avvDescription): void
    {
        $this->avvDescription = $avvDescription;
    }

    public function getExtServiceLocationId(): string
    {
        return $this->extServiceLocationId;
    }

    public function setExtServiceLocationId(string $extServiceLocationId): void
    {
        $this->extServiceLocationId = $extServiceLocationId;
    }

    public function getApprovedAmountDocument(): float
    {
        return $this->approvedAmountDocument;
    }

    public function setApprovedAmountDocument(float $approvedAmountDocument): void
    {
        $this->approvedAmountDocument = $approvedAmountDocument;
    }

    public function getApprovedAmountYear(): ?float
    {
        return $this->approvedAmountYear;
    }

    public function setApprovedAmountYear(?float $approvedAmountYear): void
    {
        $this->approvedAmountYear = $approvedAmountYear;
    }

    public function getApprovedRemAmountDocument(): float
    {
        return $this->approvedRemAmountDocument;
    }

    public function setApprovedRemAmountDocument(float $approvedRemAmountDocument): void
    {
        $this->approvedRemAmountDocument = $approvedRemAmountDocument;
    }

    public function getApprovedRemAmountYear(): float
    {
        return $this->approvedRemAmountYear;
    }

    public function setApprovedRemAmountYear(float $approvedRemAmountYear): void
    {
        $this->approvedRemAmountYear = $approvedRemAmountYear;
    }

    public function getAmountUnitOm(): string
    {
        return $this->amountUnitOm;
    }

    public function setAmountUnitOm(string $amountUnitOm): void
    {
        $this->amountUnitOm = $amountUnitOm;
    }

    public function isDeletionFlag(): ?bool
    {
        return $this->deletionFlag;
    }

    public function setDeletionFlag(?bool $deletionFlag): void
    {
        $this->deletionFlag = $deletionFlag;
    }

    public function getApplicationDate(): \DateTimeImmutable
    {
        return $this->applicationDate;
    }

    public function setApplicationDate(\DateTimeImmutable $applicationDate): void
    {
        $this->applicationDate = $applicationDate;
    }

    public function getDateCustomer(): \DateTimeImmutable
    {
        return $this->dateCustomer;
    }

    public function setDateCustomer(\DateTimeImmutable $dateCustomer): void
    {
        $this->dateCustomer = $dateCustomer;
    }

    public function getDateDisposer(): \DateTimeImmutable
    {
        return $this->dateDisposer;
    }

    public function setDateDisposer(\DateTimeImmutable $dateDisposer): void
    {
        $this->dateDisposer = $dateDisposer;
    }

    public function getConfirmationAuthority(): \DateTimeImmutable
    {
        return $this->confirmationAuthority;
    }

    public function setConfirmationAuthority(\DateTimeImmutable $confirmationAuthority): void
    {
        $this->confirmationAuthority = $confirmationAuthority;
    }

    public function getApproval(): \DateTimeImmutable
    {
        return $this->approval;
    }

    public function setApproval(\DateTimeImmutable $approval): void
    {
        $this->approval = $approval;
    }

    public function getEndDate(): \DateTimeImmutable
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeImmutable $endDate): void
    {
        $this->endDate = $endDate;
    }

    public function getLockedDate(): ?\DateTimeImmutable
    {
        return $this->lockedDate;
    }

    public function setLockedDate(?\DateTimeImmutable $lockedDate): void
    {
        $this->lockedDate = $lockedDate;
    }

    public function getExtObjectId(): string
    {
        return $this->extObjectId;
    }

    public function setExtObjectId(string $extObjectId): void
    {
        $this->extObjectId = $extObjectId;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getExtDocId(): string
    {
        return $this->extDocId;
    }

    public function setExtDocId(string $extDocId): void
    {
        $this->extDocId = $extDocId;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function getStatusDescription(): string
    {
        return $this->statusDescription;
    }

    public function setStatusDescription(string $statusDescription): void
    {
        $this->statusDescription = $statusDescription;
    }

    public function getDataStatus(): ?string
    {
        return $this->dataStatus;
    }

    public function setDataStatus(?string $dataStatus): void
    {
        $this->dataStatus = $dataStatus;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(?string $value): void
    {
        $this->value = $value;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(?string $mimeType): void
    {
        $this->mimeType = $mimeType;
    }
}
