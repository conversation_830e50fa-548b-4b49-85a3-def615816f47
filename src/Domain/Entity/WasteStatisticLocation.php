<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\WasteStatisticLocationRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: WasteStatisticLocationRepository::class)]
#[ORM\Table(name: 'waste_statistic_location')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['tenant'])]
#[UniqueEntity(fields: ['extId'])]
class WasteStatisticLocation implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $extId;

    #[ORM\Column(length: 250, nullable: true)]
    private ?string $address;

    #[ORM\Column(length: 250, nullable: true)]
    private ?string $info;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $name;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtId(): ?string
    {
        return $this->extId;
    }

    public function setExtId(?string $extId): void
    {
        $this->extId = $extId;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): void
    {
        $this->address = $address;
    }

    public function getInfo(): ?string
    {
        return $this->info;
    }

    public function setInfo(?string $info): void
    {
        $this->info = $info;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }
}
