<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasAddress;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\AddressTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\SupplierRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: SupplierRepository::class)]
#[ORM\Table(name: 'supplier')]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['vkorg'])]
class Supplier implements EntityInterface, HasTenant, HasAddress
{
    use CommonTrait;
    use TenantTrait;
    use AddressTrait;

    #[ORM\Column(length: 50)]
    private string $region;

    #[ORM\Column(length: 50)]
    private string $location;

    #[ORM\Column(length: 50)]
    private string $description;

    #[ORM\Column(length: 50)]
    private string $number;

    #[ORM\Column(length: 50)]
    private string $name;

    #[ORM\Column(length: 50)]
    private string $vkorg;

    #[ORM\Column(length: 5, nullable: true)]
    private ?string $orderDelay;

    #[ORM\Column(length: 50)]
    private string $email;

    #[ORM\Column(length: 50)]
    private string $phone;

    #[ORM\Column(length: 50)]
    private string $openingHours;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $orderDelayMonday;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $orderDelayTuesday;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $orderDelayWednesday;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $orderDelayThursday;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $orderDelayFriday;

    /**
     * @var Collection<int, OrderConfirmation>
     */
    #[ORM\OneToMany(targetEntity: OrderConfirmation::class, mappedBy: 'supplier', cascade: ['persist'], orphanRemoval: true)]
    private Collection $orderConfirmations;

    public function __construct()
    {
        $this->init();
        $this->orderConfirmations = new ArrayCollection();
    }

    public function getRegion(): string
    {
        return $this->region;
    }

    public function setRegion(string $region): void
    {
        $this->region = $region;
    }

    public function getLocation(): string
    {
        return $this->location;
    }

    public function setLocation(string $location): void
    {
        $this->location = $location;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    public function setNumber(string $number): void
    {
        $this->number = $number;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getPhone(): string
    {
        return $this->phone;
    }

    public function setPhone(string $phone): void
    {
        $this->phone = $phone;
    }

    public function getOpeningHours(): string
    {
        return $this->openingHours;
    }

    public function setOpeningHours(string $openingHours): void
    {
        $this->openingHours = $openingHours;
    }

    public function getOrderDelay(): ?string
    {
        return $this->orderDelay;
    }

    public function setOrderDelay(?string $orderDelay): void
    {
        $this->orderDelay = $orderDelay;
    }

    public function getVkorg(): string
    {
        return $this->vkorg;
    }

    public function setVkorg(string $vkorg): void
    {
        $this->vkorg = $vkorg;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): void
    {
        $this->email = $email;
    }

    public function getOrderDelayMonday(): ?string
    {
        return $this->orderDelayMonday;
    }

    public function setOrderDelayMonday(?string $orderDelayMonday): void
    {
        $this->orderDelayMonday = $orderDelayMonday;
    }

    public function getOrderDelayTuesday(): ?string
    {
        return $this->orderDelayTuesday;
    }

    public function setOrderDelayTuesday(?string $orderDelayTuesday): void
    {
        $this->orderDelayTuesday = $orderDelayTuesday;
    }

    public function getOrderDelayWednesday(): ?string
    {
        return $this->orderDelayWednesday;
    }

    public function setOrderDelayWednesday(?string $orderDelayWednesday): void
    {
        $this->orderDelayWednesday = $orderDelayWednesday;
    }

    public function getOrderDelayThursday(): ?string
    {
        return $this->orderDelayThursday;
    }

    public function setOrderDelayThursday(?string $orderDelayThursday): void
    {
        $this->orderDelayThursday = $orderDelayThursday;
    }

    public function getOrderDelayFriday(): ?string
    {
        return $this->orderDelayFriday;
    }

    public function setOrderDelayFriday(?string $orderDelayFriday): void
    {
        $this->orderDelayFriday = $orderDelayFriday;
    }

    /**
     * @return Collection<int, OrderConfirmation>
     */
    public function getOrderConfirmations(): Collection
    {
        return $this->orderConfirmations;
    }

    public function addOrderConfirmation(OrderConfirmation $orderConfirmation): void
    {
        if (!$this->orderConfirmations->contains($orderConfirmation)) {
            $this->orderConfirmations->add($orderConfirmation);
        }
    }

    public function removeOrderConfirmation(OrderConfirmation $orderConfirmation): void
    {
        if ($this->orderConfirmations->contains($orderConfirmation)) {
            $this->orderConfirmations->removeElement($orderConfirmation);
        }
    }

    public function clearOrderConfirmations(): void
    {
        foreach ($this->orderConfirmations as $orderConfirmation) {
            $this->removeOrderConfirmation(orderConfirmation: $orderConfirmation);
        }

        $this->orderConfirmations->clear();
    }
}
