<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ContractRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ContractRepository::class)]
#[ORM\Table(name: 'contract')]
#[ORM\Index(fields: ['extSalesOrganisationId'])]
#[ORM\Index(fields: ['extRouteId'])]
#[ORM\Index(fields: ['extServiceLocationId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId'])]
#[UniqueEntity(fields: ['extId'])]
class Contract implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 4)]
    private string $extSalesOrganisationId;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $extRouteId;

    #[ORM\Column(length: 4, nullable: true)]
    private ?string $shippingPoint;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $contractStartDate;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $contractEndDate;

    #[ORM\Column(length: 2)]
    private string $contractType;

    #[ORM\Column(length: 40)]
    private string $description;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $extServiceLocationId;

    #[ORM\Column(length: 4)]
    private string $paymentTerm;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $paymentMethod;

    #[ORM\Column(length: 24, nullable: true)]
    private ?string $extTransactionId;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $extCustomerPurchaseOrderId;

    #[ORM\Column(length: 4)]
    private string $customerPurchaseOrderType;

    #[ORM\Column(length: 12, nullable: true)]
    private ?string $discount;

    #[ORM\Column(nullable: true)]
    private ?bool $ntRelevance;

    #[ORM\Column(length: 3, nullable: true)]
    private ?string $customerGroup;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $extSubcontractorId;

    #[ORM\Column(nullable: true)]
    private ?float $priceTransportSub;

    #[ORM\Column(nullable: true)]
    private ?float $priceRecyclingSub;

    #[ORM\Column(nullable: true)]
    private ?float $priceRentSub;

    #[ORM\Column(length: 4)]
    private string $contractDocType;

    #[ORM\Column(length: 2, nullable: true)]
    private ?string $collectionPointType;

    #[ORM\Column(length: 60, nullable: true)]
    private ?string $collectionPointText;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtSalesOrganisationId(): string
    {
        return $this->extSalesOrganisationId;
    }

    public function setExtSalesOrganisationId(string $extSalesOrganisationId): void
    {
        $this->extSalesOrganisationId = $extSalesOrganisationId;
    }

    public function getExtRouteId(): ?string
    {
        return $this->extRouteId;
    }

    public function setExtRouteId(?string $extRouteId): void
    {
        $this->extRouteId = $extRouteId;
    }

    public function getShippingPoint(): ?string
    {
        return $this->shippingPoint;
    }

    public function setShippingPoint(?string $shippingPoint): void
    {
        $this->shippingPoint = $shippingPoint;
    }

    public function getContractStartDate(): \DateTimeImmutable
    {
        return $this->contractStartDate;
    }

    public function setContractStartDate(\DateTimeImmutable $contractStartDate): void
    {
        $this->contractStartDate = $contractStartDate;
    }

    public function getContractEndDate(): \DateTimeImmutable
    {
        return $this->contractEndDate;
    }

    public function setContractEndDate(\DateTimeImmutable $contractEndDate): void
    {
        $this->contractEndDate = $contractEndDate;
    }

    public function getContractType(): string
    {
        return $this->contractType;
    }

    public function setContractType(string $contractType): void
    {
        $this->contractType = $contractType;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getExtServiceLocationId(): ?string
    {
        return $this->extServiceLocationId;
    }

    public function setExtServiceLocationId(?string $extServiceLocationId): void
    {
        $this->extServiceLocationId = $extServiceLocationId;
    }

    public function getPaymentTerm(): string
    {
        return $this->paymentTerm;
    }

    public function setPaymentTerm(string $paymentTerm): void
    {
        $this->paymentTerm = $paymentTerm;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(?string $paymentMethod): void
    {
        $this->paymentMethod = $paymentMethod;
    }

    public function getExtTransactionId(): ?string
    {
        return $this->extTransactionId;
    }

    public function setExtTransactionId(?string $extTransactionId): void
    {
        $this->extTransactionId = $extTransactionId;
    }

    public function getExtCustomerPurchaseOrderId(): ?string
    {
        return $this->extCustomerPurchaseOrderId;
    }

    public function setExtCustomerPurchaseOrderId(?string $extCustomerPurchaseOrderId): void
    {
        $this->extCustomerPurchaseOrderId = $extCustomerPurchaseOrderId;
    }

    public function getCustomerPurchaseOrderType(): string
    {
        return $this->customerPurchaseOrderType;
    }

    public function setCustomerPurchaseOrderType(string $customerPurchaseOrderType): void
    {
        $this->customerPurchaseOrderType = $customerPurchaseOrderType;
    }

    public function getDiscount(): ?string
    {
        return $this->discount;
    }

    public function setDiscount(?string $discount): void
    {
        $this->discount = $discount;
    }

    public function isNtRelevance(): ?bool
    {
        return $this->ntRelevance;
    }

    public function setNtRelevance(?bool $ntRelevance): void
    {
        $this->ntRelevance = $ntRelevance;
    }

    public function getCustomerGroup(): ?string
    {
        return $this->customerGroup;
    }

    public function setCustomerGroup(?string $customerGroup): void
    {
        $this->customerGroup = $customerGroup;
    }

    public function getExtSubcontractorId(): ?string
    {
        return $this->extSubcontractorId;
    }

    public function setExtSubcontractorId(?string $extSubcontractorId): void
    {
        $this->extSubcontractorId = $extSubcontractorId;
    }

    public function getPriceTransportSub(): ?float
    {
        return $this->priceTransportSub;
    }

    public function setPriceTransportSub(?float $priceTransportSub): void
    {
        $this->priceTransportSub = $priceTransportSub;
    }

    public function getPriceRecyclingSub(): ?float
    {
        return $this->priceRecyclingSub;
    }

    public function setPriceRecyclingSub(?float $priceRecyclingSub): void
    {
        $this->priceRecyclingSub = $priceRecyclingSub;
    }

    public function getPriceRentSub(): ?float
    {
        return $this->priceRentSub;
    }

    public function setPriceRentSub(?float $priceRentSub): void
    {
        $this->priceRentSub = $priceRentSub;
    }

    public function getContractDocType(): string
    {
        return $this->contractDocType;
    }

    public function setContractDocType(string $contractDocType): void
    {
        $this->contractDocType = $contractDocType;
    }

    public function getCollectionPointType(): ?string
    {
        return $this->collectionPointType;
    }

    public function setCollectionPointType(?string $collectionPointType): void
    {
        $this->collectionPointType = $collectionPointType;
    }

    public function getCollectionPointText(): ?string
    {
        return $this->collectionPointText;
    }

    public function setCollectionPointText(?string $collectionPointText): void
    {
        $this->collectionPointText = $collectionPointText;
    }
}
