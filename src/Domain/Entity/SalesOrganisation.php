<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\BusinessPartnerTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\SalesOrganisationRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: SalesOrganisationRepository::class)]
#[ORM\Table(name: 'sales_organisation')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['extBusinessPartnerId'])]
#[ORM\Index(fields: ['businessPartnerId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extBusinessPartnerId'])]
#[UniqueEntity(fields: ['extId', 'extBusinessPartnerId'])]
class SalesOrganisation implements EntityInterface, HasTenant, HasBusinessPartner
{
    use CommonTrait;
    use TenantTrait;
    use BusinessPartnerTrait;

    #[ORM\Column(length: 4)]
    private string $extId;

    #[ORM\Column(length: 10)]
    private string $extBusinessPartnerId;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtBusinessPartnerId(): string
    {
        return $this->extBusinessPartnerId;
    }

    public function setExtBusinessPartnerId(string $extBusinessPartnerId): void
    {
        $this->extBusinessPartnerId = $extBusinessPartnerId;
    }
}
