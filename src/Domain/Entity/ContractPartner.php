<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasBusinessPartner;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\BusinessPartnerTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ContractPartnerRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ContractPartnerRepository::class)]
#[ORM\Table(name: 'contract_partner')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['extContractId'])]
#[ORM\Index(fields: ['contractId'])]
#[ORM\Index(fields: ['contractPosId'])]
#[ORM\Index(fields: ['businessPartnerId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId', 'extContractId', 'extContractPosId', 'role'])]
#[UniqueEntity(fields: ['extId', 'extContractId', 'extContractPosId', 'role'])]
class ContractPartner implements EntityInterface, HasTenant, HasBusinessPartner
{
    use CommonTrait;
    use TenantTrait;
    use BusinessPartnerTrait;

    #[ORM\Column(length: 10)]
    private string $extId;

    #[ORM\Column(length: 10)]
    private string $extContractId;

    #[ORM\Column(length: 6, nullable: true)]
    private ?string $extContractPosId;

    #[ORM\Column(type: Types::GUID)]
    private string $contractId;

    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $contractPosId;

    #[ORM\Column(length: 2)]
    private string $role;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $title;

    #[ORM\Column(length: 80)]
    private string $name;

    #[ORM\Column(length: 60, nullable: true)]
    private ?string $street;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $houseNumber;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $postalCode;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $city;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $district;

    #[ORM\Column(length: 3, nullable: true)]
    private ?string $country;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtContractId(): string
    {
        return $this->extContractId;
    }

    public function setExtContractId(string $extContractId): void
    {
        $this->extContractId = $extContractId;
    }

    public function getExtContractPosId(): ?string
    {
        return $this->extContractPosId;
    }

    public function setExtContractPosId(?string $extContractPosId): void
    {
        $this->extContractPosId = $extContractPosId;
    }

    public function getContractId(): string
    {
        return $this->contractId;
    }

    public function setContractId(string $contractId): void
    {
        $this->contractId = $contractId;
    }

    public function getContractPosId(): ?string
    {
        return $this->contractPosId;
    }

    public function setContractPosId(?string $contractPosId): void
    {
        $this->contractPosId = $contractPosId;
    }

    public function getRole(): string
    {
        return $this->role;
    }

    public function setRole(string $role): void
    {
        $this->role = $role;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getStreet(): ?string
    {
        return $this->street;
    }

    public function setStreet(?string $street): void
    {
        $this->street = $street;
    }

    public function getHouseNumber(): ?string
    {
        return $this->houseNumber;
    }

    public function setHouseNumber(?string $houseNumber): void
    {
        $this->houseNumber = $houseNumber;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): void
    {
        $this->postalCode = $postalCode;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): void
    {
        $this->city = $city;
    }

    public function getDistrict(): ?string
    {
        return $this->district;
    }

    public function setDistrict(?string $district): void
    {
        $this->district = $district;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): void
    {
        $this->country = $country;
    }
}
