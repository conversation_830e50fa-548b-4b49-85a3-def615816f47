<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\SignDataRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: SignDataRepository::class)]
#[ORM\Table(name: 'sign_data')]
#[ORM\Index(fields: ['extId'])]
#[ORM\Index(fields: ['supervisionDocumentId'])]
#[ORM\Index(fields: ['verificationDocumentId'])]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['sequence', 'extId', 'count'])]
#[UniqueEntity(fields: ['sequence', 'extId', 'count'])]
class SignData implements EntityInterface, HasTenant
{
    use CommonTrait;
    use TenantTrait;

    #[ORM\Column(length: 70)]
    private string $extId;

    #[ORM\Column(length: 50)]
    private string $extDocId;

    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $supervisionDocumentId;

    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $verificationDocumentId;

    #[ORM\Column]
    private int $sequence;

    #[ORM\Column]
    private int $count;

    #[ORM\Column(length: 6)]
    private string $role;

    #[ORM\Column(type: Types::DATETIME_IMMUTABLE)]
    private \DateTimeImmutable $signDateTime;

    #[ORM\Column(length: 70)]
    private string $signer;

    #[ORM\Column(length: 30)]
    private string $status;

    #[ORM\Column(length: 60)]
    private string $statusDescription;

    #[ORM\Column]
    private bool $extValid;

    public function __construct()
    {
        $this->init();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getExtDocId(): string
    {
        return $this->extDocId;
    }

    public function setExtDocId(string $extDocId): void
    {
        $this->extDocId = $extDocId;
    }

    public function getSupervisionDocumentId(): ?string
    {
        return $this->supervisionDocumentId;
    }

    public function setSupervisionDocumentId(?string $supervisionDocumentId): void
    {
        $this->supervisionDocumentId = $supervisionDocumentId;
    }

    public function getVerificationDocumentId(): ?string
    {
        return $this->verificationDocumentId;
    }

    public function setVerificationDocumentId(?string $verificationDocumentId): void
    {
        $this->verificationDocumentId = $verificationDocumentId;
    }

    public function getSequence(): int
    {
        return $this->sequence;
    }

    public function setSequence(int $sequence): void
    {
        $this->sequence = $sequence;
    }

    public function getCount(): int
    {
        return $this->count;
    }

    public function setCount(int $count): void
    {
        $this->count = $count;
    }

    public function getRole(): string
    {
        return $this->role;
    }

    public function setRole(string $role): void
    {
        $this->role = $role;
    }

    public function getSignDateTime(): \DateTimeImmutable
    {
        return $this->signDateTime;
    }

    public function setSignDateTime(\DateTimeImmutable $signDateTime): void
    {
        $this->signDateTime = $signDateTime;
    }

    public function getSigner(): string
    {
        return $this->signer;
    }

    public function setSigner(string $signer): void
    {
        $this->signer = $signer;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): void
    {
        $this->status = $status;
    }

    public function getStatusDescription(): string
    {
        return $this->statusDescription;
    }

    public function setStatusDescription(string $statusDescription): void
    {
        $this->statusDescription = $statusDescription;
    }

    public function isExtValid(): bool
    {
        return $this->extValid;
    }

    public function setExtValid(bool $extValid): void
    {
        $this->extValid = $extValid;
    }
}
