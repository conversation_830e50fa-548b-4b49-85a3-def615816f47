<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Interface\HasAddress;
use App\Domain\Entity\Interface\HasTenant;
use App\Domain\Entity\Trait\AddressTrait;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Entity\Trait\TenantTrait;
use App\Domain\Repository\ServiceLocationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: ServiceLocationRepository::class)]
#[ORM\Table(name: 'service_location')]
#[ORM\Index(fields: ['tenant'])]
#[ORM\UniqueConstraint(fields: ['extId'])]
#[UniqueEntity(fields: ['extId'])]
class ServiceLocation implements EntityInterface, HasTenant, HasAddress
{
    use CommonTrait;
    use TenantTrait;
    use AddressTrait;

    #[ORM\Column(length: 30)]
    private string $extId;

    #[ORM\Column(length: 80)]
    private string $name;

    #[ORM\Column(length: 30, nullable: true)]
    private ?string $additionalInformation = null;

    /**
     * @var Collection<int, Group>
     */
    #[ORM\ManyToMany(targetEntity: Group::class, mappedBy: 'users', cascade: ['persist'])]
    private Collection $groups;

    public function __construct(?string $id = null)
    {
        $this->init(id: $id);
        $this->groups = new ArrayCollection();
    }

    public function getExtId(): string
    {
        return $this->extId;
    }

    public function setExtId(string $extId): void
    {
        $this->extId = $extId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getAdditionalInformation(): ?string
    {
        return $this->additionalInformation;
    }

    public function setAdditionalInformation(?string $additionalInformation): void
    {
        $this->additionalInformation = $additionalInformation;
    }

    public function getServiceLocationInfo(): string
    {
        return $this->name.', '.$this->getAddress();
    }

    /**
     * @return Collection<int, Group>
     */
    public function getGroups(): Collection
    {
        return $this->groups;
    }

    public function addGroup(Group $group): void
    {
        if (!$this->groups->contains(element: $group)) {
            $this->groups->add($group);
            $group->addServiceLocation(serviceLocation: $this);
        }
    }

    public function removeGroup(Group $group): void
    {
        if ($this->groups->contains($group)) {
            $this->groups->removeElement($group);
            $group->removeServiceLocation(serviceLocation: $this);
        }
    }

    public function clearGroups(): void
    {
        foreach ($this->groups as $group) {
            $this->removeGroup(group: $group);
        }
    }
}
