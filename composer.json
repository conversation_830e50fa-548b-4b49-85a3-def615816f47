{"name": "prezero/myprezero-sf", "description": "MyPreZero Backend", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": "^8.4", "ext-amqp": "*", "ext-bcmath": "*", "ext-calendar": "*", "ext-ctype": "*", "ext-dom": "*", "ext-gd": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_pgsql": "*", "ext-redis": "*", "ext-simplexml": "*", "ext-sockets": "*", "ext-xsl": "*", "ext-zip": "*", "aws/aws-sdk-php": "^3.339", "azuyalabs/yasumi": "^2.8", "doctrine/dbal": "^4", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.4", "doctrine/orm": "^3.3", "fakerphp/faker": "^1.24", "guzzlehttp/guzzle": "^7.9", "hautelook/alice-bundle": "^2.15", "liip/monitor-bundle": "^2.23", "nelmio/cors-bundle": "^2.5", "phpdocumentor/reflection-docblock": "^5.6.1", "phpstan/phpdoc-parser": "^2.0", "prezero/api-bundle": "^3.0.0", "snc/redis-bundle": "^4.10", "symfony/amqp-messenger": "7.3.*", "symfony/asset": "7.3.*", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/expression-language": "7.3.*", "symfony/flex": "^2.4.7", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "7.3.*", "symfony/lock": "7.3.*", "symfony/mailer": "7.3.*", "symfony/messenger": "7.3.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "7.3.*", "symfony/property-info": "7.3.*", "symfony/runtime": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/serializer": "7.3.*", "symfony/translation": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/uid": "7.3.*", "symfony/validator": "7.3.*", "symfony/yaml": "7.3.*", "twig/extra-bundle": "^2.12|^3.19", "twig/twig": "^2.12|^3.19", "vuryss/serializer": "^2.0.0", "web-token/jwt-library": "^4.0.3", "ext-curl": "*"}, "require-dev": {"captainhook/captainhook": "^5.24.3", "codeception/c3": "^2.9", "codeception/codeception": "^5.1.2", "codeception/module-asserts": "^3.0", "codeception/module-phpbrowser": "^3.0", "codeception/module-rest": "^3.4", "codeception/module-symfony": "^3.5", "friendsofphp/php-cs-fixer": "^3.68.5", "league/openapi-psr7-validator": "^0.22.0", "phpstan/phpstan": "^2.1.3", "ramsey/conventional-commits": "^1.5.1", "rector/rector": "^2.0.8", "roave/security-advisories": "dev-latest", "savinmikhail/add_named_arguments_rector": "^0.1.17", "symfony/maker-bundle": "^1.62", "symfony/stopwatch": "7.3.*", "symfony/web-profiler-bundle": "7.3.*"}, "config": {"allow-plugins": {"captainhook/hook-installer": true, "codeception/c3": true, "php-http/discovery": true, "phpstan/extension-installer": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*", "symfony/polyfill-php83": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}, "captainhook": {"force-install": true, "only-enabled": false}, "ramsey/conventional-commits": {"config": {"types": ["perf", "revert", "chore", "docs", "style", "refactor", "test", "build", "ci"]}}}, "repositories": [{"name": "prezero/api-bundle", "type": "vcs", "url": "https://github.com/prezero/api-bundle"}]}