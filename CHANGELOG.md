# Changelog

## [0.24.1](https://github.com/prezero/myprezero-sf/compare/v0.24.0...v0.24.1) (2025-08-27)


### Bug Fixes

* **#620:** delete endpoint ([#621](https://github.com/prezero/myprezero-sf/issues/621)) ([af9ae4b](https://github.com/prezero/myprezero-sf/commit/af9ae4b5ecb2b3196e757c39bc21eacab28bbb82))
* **#636:** Get service location address ([#637](https://github.com/prezero/myprezero-sf/issues/637)) ([038fd93](https://github.com/prezero/myprezero-sf/commit/038fd93feec47aed276f4627e38ffb193ff8044a))
* **656:** fix get options even without group id ([#658](https://github.com/prezero/myprezero-sf/issues/658)) ([f499d55](https://github.com/prezero/myprezero-sf/commit/f499d55362fe4ac282e62cdeacd959864ef64d49))
* run messenger sync in local and tests ([#664](https://github.com/prezero/myprezero-sf/issues/664)) ([7ca688f](https://github.com/prezero/myprezero-sf/commit/7ca688fcd247e4330aefae00dceac3d32f26a529))


### Code Refactoring

* sap client ([#616](https://github.com/prezero/myprezero-sf/issues/616)) ([7fd7e8a](https://github.com/prezero/myprezero-sf/commit/7fd7e8a1d1648cc5187616d53837de1111828a2f))

## [0.24.0](https://github.com/prezero/myprezero-sf/compare/v0.23.0...v0.24.0) (2025-08-26)


### Features

* update translations ([47bb5ad](https://github.com/prezero/myprezero-sf/commit/47bb5add504515cb6ba416f14c8235cb4e7e5b3c))


### Bug Fixes

* **#612:** column contents ([#613](https://github.com/prezero/myprezero-sf/issues/613)) ([bfcc870](https://github.com/prezero/myprezero-sf/commit/bfcc8704f13f69f6ee94610366c6b2b9a6c897bb))
* **638:** add additinal info ([#660](https://github.com/prezero/myprezero-sf/issues/660)) ([41dda0e](https://github.com/prezero/myprezero-sf/commit/41dda0ea741f517fe9a393e048fae46c14f8065a))
* **661:** make translation key to lower ([#663](https://github.com/prezero/myprezero-sf/issues/663)) ([d32d5ad](https://github.com/prezero/myprezero-sf/commit/d32d5ad348fe867172b5a5721bd110741a31b0d8))
* async event ([#659](https://github.com/prezero/myprezero-sf/issues/659)) ([754baa4](https://github.com/prezero/myprezero-sf/commit/754baa42033202e568534cd90113057339a925d4))
* **deps:** update dependency aws/aws-sdk-php to v3.356.3 ([#654](https://github.com/prezero/myprezero-sf/issues/654)) ([f98529b](https://github.com/prezero/myprezero-sf/commit/f98529b1362c6d48d2d7835b742ab9302449a483))
* **deps:** update dependency symfony/flex to v2.8.2 ([#655](https://github.com/prezero/myprezero-sf/issues/655)) ([c7a40e5](https://github.com/prezero/myprezero-sf/commit/c7a40e5164dee5304096ce154f8f37dea78a2633))
* postgres version for dev db ([0a1d6e9](https://github.com/prezero/myprezero-sf/commit/0a1d6e96d3b9d49172f5ee07c00570d48c7d65d3))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.35.1 ([#662](https://github.com/prezero/myprezero-sf/issues/662)) ([283be2c](https://github.com/prezero/myprezero-sf/commit/283be2ca2c727ec1fe168a1a2082e75a19b46eac))


### Code Refactoring

* send order (service) to sap ([#611](https://github.com/prezero/myprezero-sf/issues/611)) ([d575aea](https://github.com/prezero/myprezero-sf/commit/d575aead0003f8e78e8c3fa3246c8be5c5c52b8e))

## [0.23.0](https://github.com/prezero/myprezero-sf/compare/v0.22.0...v0.23.0) (2025-08-25)


### Features

* update translations ([1cb788c](https://github.com/prezero/myprezero-sf/commit/1cb788c5a74794a50e9e31b32cda9068b33d382b))
* update translations ([74ff598](https://github.com/prezero/myprezero-sf/commit/74ff598255c64f5b3a74093a21323e22ef6d9267))


### Bug Fixes

* **651:** add service type ([#652](https://github.com/prezero/myprezero-sf/issues/652)) ([baaf78e](https://github.com/prezero/myprezero-sf/commit/baaf78ed588d9b4844c5fe11b7ca0f66781600ba))


### Miscellaneous Chores

* **deps:** update local docker-compose env dependencies ([#649](https://github.com/prezero/myprezero-sf/issues/649)) ([70d4746](https://github.com/prezero/myprezero-sf/commit/70d47469a06ddb3361a630e81dcd1c883f1c1381))
* **deps:** update postgres docker tag to v17.6 ([#653](https://github.com/prezero/myprezero-sf/issues/653)) ([dbb7d59](https://github.com/prezero/myprezero-sf/commit/dbb7d59d8acd6637b555ded72c875f785f0a6894))

## [0.22.0](https://github.com/prezero/myprezero-sf/compare/v0.21.1...v0.22.0) (2025-08-22)


### Features

* change xml parser ([#645](https://github.com/prezero/myprezero-sf/issues/645)) ([f48666c](https://github.com/prezero/myprezero-sf/commit/f48666cd8e2d741b0f3c73ac8cbddd9007935c03))


### Bug Fixes

* translation keys ([9196752](https://github.com/prezero/myprezero-sf/commit/9196752c9966c6dc3b6951aabd5e77b2f428e65d))
* translation keys ([2df3184](https://github.com/prezero/myprezero-sf/commit/2df318453d1ad62fe435e1950ff2304241a0d22a))
* translation keys ([ec05d12](https://github.com/prezero/myprezero-sf/commit/ec05d126fd1c65e58eb465870c168b5f4f09727c))


### Miscellaneous Chores

* **deps:** pin valkey/valkey docker tag to 9c3e63b ([#647](https://github.com/prezero/myprezero-sf/issues/647)) ([42f69a2](https://github.com/prezero/myprezero-sf/commit/42f69a25cb4e2ef7c5c9508c249302001f25717e))
* update translations ([8d76491](https://github.com/prezero/myprezero-sf/commit/8d764914b574c747f90e3a5992292f9cec9434ac))

## [0.21.1](https://github.com/prezero/myprezero-sf/compare/v0.21.0...v0.21.1) (2025-08-21)


### Bug Fixes

* service translation ([393c8f1](https://github.com/prezero/myprezero-sf/commit/393c8f1e2cd4a77453b0bba33dfb703a944ffb35))
* service translation ([b188fea](https://github.com/prezero/myprezero-sf/commit/b188fea1cc2a9cac09b07f60dc3810ac242efa86))

## [0.21.0](https://github.com/prezero/myprezero-sf/compare/v0.20.0...v0.21.0) (2025-08-21)


### Features

* **#574:** import translations from csv ([#618](https://github.com/prezero/myprezero-sf/issues/618)) ([91bc265](https://github.com/prezero/myprezero-sf/commit/91bc2658202f71762b3b29c2f8763563bf8ddd95))
* **#576:** remove translation entity ([#619](https://github.com/prezero/myprezero-sf/issues/619)) ([1795205](https://github.com/prezero/myprezero-sf/commit/1795205076314163e9c5415cac0e0907c30effc5))
* Add sap call mocks ([#530](https://github.com/prezero/myprezero-sf/issues/530)) ([89ea6e4](https://github.com/prezero/myprezero-sf/commit/89ea6e4b326f70f0d1ed284a30074ec7e368eaad))
* add translations ([#644](https://github.com/prezero/myprezero-sf/issues/644)) ([fbea152](https://github.com/prezero/myprezero-sf/commit/fbea152b0cd0198386907a59636ea4021b1bc6a7))
* update translations ([2c7aedc](https://github.com/prezero/myprezero-sf/commit/2c7aedc40d18f2cde8b2d6b9061a673d002a8f40))
* update translations ([0b5bc7e](https://github.com/prezero/myprezero-sf/commit/0b5bc7e52996a2234b32f84aad68d6567fec0180))


### Bug Fixes

* translation key ([5e0cdcf](https://github.com/prezero/myprezero-sf/commit/5e0cdcf97c18a2e16d019a43eef89a7150dd171f))

## [0.20.0](https://github.com/prezero/myprezero-sf/compare/v0.19.0...v0.20.0) (2025-08-21)


### Features

* **#406:** connect waste statistic to sap ([#598](https://github.com/prezero/myprezero-sf/issues/598)) ([98679b5](https://github.com/prezero/myprezero-sf/commit/98679b593dc2aa76f0e4aa802da3119f0c7ac268))
* **#505:** async quere for events ([#570](https://github.com/prezero/myprezero-sf/issues/570)) ([4347fc1](https://github.com/prezero/myprezero-sf/commit/4347fc13db507fa63236c8fc54c647fbd38787f1))
* **#569:** xliff ([#608](https://github.com/prezero/myprezero-sf/issues/608)) ([5a465bd](https://github.com/prezero/myprezero-sf/commit/5a465bd0d82834837d15bfeaf18995ab599401fe))
* **#601:** create middleware tenant ([#602](https://github.com/prezero/myprezero-sf/issues/602)) ([210c0dc](https://github.com/prezero/myprezero-sf/commit/210c0dc9c7eea4347045a288c5b1c95f2c9d5595))
* **578:** add redis ([#606](https://github.com/prezero/myprezero-sf/issues/606)) ([79d9c2b](https://github.com/prezero/myprezero-sf/commit/79d9c2b03c56a226a476da79659d66598afe2f34))


### Bug Fixes

* **#594:** maximum execution time exceeded ([#600](https://github.com/prezero/myprezero-sf/issues/600)) ([6845bf5](https://github.com/prezero/myprezero-sf/commit/6845bf58dfcd0872bdda6afcaa333343898359ef))
* **#603:** sorting in order ([#604](https://github.com/prezero/myprezero-sf/issues/604)) ([b5a79e9](https://github.com/prezero/myprezero-sf/commit/b5a79e96739ce3ded0e7240da36b3f0569409f3a))
* **deps:** update composer packages ([#599](https://github.com/prezero/myprezero-sf/issues/599)) ([09e2bbc](https://github.com/prezero/myprezero-sf/commit/09e2bbcb0cf08124416a1ef951866755b978f3ae))
* **deps:** update composer packages ([#614](https://github.com/prezero/myprezero-sf/issues/614)) ([aff6983](https://github.com/prezero/myprezero-sf/commit/aff69838a28e8c85bd522667027479a4c8e40b40))
* **deps:** update dependency aws/aws-sdk-php to v3.354.0 ([#609](https://github.com/prezero/myprezero-sf/issues/609)) ([9faa9ee](https://github.com/prezero/myprezero-sf/commit/9faa9ee4fe1cfdf6588e811190fbb7134049576b))
* rector changes ([#639](https://github.com/prezero/myprezero-sf/issues/639)) ([ee39fa4](https://github.com/prezero/myprezero-sf/commit/ee39fa4baf79a3216a474eb745d1e5410247435a))
* remove doctrine messenger. Messenger not needed ([#622](https://github.com/prezero/myprezero-sf/issues/622)) ([4d4914e](https://github.com/prezero/myprezero-sf/commit/4d4914e7ed9b6c40a00de5cd6e0da3b53994c53d))


### Miscellaneous Chores

* **deps:** pin postgres docker tag to 6567bca ([#607](https://github.com/prezero/myprezero-sf/issues/607)) ([4a1b6c9](https://github.com/prezero/myprezero-sf/commit/4a1b6c9d50dc626e06c0de29e484561097958227))
* **deps:** update ghcr.io/prezero/keycloak docker tag to v3.1.1 ([#641](https://github.com/prezero/myprezero-sf/issues/641)) ([296120e](https://github.com/prezero/myprezero-sf/commit/296120e929af7a8ec95c8cb03c21f1277e5fc4df))
* **deps:** update googleapis/release-please-action digest to c2a5a2b ([#640](https://github.com/prezero/myprezero-sf/issues/640)) ([e6eb2ca](https://github.com/prezero/myprezero-sf/commit/e6eb2ca0aa874f818922e45a3b7821514f7ec507))
* **deps:** update postgres docker tag to v17.6 ([#615](https://github.com/prezero/myprezero-sf/issues/615)) ([2866a5b](https://github.com/prezero/myprezero-sf/commit/2866a5b4de7fd51eeba0cd1764f6c00b1a8a8130))
* **deps:** update prezero/workflows action to v1.35.0 ([#610](https://github.com/prezero/myprezero-sf/issues/610)) ([f583dfd](https://github.com/prezero/myprezero-sf/commit/f583dfd1923cb86479d24cd97a3dff7d623040e0))


### Continuous Integration

* fix typo in dockerfile comment ([ccb7f6c](https://github.com/prezero/myprezero-sf/commit/ccb7f6cf47b8465fd2e033db174fad9df64d3698))
* separate release-please and image build ([99adbc7](https://github.com/prezero/myprezero-sf/commit/99adbc76c66b45f21a65c41959770986be60c961))

## [0.19.0](https://github.com/prezero/myprezero-sf/compare/v0.18.0...v0.19.0) (2025-08-14)


### Features

* **#492:** add post management businesspartner user endpoint ([#566](https://github.com/prezero/myprezero-sf/issues/566)) ([e1b683c](https://github.com/prezero/myprezero-sf/commit/e1b683ccb175059f69e37cb0a78d17d122fba41e))
* **#493:** add patch management businesspartner user endpoint ([#561](https://github.com/prezero/myprezero-sf/issues/561)) ([e88c5e4](https://github.com/prezero/myprezero-sf/commit/e88c5e4ddeedef4fe48e199f558bbf27ee692fe5))
* **#591:** do not show order without service ([#592](https://github.com/prezero/myprezero-sf/issues/592)) ([ea8ae1e](https://github.com/prezero/myprezero-sf/commit/ea8ae1e78163ce1ded453938842d89c625a578c9))


### Bug Fixes

* **#590:** remove static business partner ([#596](https://github.com/prezero/myprezero-sf/issues/596)) ([c47ec7b](https://github.com/prezero/myprezero-sf/commit/c47ec7b45505fa914690b23b5b6a71e32380c127))
* **#593:** url ([#595](https://github.com/prezero/myprezero-sf/issues/595)) ([6ff46cb](https://github.com/prezero/myprezero-sf/commit/6ff46cb29ed0bd484aabe603612151a01a10b2f9))
* error message ([d157ec2](https://github.com/prezero/myprezero-sf/commit/d157ec27c88f8a316478d1f5146eb4bba081e026))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.34.1 ([#571](https://github.com/prezero/myprezero-sf/issues/571)) ([f0319a1](https://github.com/prezero/myprezero-sf/commit/f0319a1db67f9961fd61b14d2725c88432e849f8))
* **deps:** update rabbitmq:4.1-management docker digest to 4c52100 ([#567](https://github.com/prezero/myprezero-sf/issues/567)) ([80ef1a9](https://github.com/prezero/myprezero-sf/commit/80ef1a9783aa5a237912cba4bfa0cffaa29d6ffd))


### Code Refactoring

* use self ([#572](https://github.com/prezero/myprezero-sf/issues/572)) ([ea39777](https://github.com/prezero/myprezero-sf/commit/ea397773949a03beb4e77dce9b05910f084e6837))

## [0.18.0](https://github.com/prezero/myprezero-sf/compare/v0.17.1...v0.18.0) (2025-08-12)


### Features

* **#334:** create service (order) in sap ([#402](https://github.com/prezero/myprezero-sf/issues/402)) ([5ddd078](https://github.com/prezero/myprezero-sf/commit/5ddd0784fdb5b5c294e07698263e112d16bb6182))
* **#369:** connect to database get dates for order ([#446](https://github.com/prezero/myprezero-sf/issues/446)) ([16d2636](https://github.com/prezero/myprezero-sf/commit/16d2636f3c885dc4c10c61ac7f7c63147fd0c380))
* **#444:** add get supplier by id ([#447](https://github.com/prezero/myprezero-sf/issues/447)) ([87ad7a5](https://github.com/prezero/myprezero-sf/commit/87ad7a53c7d124ed930054031e451d42b3a8c273))
* **#448:** event get federal state ([#452](https://github.com/prezero/myprezero-sf/issues/452)) ([62e5e23](https://github.com/prezero/myprezero-sf/commit/62e5e23eb4731aa83f523173205770fb2309f415))
* **#465:** connect to database get user ([#519](https://github.com/prezero/myprezero-sf/issues/519)) ([14ee72d](https://github.com/prezero/myprezero-sf/commit/14ee72dcbaab7bd2dd44a9a8d63d850b198f7df2))
* **#466:** connect to database get user by ([#527](https://github.com/prezero/myprezero-sf/issues/527)) ([0192b73](https://github.com/prezero/myprezero-sf/commit/0192b73fa983eeea45e091af08932f3da66c8ace))
* **#467:** Connect to database patch user ([#526](https://github.com/prezero/myprezero-sf/issues/526)) ([c452c5b](https://github.com/prezero/myprezero-sf/commit/c452c5b07eecb8486b2e9ea99b5dbe3408fdc80a))
* **#468:** Connect to database delete user ([#528](https://github.com/prezero/myprezero-sf/issues/528)) ([a3d38de](https://github.com/prezero/myprezero-sf/commit/a3d38dea16e11a5bfb38099a9f2c193f01f4f0b1))
* **#469:** connect to database post user ([#521](https://github.com/prezero/myprezero-sf/issues/521)) ([11ed87d](https://github.com/prezero/myprezero-sf/commit/11ed87daa88f5d0726b0f65cb5e632c07ec88b3c))
* **#473:** Connect to database post group ([#513](https://github.com/prezero/myprezero-sf/issues/513)) ([f11ef29](https://github.com/prezero/myprezero-sf/commit/f11ef2953e42dba6b23c95dae69e3b498226587f))
* **#479:** connect to database user permission options ([#529](https://github.com/prezero/myprezero-sf/issues/529)) ([d2b5dc6](https://github.com/prezero/myprezero-sf/commit/d2b5dc6c9cd9c168814c2c79494fa27ef803a2db))
* **#480:** connect to database get group options ([#534](https://github.com/prezero/myprezero-sf/issues/534)) ([b045529](https://github.com/prezero/myprezero-sf/commit/b045529e434696c224dd9056cf77eb5d6668612c))
* **#482:** connect to database get business partner ([#552](https://github.com/prezero/myprezero-sf/issues/552)) ([4807254](https://github.com/prezero/myprezero-sf/commit/4807254d0bd723a0e28dcfd1b254c98eff1eee65))
* **#483:** connect to database get business partner by ([#553](https://github.com/prezero/myprezero-sf/issues/553)) ([df855ed](https://github.com/prezero/myprezero-sf/commit/df855eddeca5206586ffb5fb165437af92f76055))
* **#484:** connect to database get management users ([#540](https://github.com/prezero/myprezero-sf/issues/540)) ([612015a](https://github.com/prezero/myprezero-sf/commit/612015ae9ccb55d0583452066a9d08c9667f51d6))
* **#487:** connect to database get permission options ([#549](https://github.com/prezero/myprezero-sf/issues/549)) ([6544e7d](https://github.com/prezero/myprezero-sf/commit/6544e7daa92e656ff3aefeb4187627b4d1eebf6f))
* **#488:** connect to database get management user by id ([#547](https://github.com/prezero/myprezero-sf/issues/547)) ([34a681a](https://github.com/prezero/myprezero-sf/commit/34a681a3604a88d4fa4c4effec53ee9b3d91877a))
* **#489:** connect to database delete management user ([#548](https://github.com/prezero/myprezero-sf/issues/548)) ([eff6adf](https://github.com/prezero/myprezero-sf/commit/eff6adf6f7282e552fcd40d819a0640bc20622d9))
* **#491:** add get management business partner user by id ([#559](https://github.com/prezero/myprezero-sf/issues/559)) ([39877f1](https://github.com/prezero/myprezero-sf/commit/39877f173d6fed23c057a2e11a9e96ffba83367d))
* **#494:** add delete management businesspartner user endpoint ([#564](https://github.com/prezero/myprezero-sf/issues/564)) ([e821a5a](https://github.com/prezero/myprezero-sf/commit/e821a5a272a020b1f5a79a6dde8972b0fa12eccf))
* **#497:** add get groups by management businesspartner endpoint ([#532](https://github.com/prezero/myprezero-sf/issues/532)) ([c06d08d](https://github.com/prezero/myprezero-sf/commit/c06d08d31435f9546d7112e4cc0eee25d944cbc8))
* **#498:** add get detail group by managament businesspartner endpoint ([#533](https://github.com/prezero/myprezero-sf/issues/533)) ([034dad0](https://github.com/prezero/myprezero-sf/commit/034dad091dca8259fa6d2e2cf9adc40e0ba23b7e))
* **#500:** add get management businesspartner servicelocations endpoint ([#543](https://github.com/prezero/myprezero-sf/issues/543)) ([dae0b8d](https://github.com/prezero/myprezero-sf/commit/dae0b8da4ea80a43b0249beda4482bc301717559))
* **#502:** add patch management businesspartner group endpoint ([#537](https://github.com/prezero/myprezero-sf/issues/537)) ([9e52ca6](https://github.com/prezero/myprezero-sf/commit/9e52ca61bfcc95bda45a61596ce3d431b280606b))
* **#503:** add get management businesspartner users endpoint ([#550](https://github.com/prezero/myprezero-sf/issues/550)) ([762db43](https://github.com/prezero/myprezero-sf/commit/762db4330b3ff986a4b14133dd73a58ac7d5c43c))
* **#504:** add management businesspartner group user patch endpoint ([#557](https://github.com/prezero/myprezero-sf/issues/557)) ([a359bf4](https://github.com/prezero/myprezero-sf/commit/a359bf4d7a032c6a142253aeb2f86b5a05fb2c48))
* **#544:** add get management businesspartner group servicelocations endpoint ([#545](https://github.com/prezero/myprezero-sf/issues/545)) ([6566c13](https://github.com/prezero/myprezero-sf/commit/6566c130f575b713fbf18035df121989a8aca7bb))
* **#554:** add get management businesspartner group users endpoint ([#551](https://github.com/prezero/myprezero-sf/issues/551)) ([a264c87](https://github.com/prezero/myprezero-sf/commit/a264c87133f91dfb9579d86318fedda1e8c02d06))
* **400:** show material text ([#509](https://github.com/prezero/myprezero-sf/issues/509)) ([1270f27](https://github.com/prezero/myprezero-sf/commit/1270f27d28333a6b2fef9befc08a15b0e2d718f0))
* **425:** use enum for material type ([#562](https://github.com/prezero/myprezero-sf/issues/562)) ([03e5ec2](https://github.com/prezero/myprezero-sf/commit/03e5ec26759d3fd0adce1d1ac45703a68ca7bd54))
* **485:** connect to database post management user ([#542](https://github.com/prezero/myprezero-sf/issues/542)) ([2a69948](https://github.com/prezero/myprezero-sf/commit/2a69948ee87c53c6ddbfe15de9ea1b4b1417da1b))
* **486:** connect to database patch management user ([#546](https://github.com/prezero/myprezero-sf/issues/546)) ([f303a09](https://github.com/prezero/myprezero-sf/commit/f303a0997851d2a2a6e8983d717d151a6769f4d8))
* **495:** get user permission options ([#558](https://github.com/prezero/myprezero-sf/issues/558)) ([557d6cf](https://github.com/prezero/myprezero-sf/commit/557d6cfc9dbef8c66959a9c5d9e408d3adccd3c9))
* **496:** get group options for user in business partner ([#560](https://github.com/prezero/myprezero-sf/issues/560)) ([6e568d9](https://github.com/prezero/myprezero-sf/commit/6e568d9b9a342c5844598b6fb957fd6410a2897d))
* add management endpoints ([d6be33b](https://github.com/prezero/myprezero-sf/commit/d6be33ba1853ec8309381142bb30258b30a3e551))
* add tooltip and fix docs ([69a7d32](https://github.com/prezero/myprezero-sf/commit/69a7d32516113287a978266e14c3134975993672))
* Add user post and patch for management dummy endpoints ([#507](https://github.com/prezero/myprezero-sf/issues/507)) ([fbd127f](https://github.com/prezero/myprezero-sf/commit/fbd127fa80face86f52f490e30a2855b8e7abcf7))
* fix filters and add tests ([#404](https://github.com/prezero/myprezero-sf/issues/404)) ([aef5ad9](https://github.com/prezero/myprezero-sf/commit/aef5ad936e7d578401d90334aace736db858f477))


### Bug Fixes

* allow user email update ([#555](https://github.com/prezero/myprezero-sf/issues/555)) ([65e53e8](https://github.com/prezero/myprezero-sf/commit/65e53e8876022d50c3286824e7113e89a83f6088))
* bugs ([2ef93b1](https://github.com/prezero/myprezero-sf/commit/2ef93b1f801f9c161ec41863185f851d72c3b96d))
* change string to const ([#565](https://github.com/prezero/myprezero-sf/issues/565)) ([1f8d8a9](https://github.com/prezero/myprezero-sf/commit/1f8d8a9d8294a3658f52eb598c7432a1117edd28))
* **deps:** update composer packages ([#538](https://github.com/prezero/myprezero-sf/issues/538)) ([5c8707a](https://github.com/prezero/myprezero-sf/commit/5c8707acf9126aa8fba8cb16f3cf8a51059f3167))
* **deps:** update composer packages ([#539](https://github.com/prezero/myprezero-sf/issues/539)) ([455e280](https://github.com/prezero/myprezero-sf/commit/455e280ebb713d6a1163c38c4810e1efeba16944))
* **deps:** update dependency prezero/api-bundle to v3 & vuryss/serializer to v2 ([94f4412](https://github.com/prezero/myprezero-sf/commit/94f44126e84c56aa91e712320280d5be90cdb57f))
* extend from string type ([#541](https://github.com/prezero/myprezero-sf/issues/541)) ([0d696d4](https://github.com/prezero/myprezero-sf/commit/0d696d4254b349781c385305296e8e134a5e32e2))
* feedback ([ed27898](https://github.com/prezero/myprezero-sf/commit/ed27898625e55ff4164da7c8f91119523c27f4c1))
* get contract id ([#516](https://github.com/prezero/myprezero-sf/issues/516)) ([1a4b236](https://github.com/prezero/myprezero-sf/commit/1a4b23688b09cafa149b42e44a3481d6f75fa9f4))
* order bp ([f6d36e2](https://github.com/prezero/myprezero-sf/commit/f6d36e20442e116ee3afd2a0a8ef9a8f3dd061dc))
* quick fix, fake data ([cf3e6ec](https://github.com/prezero/myprezero-sf/commit/cf3e6ec887aafff91095a4717ad700c1788dcefc))
* remove required filter on contract option ([df91436](https://github.com/prezero/myprezero-sf/commit/df91436fa9d2c7d8299bdca27c67556517f259aa))
* remove users from create group ([#536](https://github.com/prezero/myprezero-sf/issues/536)) ([a809bbe](https://github.com/prezero/myprezero-sf/commit/a809bbe1258d0f497891d5489d0a2c80d4fc128e))
* service-type issue ([e814a13](https://github.com/prezero/myprezero-sf/commit/e814a1317ea0c2ed826f74ecbe34948fa90457c0))
* skipped tests ([#514](https://github.com/prezero/myprezero-sf/issues/514)) ([a23ed13](https://github.com/prezero/myprezero-sf/commit/a23ed1350e6bc51d7544faa3cac325ae8f20e378))
* status ([4b5997a](https://github.com/prezero/myprezero-sf/commit/4b5997ad71c16a04d880c0f9b3a5293cb86a63bb))
* status text ([973ecbb](https://github.com/prezero/myprezero-sf/commit/973ecbbc164af15bd34266161dee91c1d3b615be))
* supplier ([#508](https://github.com/prezero/myprezero-sf/issues/508)) ([6395c5d](https://github.com/prezero/myprezero-sf/commit/6395c5d6e18090fe6cb22c4b7c06e4c3ca31fa2d))
* test data ([#515](https://github.com/prezero/myprezero-sf/issues/515)) ([8b80041](https://github.com/prezero/myprezero-sf/commit/8b80041b11ba818f9d983628e27cf8bc7b019075))
* waste statistic ([#512](https://github.com/prezero/myprezero-sf/issues/512)) ([ec90c94](https://github.com/prezero/myprezero-sf/commit/ec90c948f945bec8a96aec4718cf53ee581aa36b))


### Miscellaneous Chores

* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v1.1.3 ([#511](https://github.com/prezero/myprezero-sf/issues/511)) ([5dc7465](https://github.com/prezero/myprezero-sf/commit/5dc74659886cb24ed032ce923c3e3e7eee6306e1))
* **deps:** update ghcr.io/prezero/keycloak docker tag to v3.1.0 ([#522](https://github.com/prezero/myprezero-sf/issues/522)) ([250c2a7](https://github.com/prezero/myprezero-sf/commit/250c2a7f06add0a41a83ad1dd733715bcea9084f))
* **deps:** update prezero/workflows action to v1.33.0 ([#525](https://github.com/prezero/myprezero-sf/issues/525)) ([5baaeba](https://github.com/prezero/myprezero-sf/commit/5baaeba537b78cf5d61dc024b5f0b977e7044f51))
* **deps:** update prezero/workflows action to v1.33.2 ([#531](https://github.com/prezero/myprezero-sf/issues/531)) ([19ca220](https://github.com/prezero/myprezero-sf/commit/19ca2206097a16257fb7e8d13dc1ff0735f2175d))
* **deps:** update prezero/workflows action to v1.33.3 ([#563](https://github.com/prezero/myprezero-sf/issues/563)) ([cbd1ab9](https://github.com/prezero/myprezero-sf/commit/cbd1ab90500f131fc83eb6b9f90abdf35b96c315))
* **deps:** update rabbitmq:4.1-management docker digest to d0e1a5a ([#517](https://github.com/prezero/myprezero-sf/issues/517)) ([1865b92](https://github.com/prezero/myprezero-sf/commit/1865b92df690b92cb0f3b0a2ed37a956e85bbca6))
* **deps:** update rabbitmq:4.1-management docker digest to ddc81a4 ([#510](https://github.com/prezero/myprezero-sf/issues/510)) ([5f6ed6a](https://github.com/prezero/myprezero-sf/commit/5f6ed6acb5e1a21b8253d774accb5df34f8bfedf))
* **deps:** update rabbitmq:4.1-management docker digest to e111bdf ([#523](https://github.com/prezero/myprezero-sf/issues/523)) ([54e2dba](https://github.com/prezero/myprezero-sf/commit/54e2dba09e2fb121485b3b137849fac243b139ae))


### Code Refactoring

* merge ([c52ffd6](https://github.com/prezero/myprezero-sf/commit/c52ffd60b6dc9e124a4f6601b9b90a9615d39964))
* users & groups ([d840fa3](https://github.com/prezero/myprezero-sf/commit/d840fa35f8a608f150b75f9204c5a8b5a4ece0f5))

## [0.17.1](https://github.com/prezero/myprezero-sf/compare/v0.17.0...v0.17.1) (2025-08-02)


### Bug Fixes

* **deps:** update dependency prezero/api-bundle to v2 ([b990608](https://github.com/prezero/myprezero-sf/commit/b9906082ad65835db89b7797a41649bb94824dea))

## [0.17.0](https://github.com/prezero/myprezero-sf/compare/v0.16.1...v0.17.0) (2025-08-02)


### Features

* **#368:** add order agreement option endpoint ([#434](https://github.com/prezero/myprezero-sf/issues/434)) ([8d583d7](https://github.com/prezero/myprezero-sf/commit/8d583d7b6f1b2fe11df97aecc35e6e8198b7e12b))
* Update group endpoint dummies ([#454](https://github.com/prezero/myprezero-sf/issues/454)) ([823968f](https://github.com/prezero/myprezero-sf/commit/823968ff6294a8218291e9c2741bbe14fbd2e839))

## [0.16.1](https://github.com/prezero/myprezero-sf/compare/v0.16.0...v0.16.1) (2025-08-02)


### Bug Fixes

* **deps:** update composer packages ([#450](https://github.com/prezero/myprezero-sf/issues/450)) ([5b40c7f](https://github.com/prezero/myprezero-sf/commit/5b40c7fb8732a2e86a79ca0654e2a5069177a718))


### Miscellaneous Chores

* **deps:** pin quay.io/minio/minio docker tag to d249d1f ([#460](https://github.com/prezero/myprezero-sf/issues/460)) ([e5aa981](https://github.com/prezero/myprezero-sf/commit/e5aa981307609e69f683b1b3a6abbd8d4c77e1d4))
* **deps:** update ghcr.io/prezero/keycloak docker tag to v3 ([#435](https://github.com/prezero/myprezero-sf/issues/435)) ([103ddb4](https://github.com/prezero/myprezero-sf/commit/103ddb4c29179e3dad954a4c331a7ccdbf77fee7))
* **deps:** update postgres docker tag to v17.5 ([#458](https://github.com/prezero/myprezero-sf/issues/458)) ([4d1db43](https://github.com/prezero/myprezero-sf/commit/4d1db43cd8077a77740df575988c346e8dcd9f3f))


### Code Refactoring

* project due to image changes ([193d8f7](https://github.com/prezero/myprezero-sf/commit/193d8f79224f90d46e641f0db86190f1d5eec46e))
* project due to image changes ([2aa304f](https://github.com/prezero/myprezero-sf/commit/2aa304f58f9a13a2a7c28a80e03d71e492250ea6))
* project due to image changes ([508586e](https://github.com/prezero/myprezero-sf/commit/508586eb2c5d740fdaa6aa1c343a59e9c791319c))


### Continuous Integration

* switch to quay minio image ([#453](https://github.com/prezero/myprezero-sf/issues/453)) ([aeb549d](https://github.com/prezero/myprezero-sf/commit/aeb549db8579bb615820e2509490336ffe3430fc))

## [0.16.0](https://github.com/prezero/myprezero-sf/compare/v0.15.0...v0.16.0) (2025-08-02)


### Features

* **#300:** Connect database portal get order management by id ([#432](https://github.com/prezero/myprezero-sf/issues/432)) ([d35a23b](https://github.com/prezero/myprezero-sf/commit/d35a23b46f148d22dd38cd43a4f434b4c2359331))
* **#301:** connect to database get order overview ([#424](https://github.com/prezero/myprezero-sf/issues/424)) ([d3e0a18](https://github.com/prezero/myprezero-sf/commit/d3e0a18c16598bbd23a5ff516369c4f171f9bdc8))
* **#337:** add command to export all rows where business partner id is null ([#381](https://github.com/prezero/myprezero-sf/issues/381)) ([2d4ab5b](https://github.com/prezero/myprezero-sf/commit/2d4ab5b44af29b028cd641c467c365bbb40f6451))
* **#338:** add command to delete all rows where business partner id is null  ([#383](https://github.com/prezero/myprezero-sf/issues/383)) ([73223ef](https://github.com/prezero/myprezero-sf/commit/73223efc7af0089be7a56d5d63f5bc3c84272dd3))
* **#367:** connect to database - get container options ([#440](https://github.com/prezero/myprezero-sf/issues/440)) ([338d087](https://github.com/prezero/myprezero-sf/commit/338d08705a18d90623d3fb0e073fe1ccc869e0ba))
* **#388:** add order material option endpoint ([#439](https://github.com/prezero/myprezero-sf/issues/439)) ([a63d7f6](https://github.com/prezero/myprezero-sf/commit/a63d7f610500b90ebcc36f4a52654e45c8d7abb7))
* **#431:** add service type option endpoint ([#449](https://github.com/prezero/myprezero-sf/issues/449)) ([2c84caa](https://github.com/prezero/myprezero-sf/commit/2c84caa0b81a1e3f8f1c2d1b997e4a732c24d415))
* Update portal user endpoint dummies ([#445](https://github.com/prezero/myprezero-sf/issues/445)) ([4272459](https://github.com/prezero/myprezero-sf/commit/42724597647aab51d798685784ae265d891c642a))


### Bug Fixes

* **#308:** delete supplier id ([#442](https://github.com/prezero/myprezero-sf/issues/442)) ([e3602aa](https://github.com/prezero/myprezero-sf/commit/e3602aa496f5bbe142904a2ffffac114afd40f45))
* **#370:** contract options display value ([#419](https://github.com/prezero/myprezero-sf/issues/419)) ([741aee9](https://github.com/prezero/myprezero-sf/commit/741aee9a646ef997d8b9ae22de31b5aa3c202627))
* **deps:** update composer packages ([#438](https://github.com/prezero/myprezero-sf/issues/438)) ([f4d48bc](https://github.com/prezero/myprezero-sf/commit/f4d48bca934a276304e0d071a4b0325514216e79))
* **deps:** update symfony packages to v7.3.2 ([#451](https://github.com/prezero/myprezero-sf/issues/451)) ([4a66432](https://github.com/prezero/myprezero-sf/commit/4a66432d6c682f33573edc78d847fddf397c5d60))
* entity final ([#455](https://github.com/prezero/myprezero-sf/issues/455)) ([fbb36d9](https://github.com/prezero/myprezero-sf/commit/fbb36d90a48d0c3af62ceed1a603769b47cb73ec))
* test error ([56376dc](https://github.com/prezero/myprezero-sf/commit/56376dc3aa635762be8d87b62c2d2ae70219457d))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.32.1 ([#437](https://github.com/prezero/myprezero-sf/issues/437)) ([7dc9488](https://github.com/prezero/myprezero-sf/commit/7dc948829095ba489a986db815de138685540b80))
* **deps:** update prezero/workflows action to v1.32.2 ([#441](https://github.com/prezero/myprezero-sf/issues/441)) ([83dcc37](https://github.com/prezero/myprezero-sf/commit/83dcc37f1f28d70f5613c3a965be3935ca0e9c7e))
* run full test data on deployment ([f60047d](https://github.com/prezero/myprezero-sf/commit/f60047d3dd925d012d7c2c586f94a178eeeed293))


### Code Refactoring

* apply orm pagination and join by enum ([#422](https://github.com/prezero/myprezero-sf/issues/422)) ([fc1f099](https://github.com/prezero/myprezero-sf/commit/fc1f099cfab2e4689348d6400a8673d56ef969c8))
* env ([1511429](https://github.com/prezero/myprezero-sf/commit/1511429c65677738f0da20d32824d61e3db62800))
* env ([2910464](https://github.com/prezero/myprezero-sf/commit/291046488569e3a2304da28ef04c8704a21f0aa6))


### Continuous Integration

* add dev layer ([07d41cf](https://github.com/prezero/myprezero-sf/commit/07d41cf80a8c48fe942bf8196e6afcbfb22bbbd0))
* add dev layer ([21cb0dd](https://github.com/prezero/myprezero-sf/commit/21cb0dd3d82e1206bc0950d4c78872f214d62ab0))
* adjust concurrency ([5b71191](https://github.com/prezero/myprezero-sf/commit/5b71191d0c79bb3a967aad78870d2788b5d439c2))
* rename localdev to fixdev for hautelook in api-tests ([bdbc215](https://github.com/prezero/myprezero-sf/commit/bdbc2159207b2e1437787d17d46bf63c192ab2fe))
* rename localdev to fixdev for hautelook in run-tests ([a50cd87](https://github.com/prezero/myprezero-sf/commit/a50cd872417b7e9bf5882fed1a9afc93758e800f))

## [0.15.0](https://github.com/prezero/myprezero-sf/compare/v0.14.1...v0.15.0) (2025-07-28)


### Features

* **#323:** add co2 report endpoint ([#421](https://github.com/prezero/myprezero-sf/issues/421)) ([28988cf](https://github.com/prezero/myprezero-sf/commit/28988cffe810df5f81788b2222e92bbc454c8eac))
* feedback frontend team ([b253f27](https://github.com/prezero/myprezero-sf/commit/b253f27a7d01170f8b612d749cd7735cf4d36100))


### Code Refactoring

* api ([1f86327](https://github.com/prezero/myprezero-sf/commit/1f86327a525a0d783198b25b75d6fb2ca5b30bb0))
* api ([cb8488a](https://github.com/prezero/myprezero-sf/commit/cb8488ac6a56f7ff21700e3d1673eabd3e1d4e56))
* project ([e53b3b8](https://github.com/prezero/myprezero-sf/commit/e53b3b828f1bbfe658fd6814daf848d21678a4cc))

## [0.14.1](https://github.com/prezero/myprezero-sf/compare/v0.14.0...v0.14.1) (2025-07-27)


### Bug Fixes

* **deps:** update composer packages ([#397](https://github.com/prezero/myprezero-sf/issues/397)) ([c0fddc0](https://github.com/prezero/myprezero-sf/commit/c0fddc0f8a70f180c83f54c4f550dea4885a8ebb))
* order repository join condition ([#423](https://github.com/prezero/myprezero-sf/issues/423)) ([f4f60ad](https://github.com/prezero/myprezero-sf/commit/f4f60ad939e4e5512fcd1f39746439af46a7812b))
* test ([#426](https://github.com/prezero/myprezero-sf/issues/426)) ([506aabc](https://github.com/prezero/myprezero-sf/commit/506aabcc91f13746e2c610c873537e18ca9204ae))


### Miscellaneous Chores

* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v1.1.2 ([#405](https://github.com/prezero/myprezero-sf/issues/405)) ([d3fa657](https://github.com/prezero/myprezero-sf/commit/d3fa6575571dd34a8a75210dfe69499cf3cf9471))
* **deps:** update local docker-compose env dependencies ([#396](https://github.com/prezero/myprezero-sf/issues/396)) ([047ced6](https://github.com/prezero/myprezero-sf/commit/047ced661037408998a39ff80035c90defbc3890))
* **deps:** update prezero/workflows action to v1.32.0 ([#401](https://github.com/prezero/myprezero-sf/issues/401)) ([fc694d7](https://github.com/prezero/myprezero-sf/commit/fc694d72c9b6e552077a3a4069990efbd025b0b3))

## [0.14.0](https://github.com/prezero/myprezero-sf/compare/v0.13.0...v0.14.0) (2025-07-25)


### Features

* **#372:** add get options for service type endpoint ([#403](https://github.com/prezero/myprezero-sf/issues/403)) ([68cee40](https://github.com/prezero/myprezero-sf/commit/68cee407becac895c333a5394d2e49c84c6cb7cb))
* **#385:** add partner role enum ([#420](https://github.com/prezero/myprezero-sf/issues/420)) ([8f5a69f](https://github.com/prezero/myprezero-sf/commit/8f5a69f5081c00c126c488b75ada0875f909c81f))
* extend translations ([d416f5e](https://github.com/prezero/myprezero-sf/commit/d416f5e76018bb30e7fbd2a0afcdf50016d3fb30))


### Bug Fixes

* option selection ([e6039f5](https://github.com/prezero/myprezero-sf/commit/e6039f520af4ea30b2ab577bb27952092fc164d7))
* order endpoint ([ff66e7d](https://github.com/prezero/myprezero-sf/commit/ff66e7d49412b8e84b6860864d382b19caf75041))
* order endpoint ([38fde19](https://github.com/prezero/myprezero-sf/commit/38fde1921ef2a62271b00d764eefb5950a5315e1))
* order-date filter ([2eaea30](https://github.com/prezero/myprezero-sf/commit/2eaea30835eef24fa8b22da9b1ed2b4dca6578f4))
* remove full download path ([47089df](https://github.com/prezero/myprezero-sf/commit/47089df781ce5a7382631ec34521ffc6e40dc7cc))
* service location error ([d0ca35c](https://github.com/prezero/myprezero-sf/commit/d0ca35c7d461f18bc902a43649e02911b5713057))
* waste statistic ([40e7a4e](https://github.com/prezero/myprezero-sf/commit/40e7a4e0aff2fb161b6d320201ae7f0b77547fc9))


### Miscellaneous Chores

* merge conflict ([ecdeac8](https://github.com/prezero/myprezero-sf/commit/ecdeac8d51a36665103b52e99a13cff5f65846c1))

## [0.13.0](https://github.com/prezero/myprezero-sf/compare/v0.12.0...v0.13.0) (2025-07-16)


### Features

* **#281:** Remove enpoint get order by ([#380](https://github.com/prezero/myprezero-sf/issues/380)) ([2283aa9](https://github.com/prezero/myprezero-sf/commit/2283aa940463360dd81458ab09d85f9525e951e3))
* **#284:** Connect to database patch order ([#377](https://github.com/prezero/myprezero-sf/issues/377)) ([e0b6742](https://github.com/prezero/myprezero-sf/commit/e0b67428694b810f9100ccbb7c9f00087eefa78a))
* add filters for service type option ([db6df30](https://github.com/prezero/myprezero-sf/commit/db6df30eff468a41d80648aa0e31d2aea8029caa))
* add material option ([9e89d8b](https://github.com/prezero/myprezero-sf/commit/9e89d8b41ca98bf939409bf30cd58d89d0fefe7b))
* add waste eco savings ([#389](https://github.com/prezero/myprezero-sf/issues/389)) ([4e45656](https://github.com/prezero/myprezero-sf/commit/4e456560a3041ba0d23e5ec9404084cf454f6399))


### Bug Fixes

* endpoints ([2e0be3c](https://github.com/prezero/myprezero-sf/commit/2e0be3cc59024e2384cba06ca5928da4c0186a12))
* errors ([cc4caad](https://github.com/prezero/myprezero-sf/commit/cc4caadfd4f17e2d7b1c7fe534aeb3a379a1fe09))
* remove dashboard pagination ([b3432ef](https://github.com/prezero/myprezero-sf/commit/b3432ef5122a16dce379bf7776f70d6f26656ffb))


### Miscellaneous Chores

* add fake data for emission data ([01f6648](https://github.com/prezero/myprezero-sf/commit/01f66487547a6bd83cbac14ef9ec96fa292ff666))
* add todo comment ([aee9cca](https://github.com/prezero/myprezero-sf/commit/aee9cca250784fc57ca3e693e116b2ebfb4ddf3f))
* merge conflict ([203b58e](https://github.com/prezero/myprezero-sf/commit/203b58e40fca6638edb69cd2064d9345ea74d0e9))

## [0.12.0](https://github.com/prezero/myprezero-sf/compare/v0.11.0...v0.12.0) (2025-07-16)


### Features

* **#283:** add datebase connection for post orders ([#375](https://github.com/prezero/myprezero-sf/issues/375)) ([93fa074](https://github.com/prezero/myprezero-sf/commit/93fa074dce990fe7ce047f7ad6f9cbdee0b6c91b))
* **335:** rework test group suite ([4ef3aed](https://github.com/prezero/myprezero-sf/commit/4ef3aedb0ca2f76bbcf13b1b368c6bb0ce5ab733))
* **336:** rework test suite ([dbdf8ed](https://github.com/prezero/myprezero-sf/commit/dbdf8eda13e83f2ceaa1130e079347979019e55b))
* add sueprvision downloads ([336313a](https://github.com/prezero/myprezero-sf/commit/336313aebe73f90eb1bbd1e29dade7d07b3f89a9))
* change header ([cf05637](https://github.com/prezero/myprezero-sf/commit/cf05637b27a14389f9859893ef192679b2821d83))
* connect supervision status ([1136bec](https://github.com/prezero/myprezero-sf/commit/1136bec6faa9071b9d03d7ee2f2ffd8cea21501c))
* connect verification status ([76303b3](https://github.com/prezero/myprezero-sf/commit/76303b30ca0609814e02c6806878794755bfe0ca))
* extend endpoints ([45511fe](https://github.com/prezero/myprezero-sf/commit/45511fe2765a4cdbd1bc8b71835981dc250a6d7c))
* fix endpoint ([7da1d01](https://github.com/prezero/myprezero-sf/commit/7da1d012d3adaf09bed8608678473f93bc8c2b05))
* update dashboard endpoints ([8cf0c32](https://github.com/prezero/myprezero-sf/commit/8cf0c3217c823992de77cced8db05452fb3aa8ed))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.349.3 ([#362](https://github.com/prezero/myprezero-sf/issues/362)) ([d21d69b](https://github.com/prezero/myprezero-sf/commit/d21d69b92016aa57b715a2b0b1e0d68d287ec129))
* filters and add feedback ([#384](https://github.com/prezero/myprezero-sf/issues/384)) ([5bed8e4](https://github.com/prezero/myprezero-sf/commit/5bed8e4030fb1e91b2368671610e738b500009e0))
* order gets correct service location ([#386](https://github.com/prezero/myprezero-sf/issues/386)) ([9b21ec1](https://github.com/prezero/myprezero-sf/commit/9b21ec1fc3bffecd65ce38b13cd3b6736321cee0))


### Miscellaneous Chores

* **deps:** update ghcr.io/prezero/keycloak docker tag to v2.3.0 ([#378](https://github.com/prezero/myprezero-sf/issues/378)) ([d737e33](https://github.com/prezero/myprezero-sf/commit/d737e3349246a0898e4b01ba8762d5f51cc0d258))
* **deps:** update prezero/workflows action to v1.28.0 ([#379](https://github.com/prezero/myprezero-sf/issues/379)) ([0fba627](https://github.com/prezero/myprezero-sf/commit/0fba62702d4d166b98c0e474266b14246ac02de2))
* **deps:** update rabbitmq:4.1-management docker digest to 826db87 ([#382](https://github.com/prezero/myprezero-sf/issues/382)) ([c983b24](https://github.com/prezero/myprezero-sf/commit/c983b24607e87dab856e753b8586f9eba2f8f311))

## [0.11.0](https://github.com/prezero/myprezero-sf/compare/v0.10.0...v0.11.0) (2025-07-12)


### Features

* **#282:** Conntect to database get order ([#347](https://github.com/prezero/myprezero-sf/issues/347)) ([4f667ae](https://github.com/prezero/myprezero-sf/commit/4f667ae4dd91dd641c9a99bec36d8826207336ef))
* **#285:** add search, filter, pagination & get endpoint to order date ([#358](https://github.com/prezero/myprezero-sf/issues/358)) ([d5a4ff4](https://github.com/prezero/myprezero-sf/commit/d5a4ff404013568b0774b2b30b9558f0b71d8587))
* Update endpoints for service product ([#311](https://github.com/prezero/myprezero-sf/issues/311)) ([dff12ec](https://github.com/prezero/myprezero-sf/commit/dff12ec7bfd198b5e68f1770b5cc44e1cac7b6a0))


### Bug Fixes

* add unit to supervision document amount ([#361](https://github.com/prezero/myprezero-sf/issues/361)) ([ab5ea67](https://github.com/prezero/myprezero-sf/commit/ab5ea67e5afb4e8a188a18732ad13a506c54bb15))
* **deps:** update composer packages ([#357](https://github.com/prezero/myprezero-sf/issues/357)) ([5d13cea](https://github.com/prezero/myprezero-sf/commit/5d13cea8de81de06b94507b4da085220cd1be20f))
* locale ([163a302](https://github.com/prezero/myprezero-sf/commit/163a302810333b8a6375c783995915788062414c))
* merge ([f2a9d01](https://github.com/prezero/myprezero-sf/commit/f2a9d0168b6d5dd6bee71df0e3aa25bc1a218db4))
* replace incorrect date for supervision document ([#354](https://github.com/prezero/myprezero-sf/issues/354)) ([3dae8d8](https://github.com/prezero/myprezero-sf/commit/3dae8d89640b49a13f7b5b6cab550fe20258f06d))
* tests ([ecc8c63](https://github.com/prezero/myprezero-sf/commit/ecc8c631a4e296bda38de8b048c0ffe96a60fbce))
* tests ([a27d605](https://github.com/prezero/myprezero-sf/commit/a27d60542ec75a5ff3c306e0c716a33e44873fd4))
* tests ([92b0379](https://github.com/prezero/myprezero-sf/commit/92b0379209c1662e98edf77e5aa4ec9a08795e88))
* tests ([ca55142](https://github.com/prezero/myprezero-sf/commit/ca551426b809f62438ffc9b5ce6ac49b4354bd42))


### Miscellaneous Chores

* **deps:** update bitnami/minio docker tag to v2025.6.13 ([#356](https://github.com/prezero/myprezero-sf/issues/356)) ([6401260](https://github.com/prezero/myprezero-sf/commit/64012609d11a15b425f9aa73bbb5482787b86aa2))
* **deps:** update dependency friendsofphp/php-cs-fixer to v3.77.0 ([#352](https://github.com/prezero/myprezero-sf/issues/352)) ([96aff65](https://github.com/prezero/myprezero-sf/commit/96aff65549b9470264565c2def25f7d35f4025a9))


### Code Refactoring

* rector ([1aa8b41](https://github.com/prezero/myprezero-sf/commit/1aa8b41c034474a33548577f497c0c809a5f075a))

## [0.10.0](https://github.com/prezero/myprezero-sf/compare/v0.9.0...v0.10.0) (2025-07-08)


### Features

* integrate keycloak admin & import users ([#345](https://github.com/prezero/myprezero-sf/issues/345)) ([98baaf4](https://github.com/prezero/myprezero-sf/commit/98baaf4b568257f7994a81e7c52015a346a40e0d))


### Bug Fixes

* **deps:** update composer packages ([#343](https://github.com/prezero/myprezero-sf/issues/343)) ([5d09a06](https://github.com/prezero/myprezero-sf/commit/5d09a069c8bfd1428a3cc1c49d1e22e8c2de89f9))
* **deps:** update dependency aws/aws-sdk-php to v3.349.1 ([#327](https://github.com/prezero/myprezero-sf/issues/327)) ([e39c297](https://github.com/prezero/myprezero-sf/commit/e39c297b8ed84deb9af4522b63b86ba6154b7b85))
* **deps:** update dependency prezero/api-bundle to 1.12.* ([#244](https://github.com/prezero/myprezero-sf/issues/244)) ([860e7da](https://github.com/prezero/myprezero-sf/commit/860e7daeb233d5bb54e8f17a87542f2ae9f3e2e3))
* **deps:** update dependency symfony/flex to v2.8.1 ([#346](https://github.com/prezero/myprezero-sf/issues/346)) ([cf3e0ac](https://github.com/prezero/myprezero-sf/commit/cf3e0ac49d60fc7a6d7dcfbf24bfeca0e5519350))
* resource fix ([89f878f](https://github.com/prezero/myprezero-sf/commit/89f878f7b28ca8f23b611dee56783bf2a6738437))
* resource fix ([34a67b7](https://github.com/prezero/myprezero-sf/commit/34a67b7fbf919819739dff32078d1cc737320774))
* resource fix ([6db33f1](https://github.com/prezero/myprezero-sf/commit/6db33f1886687c0e00f74b6f1d1dc30debc571a6))
* resource fix ([01e2196](https://github.com/prezero/myprezero-sf/commit/01e21967d9945b4d93d0765623ecb8d2c0dabe73))
* Resource uuid ([a8d60f0](https://github.com/prezero/myprezero-sf/commit/a8d60f017e35bc470520637ff0a8930945914e38))
* Resource uuid ([e49aa59](https://github.com/prezero/myprezero-sf/commit/e49aa592b8c36f8e861111b5a142c25477ac4d49))
* service fix ([ec7a6c9](https://github.com/prezero/myprezero-sf/commit/ec7a6c9b471ac3f83993bc64fe0e002cfaabb74e))
* service/order fix ([8d308e8](https://github.com/prezero/myprezero-sf/commit/8d308e8e28d22762f76761819042ff7693fb58c9))
* supervision document ([430de9d](https://github.com/prezero/myprezero-sf/commit/430de9ddc32591a7b4ac741e2da85433c0c65d24))


### Miscellaneous Chores

* **deps:** update dependency symfony/maker-bundle to v1.64.0 ([#321](https://github.com/prezero/myprezero-sf/issues/321)) ([93a2b73](https://github.com/prezero/myprezero-sf/commit/93a2b73acef5a897b00e4bdeb23e14c4e875d92d))
* **deps:** update ghcr.io/prezero/keycloak docker tag to v2.2.0 ([#245](https://github.com/prezero/myprezero-sf/issues/245)) ([9172fec](https://github.com/prezero/myprezero-sf/commit/9172fec5024c2eaaab757b427c51d1f05df76e7e))
* **deps:** update ghcr.io/prezero/keycloak docker tag to v2.2.1 ([#344](https://github.com/prezero/myprezero-sf/issues/344)) ([3c61325](https://github.com/prezero/myprezero-sf/commit/3c613258112020fd7f10c7c4d44292e190416433))
* **deps:** update ghcr.io/prezero/keycloak docker tag to v2.2.2 ([#349](https://github.com/prezero/myprezero-sf/issues/349)) ([fe794b7](https://github.com/prezero/myprezero-sf/commit/fe794b7a8e721fd95d453fc667baf98dd450724a))
* **deps:** update prezero/workflows action to v1.27.0 ([#350](https://github.com/prezero/myprezero-sf/issues/350)) ([50bfc57](https://github.com/prezero/myprezero-sf/commit/50bfc570079fb7b9ca08eeb66ce0720e6e25ba62))
* **deps:** update rabbitmq:4.1-management docker digest to e1b9b48 ([#246](https://github.com/prezero/myprezero-sf/issues/246)) ([31766bc](https://github.com/prezero/myprezero-sf/commit/31766bca231d98fa9d118270f71d72c5d44d90f4))
* **deps:** update rabbitmq:4.1-management docker digest to fe0d9b5 ([#326](https://github.com/prezero/myprezero-sf/issues/326)) ([160fddf](https://github.com/prezero/myprezero-sf/commit/160fddf5059ae0b3afe2c24803c4b9a5c52a2732))


### Code Refactoring

* **#226:** replace relations with field & index ([#247](https://github.com/prezero/myprezero-sf/issues/247)) ([cfa747a](https://github.com/prezero/myprezero-sf/commit/cfa747afae1f021610ebaef2f164eea54e8ac5e4))


### Tests

* resource ([1ad37f3](https://github.com/prezero/myprezero-sf/commit/1ad37f3fcd08353b1e857b9630eedc11e9d45f06))

## [0.9.0](https://github.com/prezero/myprezero-sf/compare/v0.8.0...v0.9.0) (2025-07-02)


### Features

* **#225:** add portal endpoints supplier and service product ([#242](https://github.com/prezero/myprezero-sf/issues/242)) ([e7ece62](https://github.com/prezero/myprezero-sf/commit/e7ece62e3c2d2abd045fad2820a55ab5719a8c79))
* **#238:** connect resources to database ([#237](https://github.com/prezero/myprezero-sf/issues/237)) ([6ed4e17](https://github.com/prezero/myprezero-sf/commit/6ed4e1749fc0af933074836ed061caaf5111a917))
* add keycloak auto discovery ([#215](https://github.com/prezero/myprezero-sf/issues/215)) ([4de27b9](https://github.com/prezero/myprezero-sf/commit/4de27b9a0318d762adaf44c734d359e3b9333e3c))
* add portal endpoint for order overview ([#212](https://github.com/prezero/myprezero-sf/issues/212)) ([18bd14c](https://github.com/prezero/myprezero-sf/commit/18bd14ccdc77c84d37fb94a54b42cef9b1a5d315))
* connect portal invoice to database ([#224](https://github.com/prezero/myprezero-sf/issues/224)) ([9c66422](https://github.com/prezero/myprezero-sf/commit/9c66422e4f53d3227a526912f5d264cedc9ba5d7))


### Bug Fixes

* Dashboard Endpoint ([42eb4c7](https://github.com/prezero/myprezero-sf/commit/42eb4c7a1e3a8b06adc63f580b0f19361b352098))
* **deps:** update composer packages ([#208](https://github.com/prezero/myprezero-sf/issues/208)) ([1af0476](https://github.com/prezero/myprezero-sf/commit/1af04763464e9b31d0fe4d2f411988dce4231911))
* **deps:** update composer packages ([#221](https://github.com/prezero/myprezero-sf/issues/221)) ([fbebf04](https://github.com/prezero/myprezero-sf/commit/fbebf04c1758b3cd0db73a158b7af1000da2479b))
* **deps:** update composer packages ([#239](https://github.com/prezero/myprezero-sf/issues/239)) ([e9362ca](https://github.com/prezero/myprezero-sf/commit/e9362ca57a66489baa6d72395f2adb15546318b8))
* **deps:** update dependency aws/aws-sdk-php to v3.344.3 ([#204](https://github.com/prezero/myprezero-sf/issues/204)) ([7f318d9](https://github.com/prezero/myprezero-sf/commit/7f318d982047b64b6e9f0b093eb30cc268cadba2))
* **deps:** update dependency prezero/api-bundle to v1.10.1 ([#205](https://github.com/prezero/myprezero-sf/issues/205)) ([bd88fee](https://github.com/prezero/myprezero-sf/commit/bd88fee4637bca06dbdc3a404a801e9b54ec80db))
* **deps:** update dependency prezero/api-bundle to v1.11.1 ([#211](https://github.com/prezero/myprezero-sf/issues/211)) ([139d01a](https://github.com/prezero/myprezero-sf/commit/139d01abb1097c8f4f21851d0b56c5fb000e329e))
* **deps:** update dependency prezero/api-bundle to v1.12.0 ([#241](https://github.com/prezero/myprezero-sf/issues/241)) ([dda4fe0](https://github.com/prezero/myprezero-sf/commit/dda4fe0b06a95dc2f3e6a2a31b9b7d725c579941))
* **deps:** update dependency symfony/validator to v7.3.1 ([#220](https://github.com/prezero/myprezero-sf/issues/220)) ([3121b8a](https://github.com/prezero/myprezero-sf/commit/3121b8ae78a053b6f04926b1bd4f1cd3fe1bc151))
* **deps:** update symfony packages to v7.3.1 ([#218](https://github.com/prezero/myprezero-sf/issues/218)) ([026d00c](https://github.com/prezero/myprezero-sf/commit/026d00c7cf73b7f9f41e0764c74d06c4926d23fc))
* **deps:** update symfony packages to v7.3.1 ([#240](https://github.com/prezero/myprezero-sf/issues/240)) ([93a107d](https://github.com/prezero/myprezero-sf/commit/93a107d143e67c835299b6770e66043bec6e367e))
* env issue ([8db8294](https://github.com/prezero/myprezero-sf/commit/8db8294db50ccdb58d5fd6e06238f72b272fd5a1))
* sap api service endpoint ([#234](https://github.com/prezero/myprezero-sf/issues/234)) ([98e2a51](https://github.com/prezero/myprezero-sf/commit/98e2a512816e90e3fb507d9110d3098afb0c412d))
* serializer ([#206](https://github.com/prezero/myprezero-sf/issues/206)) ([abee1b1](https://github.com/prezero/myprezero-sf/commit/abee1b19d3bdafa8de9e7d1746e78182eedac898))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.24.1 ([#203](https://github.com/prezero/myprezero-sf/issues/203)) ([981840d](https://github.com/prezero/myprezero-sf/commit/981840dfe993f7d36b05871777bd60b23c3129ff))
* **deps:** update prezero/workflows action to v1.25.0 ([#210](https://github.com/prezero/myprezero-sf/issues/210)) ([6e6ba2d](https://github.com/prezero/myprezero-sf/commit/6e6ba2d914421d23ff01604d0e822313461e9841))
* **deps:** update prezero/workflows action to v1.25.2 ([#216](https://github.com/prezero/myprezero-sf/issues/216)) ([ca1867e](https://github.com/prezero/myprezero-sf/commit/ca1867e25c335eeec4256cfcbcfd89ee08ac05ce))
* **deps:** update prezero/workflows action to v1.26.0 ([#223](https://github.com/prezero/myprezero-sf/issues/223)) ([d73e4fe](https://github.com/prezero/myprezero-sf/commit/d73e4fe19bb5cc40e1e384af0fddab7577ee296c))
* **deps:** update rabbitmq:4.1-management docker digest to 9d67e45 ([#209](https://github.com/prezero/myprezero-sf/issues/209)) ([a7ec562](https://github.com/prezero/myprezero-sf/commit/a7ec5626812af3c71e6211c91d4fd64293880c97))
* **deps:** update rabbitmq:4.1-management docker digest to bb02d38 ([#243](https://github.com/prezero/myprezero-sf/issues/243)) ([7a2e511](https://github.com/prezero/myprezero-sf/commit/7a2e511834028fda50ba7371f32cbe4ce5017013))


### Code Refactoring

* rework database, tests etc based on real test data ([#214](https://github.com/prezero/myprezero-sf/issues/214)) ([0dfa669](https://github.com/prezero/myprezero-sf/commit/0dfa669443ffaf4415f9be20be2cbec53bf9726c))
* rework sap europe api endpoints ([#217](https://github.com/prezero/myprezero-sf/issues/217)) ([5083ac8](https://github.com/prezero/myprezero-sf/commit/5083ac81c48cc79aca20d4b99551a95cdbbc1040))
* rework sap europe api endpoints ([#233](https://github.com/prezero/myprezero-sf/issues/233)) ([5071360](https://github.com/prezero/myprezero-sf/commit/5071360df03f4f544fcbac441787de9fba64c88a))
* rework test suite ([#219](https://github.com/prezero/myprezero-sf/issues/219)) ([4979006](https://github.com/prezero/myprezero-sf/commit/4979006e62721c1849ef050fd39510c4da71380c))
* tenant ([#222](https://github.com/prezero/myprezero-sf/issues/222)) ([b49e4a3](https://github.com/prezero/myprezero-sf/commit/b49e4a34b1bc271160ca9f4f079915f87b156f1f))
* test suite ([#235](https://github.com/prezero/myprezero-sf/issues/235)) ([fa96b6c](https://github.com/prezero/myprezero-sf/commit/fa96b6cc9626737ea15fd50ab03afaaeb18583e5))


### Tests

* cleanup & refactor ([#207](https://github.com/prezero/myprezero-sf/issues/207)) ([3f53c84](https://github.com/prezero/myprezero-sf/commit/3f53c8454f1a286a37be0bd97a605e086969494c))

## [0.8.0](https://github.com/prezero/myprezero-sf/compare/v0.7.0...v0.8.0) (2025-06-11)


### Features

* add x-client-id check ([3d1c03a](https://github.com/prezero/myprezero-sf/commit/3d1c03a7a2a134e5ddea6ed2c0feb3a745690972))
* change client-id header ([69c0e58](https://github.com/prezero/myprezero-sf/commit/69c0e5825a849cc00522c30af16c58fa9dc63423))
* change client-id header ([d339f76](https://github.com/prezero/myprezero-sf/commit/d339f76210861e7c5f486faf23d411f17982a9c9))
* change translation endpoint ([eddea88](https://github.com/prezero/myprezero-sf/commit/eddea8811731d9a97f9ebf98a840a06553750bca))


### Bug Fixes

* add x-client-id header to cors ([12e5c32](https://github.com/prezero/myprezero-sf/commit/12e5c323b9191d9e9b2741bcc5c7b520f273a4ad))
* cors regex ([55ca6a6](https://github.com/prezero/myprezero-sf/commit/55ca6a660ea36e7330e9ccc250fd49cd9c3e1c97))
* cors regex ([cf3b480](https://github.com/prezero/myprezero-sf/commit/cf3b4803631217e387b79467e7acf10bc6bdcfaa))
* **deps:** update composer packages ([#195](https://github.com/prezero/myprezero-sf/issues/195)) ([607386e](https://github.com/prezero/myprezero-sf/commit/607386e6c5b44e79455dcb0cdc822f81e99ed7ea))
* **deps:** update dependency prezero/api-bundle to v1.10.0 ([#201](https://github.com/prezero/myprezero-sf/issues/201)) ([a3fd9b1](https://github.com/prezero/myprezero-sf/commit/a3fd9b14d89d0f4d8ee067018ec8d788ad32452e))
* logging ([0d63019](https://github.com/prezero/myprezero-sf/commit/0d630197a6db2ea813b0fbe946ed69abb29780b5))
* logging ([2e37d2f](https://github.com/prezero/myprezero-sf/commit/2e37d2f80df115d1d3154025828d792332b7954d))
* sap api ([394f6f9](https://github.com/prezero/myprezero-sf/commit/394f6f93ca2ebf8d096f2e7862bdf568afc04c2d))
* sap europe test ([be03de4](https://github.com/prezero/myprezero-sf/commit/be03de4a344d7b108f30a0631376b620a2468e86))


### Miscellaneous Chores

* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v1.1.1 ([#194](https://github.com/prezero/myprezero-sf/issues/194)) ([3b2c904](https://github.com/prezero/myprezero-sf/commit/3b2c9044e6a3cc4435d520eda3186d0064f2e366))
* **deps:** update local docker-compose env dependencies ([#193](https://github.com/prezero/myprezero-sf/issues/193)) ([efc9d89](https://github.com/prezero/myprezero-sf/commit/efc9d898a8cfb42350f855a79e46f692115dbfab))


### Tests

* add additional portal tests ([#200](https://github.com/prezero/myprezero-sf/issues/200)) ([0530f82](https://github.com/prezero/myprezero-sf/commit/0530f82707daa5d3b11f37634035e13ad9266cdc))

## [0.7.0](https://github.com/prezero/myprezero-sf/compare/v0.6.3...v0.7.0) (2025-06-09)


### Features

* update open api specification ([#196](https://github.com/prezero/myprezero-sf/issues/196)) ([4dc5c23](https://github.com/prezero/myprezero-sf/commit/4dc5c231106c2720ccd64aa5d0d79c419c9690df))


### Miscellaneous Chores

* update README ([#198](https://github.com/prezero/myprezero-sf/issues/198)) ([75d22b2](https://github.com/prezero/myprezero-sf/commit/75d22b26bcde9ffdb38dd60ed2573b0dda5e82d0))

## [0.6.3](https://github.com/prezero/myprezero-sf/compare/v0.6.2...v0.6.3) (2025-06-04)


### Bug Fixes

* **deps:** update composer packages ([#186](https://github.com/prezero/myprezero-sf/issues/186)) ([14b43fa](https://github.com/prezero/myprezero-sf/commit/14b43fa5a68a898fdd4e8e32d27472091f04776a))
* **deps:** update dependency aws/aws-sdk-php to v3.343.13 ([#184](https://github.com/prezero/myprezero-sf/issues/184)) ([da4c77b](https://github.com/prezero/myprezero-sf/commit/da4c77b8604d56af94b25a199a190af5f43144a0))
* **deps:** update dependency symfony/flex to v2.7.1 ([#188](https://github.com/prezero/myprezero-sf/issues/188)) ([2e0b208](https://github.com/prezero/myprezero-sf/commit/2e0b208ef1938410edf23329eb2b99b12c2d58cf))
* **deps:** update symfony packages to 7.3.* ([#192](https://github.com/prezero/myprezero-sf/issues/192)) ([7056595](https://github.com/prezero/myprezero-sf/commit/705659587cc7884f82f4fb4b9d8b725decc007ee))


### Miscellaneous Chores

* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v1 ([#191](https://github.com/prezero/myprezero-sf/issues/191)) ([5f4b994](https://github.com/prezero/myprezero-sf/commit/5f4b9945b06c39cf85e04dc0306738798c50ae34))
* **deps:** update local docker-compose env dependencies ([#189](https://github.com/prezero/myprezero-sf/issues/189)) ([f08e2ad](https://github.com/prezero/myprezero-sf/commit/f08e2ad7a1465840f07e849de6307057f5f12c4b))
* **deps:** update postgres docker tag to v17.2 ([#182](https://github.com/prezero/myprezero-sf/issues/182)) ([fa56294](https://github.com/prezero/myprezero-sf/commit/fa5629473a9291356d320c76e8a4d3bf5dd7fe9b))
* **deps:** update prezero/workflows action to v1.24.0 ([#187](https://github.com/prezero/myprezero-sf/issues/187)) ([e0db80e](https://github.com/prezero/myprezero-sf/commit/e0db80e00ab99c648f786889f0a748683d07338d))

## [0.6.2](https://github.com/prezero/myprezero-sf/compare/v0.6.1...v0.6.2) (2025-05-19)


### Bug Fixes

* **deps:** update composer packages ([#175](https://github.com/prezero/myprezero-sf/issues/175)) ([0549fa5](https://github.com/prezero/myprezero-sf/commit/0549fa51268b19749a5348d754f751eb53bc41b2))


### Miscellaneous Chores

* **deps:** update bitnami/minio:2025.4.22 docker digest to 50cec18 ([#178](https://github.com/prezero/myprezero-sf/issues/178)) ([be8d965](https://github.com/prezero/myprezero-sf/commit/be8d96526b5cc1e46327d1815331c26f187a1b3a))
* **deps:** update prezero/workflows action to v1.19.0 ([#176](https://github.com/prezero/myprezero-sf/issues/176)) ([76edd30](https://github.com/prezero/myprezero-sf/commit/76edd30cd921304b36d4c1db24b4d11bb5b4cdeb))


### Build System

* change keycloak to central image ([#181](https://github.com/prezero/myprezero-sf/issues/181)) ([be496a7](https://github.com/prezero/myprezero-sf/commit/be496a76ede612900b54bc117f7a35a533072510))


### Continuous Integration

* downgrade postgres due to renovate changes ([#179](https://github.com/prezero/myprezero-sf/issues/179)) ([7eb993c](https://github.com/prezero/myprezero-sf/commit/7eb993c7da832c9482aff155f554fbce194a8f4d))

## [0.6.1](https://github.com/prezero/myprezero-sf/compare/v0.6.0...v0.6.1) (2025-05-15)


### Bug Fixes

* **deps:** update dependency symfony/flex to v2.5.1 ([#172](https://github.com/prezero/myprezero-sf/issues/172)) ([6c50ce1](https://github.com/prezero/myprezero-sf/commit/6c50ce1d7d03f326c4e6871b6298f6c61ff7421e))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.18.1 ([#173](https://github.com/prezero/myprezero-sf/issues/173)) ([9041e62](https://github.com/prezero/myprezero-sf/commit/9041e621fdbca5f9e71c1b4f624f3db003088650))

## [0.6.0](https://github.com/prezero/myprezero-sf/compare/v0.5.0...v0.6.0) (2025-05-13)


### Features

* **#63:** sap api database integration ([#122](https://github.com/prezero/myprezero-sf/issues/122)) ([c4668f1](https://github.com/prezero/myprezero-sf/commit/c4668f19ce689d8717b4cbdedfe7cab19c93b11d))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.343.8 ([#168](https://github.com/prezero/myprezero-sf/issues/168)) ([8a9a3b3](https://github.com/prezero/myprezero-sf/commit/8a9a3b38ea1680e5f6cf20a465b6f6007baf5d67))


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.16.0 ([#169](https://github.com/prezero/myprezero-sf/issues/169)) ([af30a31](https://github.com/prezero/myprezero-sf/commit/af30a3129fd70eb262b1d750d42f222b7c380782))
* **deps:** update prezero/workflows action to v1.16.1 ([#170](https://github.com/prezero/myprezero-sf/issues/170)) ([d65f81e](https://github.com/prezero/myprezero-sf/commit/d65f81eedad19bef73a362379ffdbe77b06a3bcc))
* **deps:** update prezero/workflows action to v1.16.2 ([#171](https://github.com/prezero/myprezero-sf/issues/171)) ([5afd893](https://github.com/prezero/myprezero-sf/commit/5afd893c180d21c2a5431b15c70ffa236f4b6b8a))

## [0.5.0](https://github.com/prezero/myprezero-sf/compare/v0.4.1...v0.5.0) (2025-05-12)


### Features

* **#69:** Add dashboard endpoints ([#152](https://github.com/prezero/myprezero-sf/issues/152)) ([bd39241](https://github.com/prezero/myprezero-sf/commit/bd392416c5ae4246f9c6445dce2aa03b5b37fdd4))
* **#71:** Add fields to order dates endpoint ([#154](https://github.com/prezero/myprezero-sf/issues/154)) ([4ea6ff3](https://github.com/prezero/myprezero-sf/commit/4ea6ff34b8b0277c86b90272a28e47206c1c06c0))
* **#75:** Add endpoint for supervision document status ([#155](https://github.com/prezero/myprezero-sf/issues/155)) ([3102327](https://github.com/prezero/myprezero-sf/commit/3102327b1e6b644ab96a108f22757ef5af948a28))
* **#76:** Add endpoint for verification document number ([#156](https://github.com/prezero/myprezero-sf/issues/156)) ([50ff7b6](https://github.com/prezero/myprezero-sf/commit/50ff7b6774e6d2aa8aa7594057b30b1fe0c64579))
* **#78:** Extend verification document details ([#157](https://github.com/prezero/myprezero-sf/issues/157)) ([90499f7](https://github.com/prezero/myprezero-sf/commit/90499f7d82cd25603b221335837aa1bb98cc64e4))
* **#79:** Add endpoint for supervision document in verification ([#158](https://github.com/prezero/myprezero-sf/issues/158)) ([c04438e](https://github.com/prezero/myprezero-sf/commit/c04438e35411137020dfa60ccedfa17a9b267eca))
* Update endpoints for portal ([#153](https://github.com/prezero/myprezero-sf/issues/153)) ([77e1d00](https://github.com/prezero/myprezero-sf/commit/77e1d005e61ee15c9795f4879d41ddd8eb2a7184))


### Bug Fixes

* **deps:** update composer packages ([#123](https://github.com/prezero/myprezero-sf/issues/123)) ([ac0a8ab](https://github.com/prezero/myprezero-sf/commit/ac0a8abf3ec7993e596a613ad0160fcbde9fdebe))
* **deps:** update composer packages ([#140](https://github.com/prezero/myprezero-sf/issues/140)) ([89af46e](https://github.com/prezero/myprezero-sf/commit/89af46e1abbfced7d38ef02d802e9692dae16d93))
* **deps:** update composer packages ([#166](https://github.com/prezero/myprezero-sf/issues/166)) ([44b5e8b](https://github.com/prezero/myprezero-sf/commit/44b5e8bf82ebe3dc71d27873d5461b4b6fb718cf))
* **deps:** update dependency aws/aws-sdk-php to v3.342.30 ([#137](https://github.com/prezero/myprezero-sf/issues/137)) ([cf37bbd](https://github.com/prezero/myprezero-sf/commit/cf37bbd52b551153b57060abf912e52bb69a2c03))
* **deps:** update dependency aws/aws-sdk-php to v3.343.4 ([#161](https://github.com/prezero/myprezero-sf/issues/161)) ([3173483](https://github.com/prezero/myprezero-sf/commit/3173483b15a0bdc1f9ba96872f6c7f2234a0f4b8))
* **deps:** update dependency prezero/api-bundle to v1.4.2 ([#130](https://github.com/prezero/myprezero-sf/issues/130)) ([6996ef5](https://github.com/prezero/myprezero-sf/commit/6996ef5b96e5bbf7cb6e7222f52ca5f3a0f78aa1))
* **deps:** update dependency prezero/api-bundle to v1.5.1 ([#135](https://github.com/prezero/myprezero-sf/issues/135)) ([d8992f2](https://github.com/prezero/myprezero-sf/commit/d8992f225a5c11fcedf039f3655ca3bcea8bb655))
* **deps:** update dependency prezero/api-bundle to v1.9.0 ([#145](https://github.com/prezero/myprezero-sf/issues/145)) ([a83bd18](https://github.com/prezero/myprezero-sf/commit/a83bd18cafe6adb5b01e71ca8b67d029c69c8d5d))
* **deps:** update symfony packages to v7.2.6 ([#147](https://github.com/prezero/myprezero-sf/issues/147)) ([cb7bd12](https://github.com/prezero/myprezero-sf/commit/cb7bd12143ad8d8521ad5efbae5cb1a4d7328d46))
* remove metrics in Dockerfile due to frankenphp 1.5 update ([6c5fc6c](https://github.com/prezero/myprezero-sf/commit/6c5fc6c03f190b6188dc9f01e037632a9f60252b))
* remove metrics in Dockerfile due to frankenphp 1.5 update ([ad06129](https://github.com/prezero/myprezero-sf/commit/ad061299d4fa239617546158ec6c8b697f8ba29f))
* update api bundle ([4f146be](https://github.com/prezero/myprezero-sf/commit/4f146be7fbf317944105099cf69455c00b8ac63d))


### Miscellaneous Chores

* **deps:** update bitnami/keycloak docker tag to v26.1.5 ([#127](https://github.com/prezero/myprezero-sf/issues/127)) ([226fdbf](https://github.com/prezero/myprezero-sf/commit/226fdbf489ca2fb79b53cf611308037e373f4a32))
* **deps:** update bitnami/keycloak docker tag to v26.2.1 ([#131](https://github.com/prezero/myprezero-sf/issues/131)) ([cad9dfe](https://github.com/prezero/myprezero-sf/commit/cad9dfef0f334fa37db8d17d999734239a1681e9))
* **deps:** update bitnami/keycloak docker tag to v26.2.4 ([#146](https://github.com/prezero/myprezero-sf/issues/146)) ([f820c91](https://github.com/prezero/myprezero-sf/commit/f820c91516b62a6e46d23581b327f91da4666cc8))
* **deps:** update dependency symfony/maker-bundle to v1.63.0 ([#143](https://github.com/prezero/myprezero-sf/issues/143)) ([756dbde](https://github.com/prezero/myprezero-sf/commit/756dbde2566b2535ced1d1522bcf67ce5d0bd1c0))
* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v0.8.1 ([#141](https://github.com/prezero/myprezero-sf/issues/141)) ([d2c6df7](https://github.com/prezero/myprezero-sf/commit/d2c6df7c3586ea2a4e9f5ba2224b4c668b435a3c))
* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v0.8.2 ([#159](https://github.com/prezero/myprezero-sf/issues/159)) ([989a6b6](https://github.com/prezero/myprezero-sf/commit/989a6b64695593fc73c7557a775cb75e407b255a))
* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v0.9.1 ([#164](https://github.com/prezero/myprezero-sf/issues/164)) ([5957059](https://github.com/prezero/myprezero-sf/commit/595705957a2db2a479041b1d59dd1cd7b7c04314))
* **deps:** update postgres docker tag to v17 ([#150](https://github.com/prezero/myprezero-sf/issues/150)) ([57c4a7c](https://github.com/prezero/myprezero-sf/commit/57c4a7c1eb64fb712f9b77b57e61ca716f216b10))
* **deps:** update postgres docker tag to v17.5 ([#151](https://github.com/prezero/myprezero-sf/issues/151)) ([a212ed6](https://github.com/prezero/myprezero-sf/commit/a212ed60e35cc597d24a83ab0528483af9b66da4))
* **deps:** update prezero/workflows action to v1.10.2 ([#134](https://github.com/prezero/myprezero-sf/issues/134)) ([68bcbbd](https://github.com/prezero/myprezero-sf/commit/68bcbbdf4b304dd4665bb9c532dc7db04280125c))
* **deps:** update prezero/workflows action to v1.11.0 ([#139](https://github.com/prezero/myprezero-sf/issues/139)) ([23d53a5](https://github.com/prezero/myprezero-sf/commit/23d53a51b208bca66e86e9e07d584ea984ed1764))
* **deps:** update prezero/workflows action to v1.12.0 ([#144](https://github.com/prezero/myprezero-sf/issues/144)) ([6a8a7b4](https://github.com/prezero/myprezero-sf/commit/6a8a7b4e714ab28f465f377ad0b4380e5f2a438e))
* **deps:** update prezero/workflows action to v1.15.0 ([#163](https://github.com/prezero/myprezero-sf/issues/163)) ([a053fbf](https://github.com/prezero/myprezero-sf/commit/a053fbf1bb254bee40ce5facec0982e8c72c8ef8))
* **deps:** update prezero/workflows action to v1.6.3 ([#128](https://github.com/prezero/myprezero-sf/issues/128)) ([25c670b](https://github.com/prezero/myprezero-sf/commit/25c670be9c414bdd7afd85ec1f0062ac77f74697))
* **deps:** update rabbitmq:4.1-management docker digest to 314ed3f ([#165](https://github.com/prezero/myprezero-sf/issues/165)) ([9308755](https://github.com/prezero/myprezero-sf/commit/930875549f5890e045817af927eef6cd1cceb37f))
* **deps:** update rabbitmq:4.1-management docker digest to 6bcaebe ([#160](https://github.com/prezero/myprezero-sf/issues/160)) ([fd7e98b](https://github.com/prezero/myprezero-sf/commit/fd7e98b8fe9664e6a94552cb0535106c41ca76dd))
* **deps:** update update local docker-compose env dependencies ([#133](https://github.com/prezero/myprezero-sf/issues/133)) ([450b07f](https://github.com/prezero/myprezero-sf/commit/450b07f02af60d50227d713fe79ab11a0ec9d124))
* **deps:** update update local docker-compose env dependencies ([#148](https://github.com/prezero/myprezero-sf/issues/148)) ([7afbb11](https://github.com/prezero/myprezero-sf/commit/7afbb11c063e0425cccfa449e6492fa0ab6eeca3))


### Continuous Integration

* add snyk integration ([#162](https://github.com/prezero/myprezero-sf/issues/162)) ([d98a524](https://github.com/prezero/myprezero-sf/commit/d98a524ddbc54461662dfb37934b75007e93575f))
* add target branch for release please alt branch ([#136](https://github.com/prezero/myprezero-sf/issues/136)) ([c6fe5fc](https://github.com/prezero/myprezero-sf/commit/c6fe5fc63a1be2dd2943fc57f25743610dccd028))
* adjust pr workflow env ([#142](https://github.com/prezero/myprezero-sf/issues/142)) ([374e757](https://github.com/prezero/myprezero-sf/commit/374e75742c49de99886aafd34ee6d285484395e9))
* bump deprecated release please ([#132](https://github.com/prezero/myprezero-sf/issues/132)) ([6530937](https://github.com/prezero/myprezero-sf/commit/65309379f2d8c99e91cc9c18a3103d572aa1df30))

## [0.4.1](https://github.com/prezero/myprezero-sf/compare/v0.4.0...v0.4.1) (2025-04-11)


### Miscellaneous Chores

* **deps:** update prezero/workflows action to v1.6.2 ([#124](https://github.com/prezero/myprezero-sf/issues/124)) ([5a125d1](https://github.com/prezero/myprezero-sf/commit/5a125d10b4979355afb0229513906390653439b2))


### Continuous Integration

* renovate adjustments for vcs ([#125](https://github.com/prezero/myprezero-sf/issues/125)) ([250f504](https://github.com/prezero/myprezero-sf/commit/250f50478b0307a333e0f72f3f828163230230ef))

## [0.4.0](https://github.com/prezero/myprezero-sf/compare/v0.3.0...v0.4.0) (2025-04-10)


### Features

* **#70:** add prezero api bundle ([#115](https://github.com/prezero/myprezero-sf/issues/115)) ([377e6ef](https://github.com/prezero/myprezero-sf/commit/377e6efcc390d862520ebb4be8705c66d75f1e03))


### Bug Fixes

* **deps:** update dependency aws/aws-sdk-php to v3.342.24 ([#120](https://github.com/prezero/myprezero-sf/issues/120)) ([6bee64d](https://github.com/prezero/myprezero-sf/commit/6bee64d0b70a91774b160f1c4fa27d00dace198d))


### Miscellaneous Chores

* **deps:** update update local docker-compose env dependencies ([#119](https://github.com/prezero/myprezero-sf/issues/119)) ([f775b0d](https://github.com/prezero/myprezero-sf/commit/f775b0d1e9b0e2dadc11956443263948cffde20f))

## [0.3.0](https://github.com/prezero/myprezero-sf/compare/v0.2.2...v0.3.0) (2025-04-09)


### Features

* **#51:** update/add resources and endpoints ([#50](https://github.com/prezero/myprezero-sf/issues/50)) ([11dbfbc](https://github.com/prezero/myprezero-sf/commit/11dbfbcdf85c9963f43b9f33701bd6b93a011859))
* **#52:** extend backend api ([#55](https://github.com/prezero/myprezero-sf/issues/55)) ([044735d](https://github.com/prezero/myprezero-sf/commit/044735d64627c11088783fbccb15544b6d7b09a4))
* **#53:** Aufbau Datenbank ([#59](https://github.com/prezero/myprezero-sf/issues/59)) ([fe26fb4](https://github.com/prezero/myprezero-sf/commit/fe26fb4afe51ebd421c98b501e28af81070d3dd7))
* **#54:** frontend api ([#61](https://github.com/prezero/myprezero-sf/issues/61)) ([5648590](https://github.com/prezero/myprezero-sf/commit/56485904ba92f8edebc36d20557c897adc123e21))
* add a localdev environment to test against the dev environment ([#41](https://github.com/prezero/myprezero-sf/issues/41)) ([d7e615f](https://github.com/prezero/myprezero-sf/commit/d7e615fc15c7a488435723e1cff7bcdd6c672ba3))
* add basic portal api ([#34](https://github.com/prezero/myprezero-sf/issues/34)) ([fd603cd](https://github.com/prezero/myprezero-sf/commit/fd603cd4ae75604236e07acdc353fa2e3f90ee4c))
* add database ([#37](https://github.com/prezero/myprezero-sf/issues/37)) ([91b3e00](https://github.com/prezero/myprezero-sf/commit/91b3e00a04bca40ef807d67f7efddf75efe8c002))
* add messenger ([#36](https://github.com/prezero/myprezero-sf/issues/36)) ([46f167c](https://github.com/prezero/myprezero-sf/commit/46f167c478d1360faa4bc9cc7a2bb6fd781eddf4))
* add s3storage ([#38](https://github.com/prezero/myprezero-sf/issues/38)) ([49764ef](https://github.com/prezero/myprezero-sf/commit/49764ef2843cce0d89635eee0c3f27221bd901cf))
* add symfony mailer ([#35](https://github.com/prezero/myprezero-sf/issues/35)) ([484ff92](https://github.com/prezero/myprezero-sf/commit/484ff9210b79abdfc4acd6def21a670c3b2221f5))
* adjust codeception to generate test coverage ([#48](https://github.com/prezero/myprezero-sf/issues/48)) ([da84168](https://github.com/prezero/myprezero-sf/commit/da841689fc27856f18fd52bb416ca743e364d9d9))
* create more tests ([#58](https://github.com/prezero/myprezero-sf/issues/58)) ([4477220](https://github.com/prezero/myprezero-sf/commit/4477220b5c5a9e366c1a8a37cb120bb3d267c131))
* integrate ApiBundle ([#33](https://github.com/prezero/myprezero-sf/issues/33)) ([43a1a5e](https://github.com/prezero/myprezero-sf/commit/43a1a5ea079c0d9e4a08b8be89caed14a7a72661))


### Bug Fixes

* add env to specific the keycloak test user ([#47](https://github.com/prezero/myprezero-sf/issues/47)) ([47ac9c8](https://github.com/prezero/myprezero-sf/commit/47ac9c82af1208b029081cf0e4768470ac583a02))
* adjust dev to localdev ([#43](https://github.com/prezero/myprezero-sf/issues/43)) ([fb78bbe](https://github.com/prezero/myprezero-sf/commit/fb78bbebbe53d920df86838a90a244f46e33c8b7))
* dependencies not needed ([#60](https://github.com/prezero/myprezero-sf/issues/60)) ([cd06af4](https://github.com/prezero/myprezero-sf/commit/cd06af40a20db47137eb616158b5b4cf8aa9be9b))
* **deps:** update composer packages ([c895b28](https://github.com/prezero/myprezero-sf/commit/c895b2812f66b9e4e655d920c0aca7bac21d2d13))
* **deps:** update dependency aws/aws-sdk-php to v3.342.23 ([#102](https://github.com/prezero/myprezero-sf/issues/102)) ([0e246f9](https://github.com/prezero/myprezero-sf/commit/0e246f946970f32031ee19fbe7c33bacb56b3ad7))
* **deps:** update dependency doctrine/dbal to v4 ([#104](https://github.com/prezero/myprezero-sf/issues/104)) ([c3e301d](https://github.com/prezero/myprezero-sf/commit/c3e301da79730ab9a2cf5cc92a49577ecce65fe4))
* **deps:** update symfony packages to v7.2.5 ([#95](https://github.com/prezero/myprezero-sf/issues/95)) ([5017ea6](https://github.com/prezero/myprezero-sf/commit/5017ea60ead3dbf61e171736c3e98e5eda11ca72))
* phpstan ([2d14a23](https://github.com/prezero/myprezero-sf/commit/2d14a23cc6f0ea6bc65bb17ce3995fc020b8d560))
* tests ([0250829](https://github.com/prezero/myprezero-sf/commit/0250829989a9250ad6a5aa5d067559531caeb0de))
* update loki-promtail and prometheus-node-exporter ([#56](https://github.com/prezero/myprezero-sf/issues/56)) ([13ca54c](https://github.com/prezero/myprezero-sf/commit/13ca54cb63507094e92778ba763dabd002086a63))
* Update resource type and fixtures ([#57](https://github.com/prezero/myprezero-sf/issues/57)) ([c184bdb](https://github.com/prezero/myprezero-sf/commit/c184bdb6f9e88d8f4797357abcd601479c92f4c1))


### Miscellaneous Chores

* **deps:** pin dependencies ([#109](https://github.com/prezero/myprezero-sf/issues/109)) ([d357902](https://github.com/prezero/myprezero-sf/commit/d357902943e5bca77db2f951ab4bd891fb39e431))
* **deps:** pin dependencies ([#81](https://github.com/prezero/myprezero-sf/issues/81)) ([1318ef8](https://github.com/prezero/myprezero-sf/commit/1318ef88b43b98619d2b5bc573c419071c43c951))
* **deps:** pin dependencies ([#82](https://github.com/prezero/myprezero-sf/issues/82)) ([da79f42](https://github.com/prezero/myprezero-sf/commit/da79f423b0ddcebe2cf4d4bb2a6b968adeca42f1))
* **deps:** update bitnami/keycloak docker tag to v26.1.4 ([#85](https://github.com/prezero/myprezero-sf/issues/85)) ([76bf225](https://github.com/prezero/myprezero-sf/commit/76bf22554f3f6bc00d3508dcf9c7b1816b593464))
* **deps:** update bitnami/keycloak:26.1.0 docker digest to 70528fa ([#84](https://github.com/prezero/myprezero-sf/issues/84)) ([500de56](https://github.com/prezero/myprezero-sf/commit/500de566d3e277efe15a02e3881f1f6cbf95bf02))
* **deps:** update bitnami/keycloak:26.1.4 docker digest to cfbcd98 ([#106](https://github.com/prezero/myprezero-sf/issues/106)) ([e7dd1cb](https://github.com/prezero/myprezero-sf/commit/e7dd1cbc4b767c4c032ec77da7a450df10b3e530))
* **deps:** update bitnami/minio docker tag to v2025.4.8 ([#114](https://github.com/prezero/myprezero-sf/issues/114)) ([b9be7a2](https://github.com/prezero/myprezero-sf/commit/b9be7a276d45f9b4ff6c29c4935a85b0baba5684))
* **deps:** update bitnami/minio:latest docker digest to 8b4f6d2 ([#107](https://github.com/prezero/myprezero-sf/issues/107)) ([2a0a00d](https://github.com/prezero/myprezero-sf/commit/2a0a00d3942dd0907252f7dc8541b7db2988ffe5))
* **deps:** update dependencies ([#30](https://github.com/prezero/myprezero-sf/issues/30)) ([9879a9b](https://github.com/prezero/myprezero-sf/commit/9879a9b9cf708e0d8bc2bd1e2391d50e2313bcd6))
* **deps:** update ghcr.io/prezero/docker-images/franken docker tag to v0.7.0 ([#88](https://github.com/prezero/myprezero-sf/issues/88)) ([0fe9724](https://github.com/prezero/myprezero-sf/commit/0fe9724283516a1b064127a9af38c8f332ebad69))
* **deps:** update postgres docker tag to v16 ([#89](https://github.com/prezero/myprezero-sf/issues/89)) ([e66ca81](https://github.com/prezero/myprezero-sf/commit/e66ca81bc09326a11adc6cad85ccf8aa419978ff))
* **deps:** update postgres docker tag to v17 ([#90](https://github.com/prezero/myprezero-sf/issues/90)) ([3b1aec0](https://github.com/prezero/myprezero-sf/commit/3b1aec042dcdbd4e61120d48e36b3c38199f6a45))
* **deps:** update prezero/workflows action to v1.5.0 ([#110](https://github.com/prezero/myprezero-sf/issues/110)) ([b4b72be](https://github.com/prezero/myprezero-sf/commit/b4b72be7c82d89a126af8d6b7506b268a3419553))
* **deps:** update prezero/workflows action to v1.5.2 ([#111](https://github.com/prezero/myprezero-sf/issues/111)) ([f939a7a](https://github.com/prezero/myprezero-sf/commit/f939a7a0cc9bcd44bc6bf88492ec2710340f4360))
* **deps:** update prezero/workflows action to v1.5.3 ([#112](https://github.com/prezero/myprezero-sf/issues/112)) ([c29a265](https://github.com/prezero/myprezero-sf/commit/c29a265cfb33b09a0e5a0de5ef775ceaa9aa3a2d))
* **deps:** update prezero/workflows action to v1.5.4 ([#118](https://github.com/prezero/myprezero-sf/issues/118)) ([d30361a](https://github.com/prezero/myprezero-sf/commit/d30361a5300fdef0c7bda2a36054d1ed9e154e5d))
* **deps:** update rabbitmq:4.0-management docker digest to 0832823 ([#117](https://github.com/prezero/myprezero-sf/issues/117)) ([b6990e3](https://github.com/prezero/myprezero-sf/commit/b6990e37dde69b166c8acacfe3ac8201ec1d6d6c))
* **deps:** update update github actions (major) ([#91](https://github.com/prezero/myprezero-sf/issues/91)) ([f5afee0](https://github.com/prezero/myprezero-sf/commit/f5afee0c7a72a4747e58b6f6fdc31efcaae739c5))


### Continuous Integration

* **#108:** fixed docker versions ([#116](https://github.com/prezero/myprezero-sf/issues/116)) ([8723b76](https://github.com/prezero/myprezero-sf/commit/8723b76c75dd3eba2cc4e6d04fedb7dd0371e3c4))
* add azure devops ([#20](https://github.com/prezero/myprezero-sf/issues/20)) ([4cdff4f](https://github.com/prezero/myprezero-sf/commit/4cdff4fbd0bb078a127e50eb9b3ee389149d20a1))
* add database migration execution ([#44](https://github.com/prezero/myprezero-sf/issues/44)) ([aed886f](https://github.com/prezero/myprezero-sf/commit/aed886f64cd8688d58e8fbfdec02e9d107b258b1))
* add maintenance workflow ([#49](https://github.com/prezero/myprezero-sf/issues/49)) ([21bcb68](https://github.com/prezero/myprezero-sf/commit/21bcb68b8b02d62db2f7925c4e25a9a052bd9da8))
* add migration and tests toggle ([c4c0013](https://github.com/prezero/myprezero-sf/commit/c4c00137825de74340e58c85f460cf275c1b23bb))
* add phpstan test execution ([#42](https://github.com/prezero/myprezero-sf/issues/42)) ([f5e2f20](https://github.com/prezero/myprezero-sf/commit/f5e2f20c0668c9ff5e4625e0fcb895d44cbc0ff2))
* adjust odj reports ([#40](https://github.com/prezero/myprezero-sf/issues/40)) ([2338a41](https://github.com/prezero/myprezero-sf/commit/2338a4173bd65961e7c97da1b34c80965b290512))
* adjust the tag reference ([#67](https://github.com/prezero/myprezero-sf/issues/67)) ([1de0d69](https://github.com/prezero/myprezero-sf/commit/1de0d698cd755af00f09332849618548a91b9801))
* adjust to higher ports ([#68](https://github.com/prezero/myprezero-sf/issues/68)) ([72ccf4c](https://github.com/prezero/myprezero-sf/commit/72ccf4ce1b2dca21519bfd7b8748ca2bb8b6ea71))
* adjustments for azure odj pipeline ([#39](https://github.com/prezero/myprezero-sf/issues/39)) ([811dc4e](https://github.com/prezero/myprezero-sf/commit/811dc4e1667fa4f1d394139cfdaf724295f98a58))
* enable renovate ([#80](https://github.com/prezero/myprezero-sf/issues/80)) ([c7d5e04](https://github.com/prezero/myprezero-sf/commit/c7d5e041d228853a6a78456cc1aa5f26f1f1a663))
* enhance container security ([#64](https://github.com/prezero/myprezero-sf/issues/64)) ([9bd9a22](https://github.com/prezero/myprezero-sf/commit/9bd9a22af1750172ec72fbb5a97ea59d8fb0a650))
* enhance container security further ([#65](https://github.com/prezero/myprezero-sf/issues/65)) ([1cea09f](https://github.com/prezero/myprezero-sf/commit/1cea09f900f9cff09017414ba4c8e7808ed5b006))
* fix azure sync delete ([#32](https://github.com/prezero/myprezero-sf/issues/32)) ([10d79c4](https://github.com/prezero/myprezero-sf/commit/10d79c4f2783fd9a0765a55281c1b8a5e37bab0c))
* fix dockerfile ([#66](https://github.com/prezero/myprezero-sf/issues/66)) ([cb61698](https://github.com/prezero/myprezero-sf/commit/cb616984532d99d2f4a937136e67572698b77ac6))
* fix HEALTHCHECK_URL ([a5e0094](https://github.com/prezero/myprezero-sf/commit/a5e0094109bb509ee3d2e108430bc0e0c60416f1))
* fix image reference ([#94](https://github.com/prezero/myprezero-sf/issues/94)) ([a002ed1](https://github.com/prezero/myprezero-sf/commit/a002ed1a7e68878c2dfc1f0973f292e0c4cee1d4))
* fix kubectl in test ([#45](https://github.com/prezero/myprezero-sf/issues/45)) ([ee7e632](https://github.com/prezero/myprezero-sf/commit/ee7e63285a1b5d49fd39acc16316bba9a2467096))
* install dev dependencies ([#46](https://github.com/prezero/myprezero-sf/issues/46)) ([ce53a75](https://github.com/prezero/myprezero-sf/commit/ce53a75872a5435f78eef051ec84357fcc4a401b))
* remove bump-after-update for composer.json ([c178c09](https://github.com/prezero/myprezero-sf/commit/c178c095cc0fa863e2db3dcaab36316410f659c0))
* switch to shared workflows ([#87](https://github.com/prezero/myprezero-sf/issues/87)) ([618c55a](https://github.com/prezero/myprezero-sf/commit/618c55a8923b1d9bcefcf540db51d6c15403e78d))

## [0.2.2](https://github.com/prezero/myprezero-sf/compare/v0.2.1...v0.2.2) (2025-02-06)


### Continuous Integration

* fix gh cli ([#28](https://github.com/prezero/myprezero-sf/issues/28)) ([627c7ec](https://github.com/prezero/myprezero-sf/commit/627c7ec99c5c1ba724aded175a792d25a14c3931))

## [0.2.1](https://github.com/prezero/myprezero-sf/compare/v0.2.0...v0.2.1) (2025-02-06)


### Continuous Integration

* add gh cli ([#26](https://github.com/prezero/myprezero-sf/issues/26)) ([8e38bca](https://github.com/prezero/myprezero-sf/commit/8e38bcaf84defda773da223fb11dbed163414466))

## [0.2.0](https://github.com/prezero/myprezero-sf/compare/v0.1.2...v0.2.0) (2025-02-06)


### Features

* add empty homepage ([#17](https://github.com/prezero/myprezero-sf/issues/17)) ([8277baa](https://github.com/prezero/myprezero-sf/commit/8277baa46371f68fba05abea75638f2f8529ff97))
* add inbound api ([#8](https://github.com/prezero/myprezero-sf/issues/8)) ([ec76c7c](https://github.com/prezero/myprezero-sf/commit/ec76c7c2830ed54109b111fba876166244209545))
* add keycloak ([#14](https://github.com/prezero/myprezero-sf/issues/14)) ([5c50e5d](https://github.com/prezero/myprezero-sf/commit/5c50e5d866b0b94ed9409416aafded4710b8f0d9))
* add monitor health check ([#19](https://github.com/prezero/myprezero-sf/issues/19)) ([772ffcb](https://github.com/prezero/myprezero-sf/commit/772ffcb155b2e773dcee3c99b2e4938310ed7edd))
* add sap mockserver & archive document test ([#12](https://github.com/prezero/myprezero-sf/issues/12)) ([2866a14](https://github.com/prezero/myprezero-sf/commit/2866a14d4b1ac23f050410962000fadb1776c1b9))
* configure security ([#22](https://github.com/prezero/myprezero-sf/issues/22)) ([a8869f2](https://github.com/prezero/myprezero-sf/commit/a8869f291935c6e185ca78fc0792137332d14937))
* integrate keycloak to backend ([#21](https://github.com/prezero/myprezero-sf/issues/21)) ([d565437](https://github.com/prezero/myprezero-sf/commit/d56543797681cf33df8549e7c26e775d05f1bec9))
* **security:** add cors config ([e6588cf](https://github.com/prezero/myprezero-sf/commit/e6588cff2366a344315800894f19ec94c5ab93dc))
* **security:** add cors config ([#25](https://github.com/prezero/myprezero-sf/issues/25)) ([2422cd9](https://github.com/prezero/myprezero-sf/commit/2422cd9089bbf1ab95f507657b05dabe9354cb68))
* update keycloak dump ([#23](https://github.com/prezero/myprezero-sf/issues/23)) ([272443f](https://github.com/prezero/myprezero-sf/commit/272443f6ad0f51b26549293a49f3b5127288fb68))


### Miscellaneous Chores

* **deps:** update dependencies ([#15](https://github.com/prezero/myprezero-sf/issues/15)) ([fe311aa](https://github.com/prezero/myprezero-sf/commit/fe311aaa7c7bd3293ce58f9548c896910ad6ff70))
* **deps:** update dependencies ([#16](https://github.com/prezero/myprezero-sf/issues/16)) ([30866dd](https://github.com/prezero/myprezero-sf/commit/30866dda0c38b9cdcac58589b8ae56e291cd7fca))
* update gitignore ([14b4488](https://github.com/prezero/myprezero-sf/commit/14b448822eefd648c851b4fa78e3eca373ffbcab))


### Code Refactoring

* add rector & phpstan ([5fa9409](https://github.com/prezero/myprezero-sf/commit/5fa9409485a23d4167906fffddf77bf193ee7d35))
* rename inbound to input ([#10](https://github.com/prezero/myprezero-sf/issues/10)) ([10899e2](https://github.com/prezero/myprezero-sf/commit/10899e21675fadefebb6c6de385f4ee9c3357eef))


### Build System

* change keycloak ports ([1f9c378](https://github.com/prezero/myprezero-sf/commit/1f9c378cf6014dd8979bcd8aa5977a4e8bafd998))
* update base image ([#13](https://github.com/prezero/myprezero-sf/issues/13)) ([6ce1b45](https://github.com/prezero/myprezero-sf/commit/6ce1b45bbee7fc5cfdda585b660711e621b31add))


### Continuous Integration

* Add Deployment Workflow to deploy-dev ([#11](https://github.com/prezero/myprezero-sf/issues/11)) ([6819f27](https://github.com/prezero/myprezero-sf/commit/6819f276bc190d098ec334732f5217d48cc60dec))
* adjust workflow for dev and qa ([#24](https://github.com/prezero/myprezero-sf/issues/24)) ([6ddef42](https://github.com/prezero/myprezero-sf/commit/6ddef42b6d5b4e54fb37e366af9f2d28470f371f))
* change actions to custom runner ([#18](https://github.com/prezero/myprezero-sf/issues/18)) ([ddaac83](https://github.com/prezero/myprezero-sf/commit/ddaac8353b6975ef098502479e57ce649dabf9b9))

## [0.1.2](https://github.com/prezero/myprezero-sf/compare/v0.1.1...v0.1.2) (2024-12-23)


### Miscellaneous Chores

* init project ([1441b6a](https://github.com/prezero/myprezero-sf/commit/1441b6ab9db65b5d44a8a3f0f462dd1e415e721f))


### Documentation

* add README ([9316b1a](https://github.com/prezero/myprezero-sf/commit/9316b1a3e6cb5912381dc26de7b93128f13d3b34))

## [0.1.1](https://github.com/prezero/myprezero-sf/compare/v0.1.0...v0.1.1) (2024-12-23)


### Miscellaneous Chores

* fix permissions ([6278787](https://github.com/prezero/myprezero-sf/commit/62787879a1ef99d66bec8cef4ed08bccae11227b))
* merge ([ffa2246](https://github.com/prezero/myprezero-sf/commit/ffa22463a6b0e1d9e994320f33151cf1ed8d472e))


### Continuous Integration

* add monitoring ([baade22](https://github.com/prezero/myprezero-sf/commit/baade22628eb899c15ad7a40ed726bdccb7eb983))

## 0.1.0 (2024-12-23)


### Miscellaneous Chores

* bootstrap releases for path: . ([#1](https://github.com/prezero/myprezero-sf/issues/1)) ([9f33b99](https://github.com/prezero/myprezero-sf/commit/9f33b992c4e63d3bbefdbc3714b7b86e7406a59d))
* init project ([de93ceb](https://github.com/prezero/myprezero-sf/commit/de93cebf2c378903579102717d3f4874ed0a701b))
* init project ([b552300](https://github.com/prezero/myprezero-sf/commit/b5523006b7a1f0ffc8d8cd93f89a2db41891a6f4))
* init project ([#2](https://github.com/prezero/myprezero-sf/issues/2)) ([458feff](https://github.com/prezero/myprezero-sf/commit/458feffc628cb92c4a041f11bf20ffcf25c7d0c6))


### Build System

* container ([80ea720](https://github.com/prezero/myprezero-sf/commit/80ea72047070c3b8bcc303aa9102579e3d77ac44))


### Continuous Integration

* add workflows ([e9be412](https://github.com/prezero/myprezero-sf/commit/e9be41291bb6d8ed4ccb54123e56acd76fb99675))
* add workflows ([#3](https://github.com/prezero/myprezero-sf/issues/3)) ([7ad622f](https://github.com/prezero/myprezero-sf/commit/7ad622f170bea9df010f29d15ce10833c7798229))
* add workflows ([#4](https://github.com/prezero/myprezero-sf/issues/4)) ([36d8733](https://github.com/prezero/myprezero-sf/commit/36d8733e69940438e240d849c9ca237ee2bdbbbb))
* fix workflow ([e635d3d](https://github.com/prezero/myprezero-sf/commit/e635d3df3bc674f714d3ab2e97d55b42f1095338))
