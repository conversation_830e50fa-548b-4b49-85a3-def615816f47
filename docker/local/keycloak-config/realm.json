{"enabled": true, "realm": "<PERSON><PERSON><PERSON><PERSON>", "loginTheme": "prezero", "ssoSessionIdleTimeout": 50400, "ssoSessionMaxLifespan": 50400, "accessTokenLifespan": 50400, "eventsEnabled": true, "eventsExpiration": 3600, "adminEventsEnabled": true, "adminEventsDetailsEnabled": true, "requiredActions": [{"alias": "VERIFY_PROFILE", "name": "Verify Profile", "providerId": "VERIFY_PROFILE", "enabled": false, "defaultAction": false, "priority": 90, "config": {}}], "clients": [{"clientId": "myprezero-portal", "name": "MyPrezero Portal", "enabled": true, "publicClient": true, "directAccessGrantsEnabled": true, "protocolMappers": [{"name": "Tenant", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "tenant", "id.token.claim": "true", "lightweight.claim": "false", "claim.name": "tenant", "jsonType.label": "String", "access.token.claim": "true"}}], "defaultClientScopes": ["profile", "roles", "groups", "email"], "optionalClientScopes": ["myprezero-portal"]}, {"clientId": "myprezero-backend", "name": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "vmhFmWZblJtBh0C2K0dT0TCGRBHQbi0F", "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "attributes": {"access.token.lifespan": "1800"}, "defaultClientScopes": ["service_account", "roles", "profile", "basic"], "optionalClientScopes": []}], "clientScopes": [{"name": "myprezero-portal", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}, "protocolMappers": [{"name": "Audience", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "config": {"included.client.audience": "myprezero-backend", "id.token.claim": "false", "lightweight.claim": "false", "introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"name": "groups", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true"}, "protocolMappers": [{"name": "Groups", "protocol": "openid-connect", "protocolMapper": "oidc-group-membership-mapper", "config": {"full.path": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}], "roles": {"client": {"myprezero-backend": [{"name": "myprezero-portal-access", "description": "Main access to the portal."}]}}, "groups": [{"name": "test-group", "path": "/test-group", "clientRoles": {"myprezero-backend": ["myprezero-portal-access"]}}, {"name": "tenant-pz", "path": "/tenant-pz", "attributes": {"tenant": ["pz"]}}, {"name": "tenant-nl", "path": "/tenant-nl", "attributes": {"tenant": ["pznl"]}}, {"name": "tenant-pl", "path": "/tenant-pl", "attributes": {"tenant": ["pzpl"]}}], "users": [{"username": "portal-user", "email": "portal-user@prezero", "enabled": true, "firstName": "Portal", "lastName": "User Tenant Europe", "emailVerified": true, "credentials": [{"type": "password", "value": "portal-password"}], "groups": ["/tenant-pz", "/test-group"]}, {"username": "service-account-myprezero-backend", "emailVerified": false, "enabled": true, "serviceAccountClientId": "myprezero-backend", "requiredActions": [], "clientRoles": {"realm-management": ["manage-users"]}, "groups": []}]}