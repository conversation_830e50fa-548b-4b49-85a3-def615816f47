# App
APP_ENV=localdev
APP_SECRET=fa6bc6ad4bbbd42ab6b086c04aeb14d4
DATA_ENV=dev
STDOUT_LOG_LEVEL=debug

# Database
DATABASE_URL='pgsql://user:userpwd@db:5432/myprezero'

# RabbitMQ
MESSENGER_TRANSPORT_DSN=amqp://rabbitmq:rabbitmq123@message_queue:5672
MESSENGER_TRANSPORT_CACERT=
RABBITMQ_QUORUM_GROUP_SIZE=1

# S3 compatible storage
S3_ACCESS_KEY=minio
S3_SECRET_KEY=minio123
S3_ENDPOINT_URL=http://s3storage:8003
S3_BUCKET_NAME=test-bucket

# Cors
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'

# Test
TEST_ENV=dev

# SAP Europe
SAP_EUROPE_INTERNAL_API=true
SAP_EUROPE_URL=http://mockserver:1080/sap-europe
SAP_EUROPE_USERNAME=sap-eu-user
SAP_EUROPE_PASSWORD=sap-eu-password

# Input
INPUT_API_SAP_EUROPE_USERNAME=input-user-sap-europe
INPUT_API_SAP_EUROPE_PASSWORD=input-password-sap-europe

# Mailer
MAILER_DSN=smtp://mailcatcher:1025
MAIL_ENABLED=1
MAIL_BASE_LINK=http://localhost:8000
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=MyPreZero
EMAIL_REPLY_TO=<EMAIL>

# Keycloak
KEYCLOAK_URL='http://keycloak:8080'
KEYCLOAK_REALM='myprezero'
KEYCLOAK_CLIENT_ID='myprezero-backend'
KEYCLOAK_CLIENT_SECRET='vmhFmWZblJtBh0C2K0dT0TCGRBHQbi0F'
KEYCLOAK_PORTAL_CLIENT_ID='myprezero-portal'
KEYCLOAK_ISSUER='http://keycloak:8080/realms/myprezero'
KEYCLOAK_OIDC_KEY='{"kid":"5u5uDO2Lrxv2hebZn1rxy7NGOABUYUyqlwHzqL5vmjM","kty":"EC","alg":"ES256","use":"sig","crv":"P-256","x":"CkMlErmy4Xbmz0yrG2yffxEZq0BNMEH0vQe7oZMAFWM","y":"td84InDYG1F3FLRqrPBvTdbj3nlqqJN4zz4ZnGVfvdg"}'
KEYCLOAK_TEST_USER_NAME='portal-user'
KEYCLOAK_TEST_USER_PASSWORD='portal-password'

# Misc
MOCK_SERVER_ENDPOINT=http://mockserver:1080

# Redis
REDIS_URL='redis://redis:6379'
