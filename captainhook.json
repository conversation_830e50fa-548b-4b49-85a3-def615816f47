{"config": {"run": {"mode": "docker", "exec": "docker compose exec -T backend"}}, "commit-msg": {"enabled": true, "actions": [{"action": "\\Ramsey\\CaptainHook\\ValidateConventionalCommit"}]}, "pre-push": {"enabled": false, "actions": []}, "pre-commit": {"enabled": true, "actions": [{"action": "\\CaptainHook\\App\\Hook\\Composer\\Action\\CheckLockFile"}, {"action": "\\CaptainHook\\App\\Hook\\PHP\\Action\\Linting"}, {"action": "\\CaptainHook\\App\\Hook\\File\\Action\\MaxSize", "options": {"maxSize": "5M"}}, {"action": "composer audit"}, {"action": "vendor/bin/rector process --dry-run"}, {"action": "/app/php-cs-fixer.sh"}, {"action": "vendor/bin/phpstan analyse -c phpstan.dist.neon --memory-limit 1G"}, {"action": "vendor/bin/phpstan analyse -c phpstan.test.neon --memory-limit 1G"}, {"action": "bin/console --env=fixdev hautelook:fixtures:load -n"}, {"action": "vendor/bin/codecept run"}]}, "prepare-commit-msg": {"enabled": true, "actions": [{"action": "\\Ramsey\\CaptainHook\\PrepareConventionalCommit"}]}, "post-commit": {"enabled": false, "actions": []}, "post-merge": {"enabled": false, "actions": []}, "post-checkout": {"enabled": false, "actions": []}, "post-rewrite": {"enabled": false, "actions": []}, "post-change": {"enabled": true, "actions": [{"action": "composer install"}]}}