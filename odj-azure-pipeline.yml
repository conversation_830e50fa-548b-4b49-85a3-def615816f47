trigger:
    batch: true

resources:
    repositories:
        - repository: templates
          type: git
          ref: refs/heads/master
          name: schwarzit.odj-pipeline-templates/odj-deliver-templates

variables:
    - group: odj-technical-product-properties
    - group: odj-component-myprezero-sf-properties
    - group: myprezero-sf-variables

parameters:
    -   name: php_version
        type: string
        default: '8.4'
    -   name: php_working_dir
        type: string
        default: $(component_path)
    -   name: php_extensions
        type: object
        default:
            - amqp
            - apcu
            - bcmath
            - calendar
            - curl
            - dom
            - exif
            - gd
            - intl
            - mbstring
            - mongodb
            - mysqli
            - opcache
            - pdo-mysql
            - pdo-pgsql
            - pgsql
            - redis
            - soap
            - sockets
            - sqlite3
            - xdebug
            - xml
            - xsl
            - zip

extends:
    template: templates/technologies/php-composer-symfony/v2/odj-pipeline-template-technology-php-composer-symfony.yml@templates
    parameters:
        php_version: ${{ parameters.php_version }}
        php_run_tests: true
        php_install_command: "echo 'Skipped let the docker compose command manage it!'"
        php_test_command: "docker compose exec backend sh -c 'php -d error_reporting=\"E_ALL & ~E_DEPRECATED & ~E_USER_DEPRECATED\" vendor/bin/codecept run Api --xml --phpunit-xml --coverage --coverage-xml -v'"
        test_results_files: "tests/_output/phpunit-report.xml"
        odj_deployment: false
        odj_internal_deploy_enabled: false
        copy_build_artifacts: false
        openapi_enabled: false
        build_docker_image: false
        odj_devenv_static_code_analysis:
            - sonarqube_saas
        odj_devenv_dependency_scanner:
            - snyk_saas_dependency
            - snyk_saas_dockerfile
        odj_devenv_artifact_repository:
            - artifactory_saas
        odj_devenv_pipeline: azuredevops  # Managed by ODJ, do not modify
        odj_devenv_code_repository: azuredevops  # Managed by ODJ, do not modify
        odj_runenv_component_type: deployable  # Managed by ODJ, do not modify
        productive_branch: refs/heads/master
        postSourcecodeCache:
          - script: |
                extensions=($(echo "${{ join(',',parameters.php_extensions) }}" | tr ',' '\n'))
                versionedExtensions=()
                for ext in "${extensions[@]}"
                do
                  versionedExtensions+=("php${{ parameters.php_version }}-${ext}")
                done
                versionedExtensionInstall=$(IFS=' '; echo "${versionedExtensions[*]}")
                sudo apt -y install $versionedExtensionInstall
            displayName: "⚙️ Install PHP Extensions"
            condition: succeeded()
          -   task: Docker@2
              displayName: "📦 Login to GHCR Registry"
              inputs:
                  command: login
                  containerRegistry: prezero-docker-registry
              condition: succeeded()
          -   task: DockerCompose@1
              displayName: "🐋 Docker compose build"
              inputs:
                  dockerComposeFile: "docker-compose.yml"
                  action: Run a Docker Compose command
                  dockerComposeCommand: "build"
                  projectName: "myprezero"
          -   task: DockerCompose@1
              displayName: "🐋 Docker compose up"
              inputs:
                  dockerComposeFile: "docker-compose.yml"
                  action: Run a Docker Compose command
                  dockerComposeCommand: "up -d --wait"
                  projectName: "myprezero"
              condition: succeeded()
              env:
                  S3_VOLUME: "s3-data"
                  XDEBUG_MODE: "coverage"
        postSourcecodeBuild:
            -   task: DockerCompose@1
                displayName: "🐋 Docker compose down"
                inputs:
                    dockerComposeFile: "docker-compose.yml"
                    action: Run a Docker Compose command
                    dockerComposeCommand: "down --rmi all --volumes"
                    projectName: "myprezero"
                condition: always()
            -   script: |
                    sudo chown -R $(id -u):$(id -g) docker
                    find docker/local/ -name data -type d -exec rm -rf {} +
                workingDirectory: ${{ parameters.php_working_dir }}
                displayName: "🧹 Cleanup leftover Docker files"
                condition: always()
            -   task: Docker@2
                displayName: "📦 Logout from GHCR Registry"
                condition: always()
                inputs:
                    command: logout
                    containerRegistry: prezero-docker-registry
