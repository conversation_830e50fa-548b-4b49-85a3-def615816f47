# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
# https://symfony.com/doc/current/configuration/secrets.html
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

# App
APP_ENV=localdev
APP_SECRET=
DATA_ENV=
STDOUT_LOG_LEVEL=debug

# Database
DATABASE_URL=

# RabbitMQ
MESSENGER_TRANSPORT_DSN=
MESSENGER_TRANSPORT_CACERT=
RABBITMQ_QUORUM_GROUP_SIZE=

# S3 compatible storage configuration
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_ENDPOINT_URL=
S3_BUCKET_NAME=

# Cors
CORS_ALLOW_ORIGIN=

# Test
TEST_ENV=dev

# SAP Europe
SAP_EUROPE_INTERNAL_API=
SAP_EUROPE_URL=
SAP_EUROPE_USERNAME=
SAP_EUROPE_PASSWORD=

# Input
INPUT_API_SAP_EUROPE_USERNAME=
INPUT_API_SAP_EUROPE_PASSWORD=

# Mailer
MAILER_DSN=null://null
MAIL_ENABLED=
MAIL_BASE_LINK=
EMAIL_FROM=
EMAIL_FROM_NAME=
EMAIL_REPLY_TO=

# Keycloak
KEYCLOAK_URL=
KEYCLOAK_ADMIN_USER=
KEYCLOAK_ADMIN_PASSWORD=
KEYCLOAK_ISSUER=
KEYCLOAK_REALM=
KEYCLOAK_OIDC_KEY=
KEYCLOAK_CLIENT_ID=
KEYCLOAK_CLIENT_SECRET=
KEYCLOAK_PORTAL_CLIENT_ID=

# Misc
MOCK_SERVER_ENDPOINT=http://mockserver:1080

# Here API
HERE_URL=
HERE_API_KEY=

###> symfony/lock ###
# Choose one of the stores below
# postgresql+advisory://db_user:db_password@localhost/db_name
LOCK_DSN=flock
###< symfony/lock ###

###> snc/redis-bundle ###
# passwords that contain special characters (@, %, :, +) must be urlencoded
REDIS_URL=
###< snc/redis-bundle ###
