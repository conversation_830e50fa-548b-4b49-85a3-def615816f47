<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait PortalManagementEndpointsTrait
{
    /**
     * @throws \Exception
     */
    public function callGetPortalManagementBusinessPartners(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementBusinessPartnerById(
        string $businessPartnerId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementOrder(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/order'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementOrderById(
        string $orderId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/order/'.$orderId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementSuppliers(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/supplier'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementSupplierById(
        string $supplierId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/supplier/'.$supplierId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementSuppliersDelayOption(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/supplier-delay-option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementSuppliersDailyDelayOption(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/supplier-daily-delay-option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callPostPortalManagementSupplier(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPost('/api/portal/v1/management/supplier'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPatchPortalManagementSupplier(
        string $supplierId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPatch('/api/portal/v1/management/supplier/'.$supplierId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementServiceProducts(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/service-product'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementServiceProductsWithFilter(
        string $serviceProduct,
        string $serviceType,
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/service-product'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'search' => $search,
            'serviceType' => $serviceType,
            'serviceProduct' => $serviceProduct,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetManagementServiceProductServiceTypeOption(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/service-product/service-type/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPostPortalManagementServiceProduct(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPost('/api/portal/v1/management/service-product'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callDeletePortalManagementServiceProduct(
        string $serviceProductId,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendDelete('/api/portal/v1/management/service-product/'.$serviceProductId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPatchPortalManagementServiceProduct(
        string $serviceProductId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPatch('/api/portal/v1/management/service-product/'.$serviceProductId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callPostPortalBusinessPartnerManagementUser(
        string $businessPartnerId,
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPost('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/user'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementBusinessPartnerUsers(
        string $businessPartnerId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/user'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementBusinessPartnerUsersWithFilter(
        string $businessPartnerId,
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/user'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'search' => $search]
        );
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementBusinessPartnerUserById(
        string $businessPartnerId,
        string $userId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementGroups(
        string $businessPartnerId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementGroupById(
        string $businessPartnerId,
        string $groupId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group/'.$groupId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPostPortalManagementGroup(
        string $businessPartnerId,
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPost('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callUpdatePortalManagementGroup(
        string $businessPartnerId,
        string $groupId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPatch('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group/'.$groupId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callUpdatePortalManagementUser(
        string $businessPartnerId,
        string $userId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPatch('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callDeletePortalManagementGroup(
        string $businessPartnerId,
        string $groupId,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendDelete('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group/'.$groupId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementServiceLocations(
        string $businessPartnerId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/service-location'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementGroupServiceLocations(
        string $businessPartnerId,
        string $groupId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group/service-location'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'groupId' => $groupId,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementGroupServiceLocationsWithFilter(
        string $businessPartnerId,
        string $groupId,
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group/service-location'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'groupId' => $groupId,
            'search' => $search,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetPortalManagementGroupUser(
        string $businessPartnerId,
        string $groupId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group/'.$groupId.'/user'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetPortalManagementGroupUserWithFilter(
        string $businessPartnerId,
        string $groupId,
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group/'.$groupId.'/user'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'groupId' => $groupId,
            'search' => $search,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callPatchPortalManagementGroupUsers(
        string $businessPartnerId,
        string $groupId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPatch('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/group/'.$groupId.'/user'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callDeletePortalBusinessPartnerManagementUser(
        string $businessPartnerId,
        string $userId,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendDelete('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetPortalManagementUsers(
        ?string $search = null,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $query = null === $search
            ? ($debug ? '?XDEBUG_TRIGGER' : '')
            : '?search='.$search.($debug ? '&XDEBUG_TRIGGER' : '');

        $this->sendGet('/api/portal/v1/management/user'.$query);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callPostPortalManagementUser(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendPost('/api/portal/v1/management/user'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callPatchPortalManagementUser(
        string $userId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendPatch('/api/portal/v1/management/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetPortalManagementUser(
        string $userId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/management/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callDeletePortalManagementUser(
        string $userId,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendDelete('/api/portal/v1/management/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetPortalManagementUserPermissionOptions(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/management/user/permissions-option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementUserPermissionsOption(
        string $businessPartnerId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/user/permissions-option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalManagementGroupsOption(
        string $businessPartnerId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/management/businesspartner/'.$businessPartnerId.'/user/groups-option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }
}
