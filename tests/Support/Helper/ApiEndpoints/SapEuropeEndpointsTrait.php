<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait SapEuropeEndpointsTrait
{
    /**
     * @throws \Exception
     */
    public function callSapEuropeBusinessPartnerUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/business-partner'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $businessPartnerId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($businessPartnerId);

        return is_string(value: $businessPartnerId) ? $businessPartnerId : '';
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeServiceLocationUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/service-location'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $serviceLocationId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($serviceLocationId);

        return is_string(value: $serviceLocationId) ? $serviceLocationId : '';
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeContractUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/contract'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $contractId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($contractId);

        return is_string(value: $contractId) ? $contractId : '';
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeContractServiceLocationUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): int {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/contract-service-location'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return 0;
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $contractServiceLocationId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($contractServiceLocationId);

        return is_int(value: $contractServiceLocationId) ? $contractServiceLocationId : 0;
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeServiceUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/service'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $serviceId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($serviceId);

        return is_string(value: $serviceId) ? $serviceId : '';
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeOrderUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/order'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $orderId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($orderId);

        return is_string(value: $orderId) ? $orderId : '';
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeRouteUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/route'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $routeId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($routeId);

        return is_string(value: $routeId) ? $routeId : '';
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeSupervisionDocumentUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/supervision-document'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $supervisionDocumentId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($supervisionDocumentId);

        return is_string(value: $supervisionDocumentId) ? $supervisionDocumentId : '';
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeVerificationDocumentUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): int {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/verification-document'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return 0;
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $verificationDocumentId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($verificationDocumentId);

        return is_int(value: $verificationDocumentId) ? $verificationDocumentId : 0;
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeInvoiceUpsert(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): string {
        $this->haveHttpHeader('Content-Type', 'application/json');
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));

        $this->sendPost(
            '/api/sap-europe/v1/invoice'.($debug ? '?XDEBUG_TRIGGER' : ''),
            $data
        );

        if (200 !== $expectedStatusCode) {
            $this->seeResponseCodeIs($expectedStatusCode);

            return '';
        }

        $this->seeResponseCodeIs($expectedStatusCode);

        $invoiceId = $this->grabDataFromResponseByJsonPath('$.id')[0];
        $this->assertNotEmpty($invoiceId);

        return is_string(value: $invoiceId) ? $invoiceId : '';
    }

    /**
     * @throws \Exception
     */
    public function callSapEuropeArchiveDocumentGet(
        string $archiveId,
        string $documentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Authorization', 'Basic '.base64_encode(string: 'input-user-sap-europe:input-password-sap-europe'));
        $this->sendGet("/api/sap-europe/v1/internal/archives/$archiveId/$documentId");
        $this->seeResponseCodeIs($expectedStatusCode);
    }
}
