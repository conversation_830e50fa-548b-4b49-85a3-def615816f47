<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait PortalCustomerEndpointsTrait
{
    /**
     * @throws \Exception
     */
    public function callGetPortalContracts(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/contract'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalContractsWithFilter(
        string $search,
        string $extServiceLocationId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/contract'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'search' => $search,
            'serviceLocationId' => $extServiceLocationId,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalContractById(
        string $contractId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/contract/'.$contractId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalInvoices(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/invoice'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalInvoicesWithFilter(
        string $dateFrom,
        string $dateTo,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/invoice'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrders(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/order'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderById(
        string $orderId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/order/'.$orderId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderAgreementOptions(
        string $serviceType,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/order/agreement/option'.($debug ? '?XDEBUG_TRIGGER' : ''), ['serviceType' => $serviceType]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderAgreementOptionsFailsWithoutFilter(
        int $expectedStatusCode = 400,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/order/agreement/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderMaterialOptions(
        string $contractId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/order/material/option'.($debug ? '?XDEBUG_TRIGGER' : ''), ['contractId' => $contractId]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderMaterialOptionsWithoutFilter(
        int $expectedStatusCode = 400,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/order/material/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPostPortalOrder(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendPost('/api/portal/v1/order'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPatchPortalOrderCancel(
        string $orderId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendPatch('/api/portal/v1/order/cancel/'.$orderId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderDates(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/order-date'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderContainerOptions(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
        string $serviceLocationId = '',
        string $contractId = '',
        string $serviceType = '',
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $params = sprintf(
            '?serviceLocationId=%s&contractId=%s&serviceType=%s',
            $serviceLocationId,
            $contractId,
            $serviceType,
        );

        $this->sendGet('/api/portal/v1/order/container/option'.$params.($debug ? '&XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderContainerOptionsWithoutFilter(
        int $expectedStatusCode = 400,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/order/container/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetPortalOrderDatePickerOptions(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
        string $serviceLocationId = '',
        string $contractId = '',
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $params = sprintf(
            '?serviceLocationId=%s&contractId=%s',
            $serviceLocationId,
            $contractId,
        );

        $this->sendGet('/api/portal/v1/order/date-picker/option'.$params.($debug ? '&XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderDatePickerOptionsWithoutFilter(
        int $expectedStatusCode = 400,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/order/date-picker/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalOrderDatesWithFilter(
        string $contractId,
        string $serviceLocationId,
        string $dateFrom,
        string $dateTo,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/order-date'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'contractId' => $contractId,
            'serviceLocationId' => $serviceLocationId,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPostPortalRegisterExtractRequest(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendPost('/api/portal/v1/register-extract-request'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalSupervisionDocumentSync(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/supervision-document/sync'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalSupervisionDocuments(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/supervision-document'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalSupervisionDocumentsWithFilter(
        string $search,
        string $status,
        string $dateFrom,
        string $dateTo,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/supervision-document'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'search' => $search,
            'status' => $status,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalSupervisionDocumentById(
        ?string $supervisionDocumentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/supervision-document/'.$supervisionDocumentId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalUserMe(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/user/me'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalUserById(
        string $userId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalUsers(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/user'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPostPortalUser(
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendPost('/api/portal/v1/user'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callUpdatePortalUser(
        string $userId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendPatch('/api/portal/v1/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalUserCheckUsername(
        string $username,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/user/check-username/'.$username.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalUserCheckEmail(
        string $email,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/user/check-email/'.$email.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callDeletePortalUser(
        string $userId,
        string $businessPartnerId = 'acfe025b-0beb-393d-adb7-c92033f1fa2c',
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', $businessPartnerId);

        $this->sendDelete('/api/portal/v1/user/'.$userId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalUserPermission(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/user'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalVerificationDocuments(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/verification-document'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalVerificationDocumentsWithFilter(
        string $search,
        string $status,
        string $dateFrom,
        string $dateTo,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/verification-document'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'search' => $search,
            'status' => $status,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalVerificationDocumentById(
        ?string $verificationDocumentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/verification-document/'.$verificationDocumentId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalWasteStatistics(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/waste-statistic'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalWasteStatisticById(
        string $wasteStatisticId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/waste-statistic/'.$wasteStatisticId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callExportPortalWasteStatistics(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendPost('/api/portal/v1/waste-statistic-export'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callFetchPortalWasteStatisticFromSap(
        string $businessPartnerId,
        string $dateFrom,
        string $dateTo,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendPost('/api/portal/v1/waste-statistic/sap', [
            'businessPartnerId' => $businessPartnerId,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalBusinessPartnerOptions(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');

        $this->sendGet('/api/portal/v1/business-partner/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalContractOptions(
        string $serviceLocationId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/contract/option'.($debug ? '?XDEBUG_TRIGGER' : ''), ['serviceLocationId' => $serviceLocationId]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalContractPositions(
        string $contractId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/contract/'.$contractId.'/position'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalContractPositionsWithFilter(
        string $contractId,
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/contract/'.$contractId.'/position'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'search' => $search,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalContractServiceLocations(
        string $contractId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/contract/'.$contractId.'/container-service-location'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalContractServiceLocationsWithFilter(
        string $contractId,
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/contract/'.$contractId.'/container-service-location'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'search' => $search,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalDashboardWasteStatistic(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/dashboard/waste-statistic'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalDashboardCo2Report(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/dashboard/co2-report'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalDashboardNextOrderDates(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/dashboard/next-order-date'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalDashboardLastOrders(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/dashboard/last-order'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalDashboardInvoices(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/dashboard/invoice'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalGroups(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/group'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalGroupsWithFilter(
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/group'.($debug ? '?XDEBUG_TRIGGER' : ''), ['search' => $search]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalGroupById(
        string $groupId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/group/'.$groupId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPostPortalGroup(
        string $data,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendPost('/api/portal/v1/group'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPatchPortalGroup(
        string $groupId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPatch('/api/portal/v1/group/'.$groupId.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callPatchPortalUsersInGroup(
        string $groupId,
        string $data,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendPatch('/api/portal/v1/group/'.$groupId.'/user'.($debug ? '?XDEBUG_TRIGGER' : ''), $data);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callDeletePortalGroup(
        string $groupId,
        int $expectedStatusCode = 204,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendDelete('/api/portal/v1/group/'.$groupId.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalGroupServiceLocationOptions(
        string $groupId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/group/service-location-option'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'groupId' => $groupId,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalGroupServiceLocationOptionsWithFilter(
        string $groupId,
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/group/service-location-option'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'groupId' => $groupId,
            'search' => $search,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalGroupUserOptions(
        string $groupId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'e5027939-6448-4328-a792-61bb18139a88');
        $this->sendGet('/api/portal/v1/group/user-option'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'groupId' => $groupId,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalGroupUserOptionsWithFilter(
        string $groupId,
        string $search,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'e5027939-6448-4328-a792-61bb18139a88');
        $this->sendGet('/api/portal/v1/group/user-option'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'groupId' => $groupId,
            'search' => $search,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalServiceLocationOptions(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/service-location/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalServiceLocationOptionsWithFilter(
        string $contractId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');

        $this->sendGet('/api/portal/v1/service-location/option'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'contractId' => $contractId,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalServiceTypeOptions(
        string $contractId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/order/service-type/option'.($debug ? '?XDEBUG_TRIGGER' : ''), ['contractId' => $contractId]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalServiceTypeOptionsWithoutFilter(
        int $expectedStatusCode = 400,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/order/service-type/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalSupervisionDocumentStatusOptions(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/supervision-document/status/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalTranslationLocales(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/translation/locale'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalTranslationsByIso(
        string $iso,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->sendGet('/api/portal/v1/translation/locale/'.$iso.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalVerificationDocumentNumberOptions(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/verification-document/option'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    /**
     * @throws \Exception
     */
    public function callGetPortalVerificationDocumentSupervisionDocuments(
        ?string $verificationDocumentId,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/verification-document/'.$verificationDocumentId.'/supervision-document'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetEmissionDataOverview(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/emission-data/overview'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetEmissionDataMaterial(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/emission-data/material'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetEmissionDataMonthlySavings(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/emission-data/monthly-savings'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetEmissionDataMonthlySavingsWithFilter(
        string $dateFrom,
        string $dateTo,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/emission-data/monthly-savings'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetEmissionDataMonthlyEmissions(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/emission-data/monthly-emissions'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetEmissionDataMonthlyEmissionsWithFilter(
        string $dateFrom,
        string $dateTo,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/emission-data/monthly-emissions'.($debug ? '?XDEBUG_TRIGGER' : ''), [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callGetEmissionDataTotal(
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->haveHttpHeader('Accept', 'application/json');
        $this->haveHttpHeader('x-current-user-context-id', 'acfe025b-0beb-393d-adb7-c92033f1fa2c');
        $this->sendGet('/api/portal/v1/emission-data/total'.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }
}
