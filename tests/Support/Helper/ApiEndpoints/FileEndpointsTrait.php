<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

trait FileEndpointsTrait
{
    public function callApiFileUpload(
        string $filePath,
        string $fileContent,
        int $expectedStatusCode = 201,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->sendPut(
            url: '/api/portal/v1/file/'.$filePath.($debug ? '?XDEBUG_TRIGGER' : ''),
            params: $fileContent,
        );
        $this->seeResponseCodeIs($expectedStatusCode);
    }

    public function callApiFileRetrieve(
        string $filePath,
        int $expectedStatusCode = 200,
        bool $debug = false,
        bool $profile = false,
    ): void {
        $this->sendGet(url: '/api/portal/v1/file/'.$filePath.($debug ? '?XDEBUG_TRIGGER' : ''));
        $this->seeResponseCodeIs($expectedStatusCode);
    }
}
