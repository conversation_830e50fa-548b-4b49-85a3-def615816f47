<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\ApiEndpoints;

use App\Tests\Support\ApiTester;

class PortalEndpointsService
{
    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getBusinessPartner(ApiTester $I): array
    {
        $I->callGetPortalBusinessPartnerOptions();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $businessPartner = $response['items'] ?? [];

        return $businessPartner[1] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getContract(ApiTester $I): array
    {
        $I->callGetPortalContracts();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $contract = $response['items'] ?? [];

        return $contract[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getContractPosition(ApiTester $I): array
    {
        $contract = self::getContract(I: $I);
        $I->callGetPortalContractPositions(contractId: $contract['id']);
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $contractPositions = $response['items'] ?? [];

        return $contractPositions[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getContractServiceLocation(ApiTester $I): array
    {
        $contract = self::getContract(I: $I);
        $I->callGetPortalContractServiceLocations(contractId: $contract['id']);
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $contractServiceLocations = $response['items'] ?? [];

        return $contractServiceLocations[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getGroup(ApiTester $I): array
    {
        $I->callGetPortalGroups();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $groups = $response['items'] ?? [];

        return $groups[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getMonthlyEmissions(ApiTester $I): array
    {
        $I->callGetEmissionDataMonthlyEmissions();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $monthlyEmissions = $response['items'] ?? [];

        return $monthlyEmissions[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getMonthlySavings(ApiTester $I): array
    {
        $I->callGetEmissionDataMonthlySavings();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $monthlySavings = $response['items'] ?? [];

        return $monthlySavings[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getOrder(ApiTester $I): array
    {
        $I->callGetPortalOrders();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $order = $response['items'] ?? [];

        return $order[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getOrderDate(ApiTester $I): array
    {
        $I->callGetPortalOrderDates();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $orderDates = $response['items'] ?? [];

        return $orderDates[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getOrderConfirmation(ApiTester $I): array
    {
        $supplier = self::getSupplier(I: $I);
        $I->callPatchPortalManagementSupplier(supplierId: $supplier['id'] ?? '', data: '{
            "name": "Mustername"
        }');
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $orderConfirmations = $response['order_confirmation'] ?? [];

        return $orderConfirmations[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getInvoice(ApiTester $I): array
    {
        $I->callGetPortalInvoices();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $invoices = $response['items'] ?? [];

        return $invoices[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getServiceLocation(ApiTester $I): array
    {
        $I->callGetPortalServiceLocationOptions();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $serviceLocations = $response['items'] ?? [];

        return $serviceLocations[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getSupplier(ApiTester $I): array
    {
        $I->callGetPortalManagementSuppliers();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $suppliers = $response['items'] ?? [];

        return $suppliers[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getServiceProduct(ApiTester $I): array
    {
        $I->callGetPortalManagementServiceProducts();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $serviceProducts = $response['items'] ?? [];

        return $serviceProducts[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getSupervisionDocument(ApiTester $I): array
    {
        $I->callGetPortalSupervisionDocuments();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $supervisionDocuments = $response['items'] ?? [];

        return $supervisionDocuments[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getUser(ApiTester $I): array
    {
        $I->callGetPortalUsers();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $users = $response['items'] ?? [];

        return $users[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getManagementUser(ApiTester $I): array
    {
        $I->callGetPortalManagementUsers();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        /** @var array<int, array<string, string>> $users */
        $users = $response['items'] ?? [];

        if (count(value: $users) > 1) {
            return $users[1];
        }

        return $users[0] ?? [];
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    public static function getVerificationDocument(ApiTester $I): array
    {
        $I->callGetPortalVerificationDocuments();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $verificationDocuments = $response['items'] ?? [];

        return $verificationDocuments[0] ?? [];
    }
}
