<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Keycloak;

trait KeycloakTestUserTrait
{
    public function getKeycloakTestUser(): string
    {
        return is_string(value: $_ENV['KEYCLOAK_TEST_USER_NAME']) ? $_ENV['KEYCLOAK_TEST_USER_NAME'] : '';
    }

    public function getKeycloakTestUserPassword(): string
    {
        return is_string(value: $_ENV['KEYCLOAK_TEST_USER_PASSWORD']) ? $_ENV['KEYCLOAK_TEST_USER_PASSWORD'] : '';
    }
}
