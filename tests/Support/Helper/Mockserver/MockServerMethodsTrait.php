<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper\Mockserver;

use App\Tests\Support\ApiTester;

trait MockServerMethodsTrait
{
    public function callMockServerReset(): void
    {
        $this->sendPut($this->getMockServerUrl().'/mockserver/reset');
        $this->seeResponseCodeIsSuccessful();
    }

    /**
     * @param array<mixed> $requestBody
     * @param array<mixed> $responseBody
     *
     * @throws \JsonException
     */
    public function callMockServerSetPermanentExpectation(
        string $method,
        string $path,
        array $requestBody = [],
        array $responseBody = [],
    ): void {
        $expectationData = [
            'httpRequest' => [
                'path' => $path,
                'method' => $method,
                'body' => $requestBody,
            ],
            'httpResponse' => [
                'statusCode' => 200,
                'body' => $responseBody,
            ],
        ];

        $this->sendPut(
            $this->getMockServerUrl().'/mockserver/expectation',
            json_encode(value: $expectationData, flags: JSON_THROW_ON_ERROR)
        );

        $this->seeResponseCodeIsSuccessful();
    }

    /**
     * @throws \JsonException
     */
    public function callMockServerSetEmptyRequestBodyExpectation(
        string $method,
        string $path,
        string $responseBody = '',
    ): void {
        $expectationData = [
            'httpRequest' => [
                'path' => $path,
                'method' => $method,
            ],
            'httpResponse' => [
                'statusCode' => 200,
                'body' => $responseBody,
            ],
            'times' => [
                'remainingTimes' => 1,
                'unlimited' => false,
            ],
            'timeToLive' => [
                'timeUnit' => 'SECONDS',
                'timeToLive' => 60,
                'unlimited' => false,
            ],
        ];

        $this->sendPut(
            $this->getMockServerUrl().'/mockserver/expectation',
            json_encode(value: $expectationData, flags: JSON_THROW_ON_ERROR)
        );

        $this->seeResponseCodeIsSuccessful();
    }

    /**
     * @param array<string, array<string>> $query
     *
     * @throws \JsonException
     */
    public function callMockServerSetEmptyRequestBodyExpectationWithFilter(
        string $method,
        string $path,
        array $query,
        string $responseBody = '',
    ): void {
        $expectationData = [
            'httpRequest' => [
                'path' => $path,
                'method' => $method,
                'queryStringParameters' => $query,
            ],
            'httpResponse' => [
                'statusCode' => 200,
                'body' => $responseBody,
            ],
            'times' => [
                'remainingTimes' => 1,
                'unlimited' => false,
            ],
            'timeToLive' => [
                'timeUnit' => 'SECONDS',
                'timeToLive' => 60,
                'unlimited' => false,
            ],
        ];

        $this->sendPut(
            $this->getMockServerUrl().'/mockserver/expectation',
            json_encode(value: $expectationData, flags: JSON_THROW_ON_ERROR)
        );

        $this->seeResponseCodeIsSuccessful();
    }

    /**
     * @param array<mixed> $requestBodyJson
     *
     * @throws \JsonException
     */
    public function callMockServerSetJsonRequestExpectation(
        string $method,
        string $path,
        array $requestBodyJson,
        string $responseBody = '',
        int $responseCode = 200,
    ): void {
        $expectationData = [
            'httpRequest' => [
                'path' => $path,
                'method' => $method,
                'body' => [
                    'type' => 'JSON',
                    'json' => $requestBodyJson,
                ],
            ],
            'httpResponse' => [
                'statusCode' => $responseCode,
                'body' => $responseBody,
            ],
            'times' => [
                'remainingTimes' => 100,
                'unlimited' => false,
            ],
            'timeToLive' => [
                'timeUnit' => 'SECONDS',
                'timeToLive' => 60,
                'unlimited' => false,
            ],
        ];

        $this->sendPut(
            $this->getMockServerUrl().'/mockserver/expectation',
            json_encode(value: $expectationData, flags: JSON_THROW_ON_ERROR)
        );

        $this->seeResponseCodeIsSuccessful();
    }

    /**
     * @throws \JsonException
     */
    public function callMockServerSetBinaryExpectation(
        string $method,
        string $path,
        string $responseBodyBase64,
        int $responseCode = 200,
    ): void {
        $expectationData = [
            'httpRequest' => [
                'path' => $path,
                'method' => $method,
            ],
            'httpResponse' => [
                'statusCode' => $responseCode,
                'body' => [
                    'type' => 'BINARY',
                    'base64Bytes' => $responseBodyBase64,
                ],
            ],
            'times' => [
                'remainingTimes' => 100,
                'unlimited' => false,
            ],
            'timeToLive' => [
                'timeUnit' => 'SECONDS',
                'timeToLive' => 60,
                'unlimited' => false,
            ],
        ];

        $this->sendPut(
            $this->getMockServerUrl().'/mockserver/expectation',
            json_encode(value: $expectationData, flags: JSON_THROW_ON_ERROR)
        );

        $this->seeResponseCodeIsSuccessful();
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function callMockServerSetRegexBodyExpectation(
        string $method,
        string $path,
        string $regex,
    ): mixed {
        $expectationData = [
            'httpRequest' => [
                'path' => $path,
                'method' => $method,
                'body' => [
                    'type' => 'REGEX',
                    'regex' => $regex,
                ],
            ],
            'httpResponse' => [
                'statusCode' => 200,
                'body' => '',
            ],
            'times' => [
                'remainingTimes' => 1,
                'unlimited' => false,
            ],
            'timeToLive' => [
                'timeUnit' => 'SECONDS',
                'timeToLive' => 30,
                'unlimited' => false,
            ],
        ];

        $this->sendPut(
            $this->getMockServerUrl().'/mockserver/expectation',
            json_encode(value: $expectationData, flags: JSON_THROW_ON_ERROR)
        );

        $this->seeResponseCodeIsSuccessful();

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @param array<mixed> $expectationIds
     *
     * @throws \Throwable
     */
    public function callMockServerVerifyExpectationsInSequence(array $expectationIds): void
    {
        $requestData = [];

        foreach ($expectationIds as $expectationId) {
            $requestData['expectationIds'][] = [
                'id' => $expectationId,
            ];
        }

        $this->waitUntil(function (ApiTester $I) use ($requestData): void {
            $this->sendPut(
                $this->getMockServerUrl().'/mockserver/verifySequence',
                json_encode(value: $requestData, flags: JSON_THROW_ON_ERROR)
            );
            $this->seeResponseCodeIsSuccessful();
        });
    }

    /**
     * @param array<mixed> $expectationIds
     *
     * @throws \Throwable
     */
    public function callMockServerVerifyExpectationsWithoutSequence(array $expectationIds): void
    {
        $requestData = [];

        foreach ($expectationIds as $expectationId) {
            $requestData['expectationId'] = [
                'id' => $expectationId,
            ];

            $this->waitUntil(function (ApiTester $I) use ($requestData): void {
                $this->sendPut(
                    $this->getMockServerUrl().'/mockserver/verify',
                    json_encode(value: $requestData, flags: JSON_THROW_ON_ERROR)
                );

                $this->seeResponseCodeIsSuccessful();
            });
        }
    }

    /**
     * @param array<mixed> $expectationIds
     *
     * @throws \Throwable
     */
    public function callMockServerVerifyNot(array $expectationIds): void
    {
        $requestData = [
            'times' => [
                'atMost' => 0,
            ],
        ];

        foreach ($expectationIds as $expectationId) {
            $requestData['expectationId'] = [
                'id' => $expectationId,
            ];

            $this->waitUntil(function (ApiTester $I) use ($requestData): void {
                $this->sendPut(
                    $this->getMockServerUrl().'/mockserver/verify',
                    json_encode(value: $requestData, flags: JSON_THROW_ON_ERROR),
                );

                $this->seeResponseCodeIsSuccessful();
            });
        }
    }

    /**
     * @param array<mixed> $bodyFilter
     *
     * @throws \JsonException
     * @throws \Exception
     */
    public function retrieveFromMockserver(string $path, array $bodyFilter): mixed
    {
        $request = [
            'path' => $path,
            'body' => [
                'type' => 'JSON',
                'json' => $bodyFilter,
            ],
        ];

        $this->sendPut(
            $this->getMockServerUrl().'/mockserver/retrieve?type=requests',
            json_encode(value: $request, flags: JSON_THROW_ON_ERROR)
        );

        return $this->grabDataFromResponseByJsonPath('$')[0];
    }
}
