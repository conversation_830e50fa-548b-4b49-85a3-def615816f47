<?php

declare(strict_types=1);

namespace App\Tests\Support\Helper;

// here you can define custom actions
// all public methods declared in helper class will be available in $I
use App\Tests\Support\InMemoryCache;
use Codeception\Module;
use Codeception\Module\REST;
use League\OpenAPIValidation\PSR7\ValidatorBuilder;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\Exception\ExceptionInterface as HttpClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\HttpExceptionInterface;

class Api extends Module
{
    protected ValidatorBuilder $validatorBuilder;

    public function amAuthenticatedAsPortalUser(string $cacheIdentifier, string $username, string $password): void
    {
        /** @var REST $restModule */
        $restModule = $this->getModule(name: 'REST');
        $keycloakClientId = is_string(value: $_ENV['KEYCLOAK_PORTAL_CLIENT_ID']) ? $_ENV['KEYCLOAK_PORTAL_CLIENT_ID'] : '';

        if (null === InMemoryCache::get(key: $cacheIdentifier)) {
            $accessToken = $this->getAccessTokenFromKeycloak(
                username: $username,
                password: $password,
                scope: 'myprezero-portal',
                clientId: $keycloakClientId,
            );
            assert(assertion: null !== $accessToken, description: 'Access token should not be null');
            InMemoryCache::set(key: $cacheIdentifier, value: $accessToken);
        }

        $accessToken = is_string(value: InMemoryCache::get(key: $cacheIdentifier)) ? InMemoryCache::get(key: $cacheIdentifier) : '';
        $restModule->amBearerAuthenticated(accessToken: $accessToken);
        $restModule->haveHttpHeader(name: 'x-current-user-context-id', value: 'd7241248-7264-4513-9549-291317455e0b');
    }

    private function getAccessTokenFromKeycloak(
        string $username,
        string $password,
        string $scope,
        string $clientId,
        ?string $clientSecret = null,
    ): ?string {
        $client = HttpClient::create();

        $keycloakUrl = is_string(value: $_ENV['KEYCLOAK_URL']) ? $_ENV['KEYCLOAK_URL'] : '';
        $kycloakRealm = is_string(value: $_ENV['KEYCLOAK_REALM']) ? $_ENV['KEYCLOAK_REALM'] : '';

        $requestData = [
            'grant_type' => 'password',
            'username' => $username,
            'password' => $password,
            'audience' => 'myprezero-backend',
            'scope' => $scope,
            'client_id' => $clientId,
        ];

        if (null !== $clientSecret) {
            $requestData['client_secret'] = $clientSecret;
        }

        try {
            $response = $client->request(
                method: 'POST',
                url: $keycloakUrl.'/realms/'.$kycloakRealm.'/protocol/openid-connect/token',
                options: [
                    'body' => $requestData,
                ],
            );

            $responseContent = $response->getContent();
        } catch (HttpClientExceptionInterface $e) {
            $content = $e instanceof HttpExceptionInterface ? $e->getResponse()->getContent(false) : '';
            $response = $e instanceof HttpExceptionInterface ? $e->getResponse() : null;

            $this->debugSection(title: 'Keycloak authentication request', msg: null !== $response ? $response->getInfo() : '');
            $this->debugSection(title: 'Keycloak request data', msg: $requestData);

            $this->fail(message: sprintf(
                'Failed to authenticate with Keycloak: %s Response: %s',
                $e->getMessage(),
                $content,
            ));

            return null;
        }

        $this->debugSection(title: 'Keycloak authentication request', msg: $response->getInfo());
        $this->debugSection(title: 'Keycloak authentication response', msg: $responseContent);

        try {
            /** @var array{access_token: string} $decodedResponse */
            $decodedResponse = json_decode(json: $responseContent, associative: true, depth: 512, flags: JSON_THROW_ON_ERROR);

            return $decodedResponse['access_token'];
        } catch (\JsonException $e) {
            $this->fail(message: 'Failed to decode response from Keycloak: '.$e->getMessage());

            return null;
        }
    }
}
