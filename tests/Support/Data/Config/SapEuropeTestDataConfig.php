<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Config;

class SapEuropeTestDataConfig
{
    /**
     * @var array<string, mixed>
     */
    private array $config;
    private string $currentMode;

    public function __construct()
    {
        $this->loadConfig();
        $this->currentMode = $this->determineMode();
    }

    private function loadConfig(): void
    {
        $configPath = $this->getConfigPath();
        if (!file_exists(filename: $configPath)) {
            throw new \Exception(message: "SapEurope test data configuration file not found at: {$configPath}");
        }

        $configContent = file_get_contents(filename: $configPath);
        if (false === $configContent) {
            throw new \Exception(message: 'Could not read SapEurope test data configuration file');
        }

        $decodedConfig = json_decode(json: $configContent, associative: true);
        if (!is_array(value: $decodedConfig)) {
            throw new \Exception(message: 'Invalid JSON in SapEurope test data configuration file');
        }

        /** @var array<string, mixed> $decodedConfig */
        $this->config = $decodedConfig;
    }

    private function getConfigPath(): string
    {
        if (function_exists(function: 'codecept_data_dir')) {
            $path = codecept_data_dir(appendPath: 'Config/sap-europe-test-data.json');
            if (is_string(value: $path) && file_exists(filename: $path)) {
                return $path;
            }
        }

        return __DIR__.'/sap-europe-test-data.json';
    }

    private function determineMode(): string
    {
        $testDataMode = getenv('TEST_DATA_MODE') ?: '';

        if (!empty($testDataMode)) {
            // Check if mode exists in the modes configuration
            if (isset($this->config['modes']) && is_array(value: $this->config['modes']) && isset($this->config['modes'][$testDataMode])) {
                return $testDataMode;
            }

            // Check if it's the custom mode
            if ('custom' === $testDataMode && isset($this->config['custom'])) {
                return $testDataMode;
            }
        }

        return 'ci';
    }

    public function getCountForObject(string $objectType): int
    {
        $modeConfig = $this->getModeConfig();

        if (!isset($modeConfig[$objectType])) {
            throw new \Exception(message: "No configuration found for object type: {$objectType}");
        }

        $count = $modeConfig[$objectType];
        if (!is_int(value: $count)) {
            throw new \Exception(message: "Invalid count configuration for object type: {$objectType}");
        }

        switch ($this->currentMode) {
            case 'ci':
                return 1; // Always use Create-1.json
            case 'full':
                $fileCount = $this->getFileCountForObject(objectType: $objectType);

                // If no fixture files exist, fall back to generated data with count 1
                return $fileCount > 0 ? $fileCount : 1;
            case 'dev':
            case 'stress':
            default:
                return $count; // Use configured count for generated data
        }
    }

    /**
     * @return array<string, mixed>
     */
    private function getModeConfig(): array
    {
        if (isset($this->config['modes']) && is_array(value: $this->config['modes']) && isset($this->config['modes'][$this->currentMode])) {
            $modeConfig = $this->config['modes'][$this->currentMode];
            if (is_array(value: $modeConfig)) {
                /** @var array<string, mixed> $modeConfig */
                return $modeConfig;
            }
        }

        if ('custom' === $this->currentMode && isset($this->config['custom']) && is_array(value: $this->config['custom'])) {
            /** @var array<string, mixed> $customConfig */
            $customConfig = $this->config['custom'];

            return $customConfig;
        }

        if (isset($this->config['modes']) && is_array(value: $this->config['modes']) && isset($this->config['modes']['ci']) && is_array(value: $this->config['modes']['ci'])) {
            /** @var array<string, mixed> $ciConfig */
            $ciConfig = $this->config['modes']['ci'];

            return $ciConfig;
        }

        throw new \Exception(message: 'No valid mode configuration found');
    }

    private function getFileCountForObject(string $objectType): int
    {
        $appendPath = sprintf('Fixtures/SapEurope/%s/Create-*.json', $objectType);

        if (function_exists(function: 'codecept_data_dir')) {
            $path = codecept_data_dir(appendPath: $appendPath);
            $dataDir = is_string(value: $path) && file_exists(filename: dirname(path: $path)) ? $path : __DIR__.'/../'.$appendPath;
        } else {
            $dataDir = __DIR__.'/../'.$appendPath;
        }

        $files = glob(pattern: $dataDir);

        if (false === $files) {
            // Fallback to manual path construction
            $dataDir = __DIR__.'/../'.$appendPath;
            $files = glob(pattern: $dataDir);
            if (false === $files) {
                // If no fixture files exist, return 0 instead of throwing exception
                // This allows the system to gracefully handle missing fixture directories
                return 0;
            }
        }

        return count(value: $files);
    }

    public function getCurrentMode(): string
    {
        return $this->currentMode;
    }

    /**
     * @return array<int, string>
     */
    public function getAvailableModes(): array
    {
        if (isset($this->config['modes']) && is_array(value: $this->config['modes'])) {
            return array_keys(array: $this->config['modes']);
        }

        return [];
    }

    /**
     * @param array<string, mixed> $customCounts
     */
    public function setCustomMode(array $customCounts): void
    {
        $this->config['custom'] = $customCounts;
        $this->currentMode = 'custom';
    }

    public function shouldUseFileData(): bool
    {
        $modeConfig = $this->getModeConfig();

        return array_any(array: $modeConfig, callback: fn ($count): bool => $count < 0);
    }

    public function getFileIdForObject(string $objectType): ?int
    {
        switch ($this->currentMode) {
            case 'ci':
                return 1; // Always use Create-1.json
            case 'full':
                $fileCount = $this->getFileCountForObject(objectType: $objectType);

                // If no fixture files exist, generate data instead
                return $fileCount > 0 ? 0 : null;
            case 'dev':
            case 'stress':
            default:
                return null; // Generate data, don't use files
        }
    }
}
