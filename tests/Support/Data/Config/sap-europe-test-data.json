{"description": "Configuration for SapEurope test data generation. Each object type can have different counts.", "modes": {"ci": {"description": "Fast CI/CD pipeline mode - uses Create-1.json fixture files", "BusinessPartner": -1, "Route": -1, "ServiceLocation": -1, "Contract": -1, "ContractServiceLocation": -1, "Service": -1, "Invoice": -1, "Order": -1, "VerificationDocument": -1, "SupervisionDocument": -1, "ArchiveDocument": -1}, "dev": {"description": "Dev mode - moderate test data for local testing", "BusinessPartner": 3, "Route": 2, "ServiceLocation": 4, "Contract": 5, "ContractServiceLocation": 8, "Service": 10, "Invoice": 6, "Order": 12, "VerificationDocument": 3, "SupervisionDocument": 5, "ArchiveDocument": 8}, "full": {"description": "Full test mode - uses all real test data from fixture files", "BusinessPartner": -1, "Route": -1, "ServiceLocation": -1, "Contract": -1, "ContractServiceLocation": -1, "Service": -1, "Invoice": -1, "Order": -1, "VerificationDocument": -1, "SupervisionDocument": -1, "ArchiveDocument": -1}, "stress": {"description": "Stress test mode - large amounts of generated data", "BusinessPartner": 10, "Route": 20, "ServiceLocation": 50, "Contract": 100, "ContractServiceLocation": 200, "Service": 250, "Invoice": 150, "Order": 500, "VerificationDocument": 50, "SupervisionDocument": 100, "ArchiveDocument": 200}}, "custom": {"description": "Custom mode - define your own counts per object type", "BusinessPartner": 3, "Route": 10, "ServiceLocation": 30, "Contract": 40, "ContractServiceLocation": 60, "Service": 80, "Invoice": 50, "Order": 100, "VerificationDocument": 20, "SupervisionDocument": 30, "ArchiveDocument": 60}}