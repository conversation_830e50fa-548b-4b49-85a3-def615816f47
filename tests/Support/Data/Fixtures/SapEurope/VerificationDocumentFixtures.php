<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class VerificationDocumentFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function verificationDocumentCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function verificationDocumentInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function verificationDocumentWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function verificationDocumentNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function verificationDocumentMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function verificationDocumentEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
