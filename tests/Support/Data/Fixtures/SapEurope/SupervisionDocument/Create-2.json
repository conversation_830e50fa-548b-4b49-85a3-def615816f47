{"uuid": "{{uuid}}", "id": "16926188900699", "docId": "5629992d-6d5d-4846-ae8f-ed1e4607dbb5", "verificationDocumentId": 8405, "verificationDocumentVersion": 1, "certificateNumber": "ENC210706A29", "routeId": "2580", "description": "Baumgarte - 120109 - ZL HM - VE 287668", "avvId": "120109", "avvText": "halogenfreie Bearbeitungsemulsionen und -lösungen", "amount": 2569, "amountUnitOm": "KG", "orderObjectId": "WE00000000000102696391", "orderId": "00000000000009159780", "orderPosId": 4, "orderDate": "2024-11-22", "salesOrganisationId": "0515", "createdDate": "2024-11-21", "role": "ENT", "status": "SIGNED", "supervisionDocumentPartner": [{"id": "E71100261", "checkId": "1", "type": "ERZ", "layername": "BGSENTLayer", "supervisionDocumentId": "16926188900699", "name1": "Eisengießerei Baumgarte", "street": "Duisburger Str.", "houseNumber": "35", "postalCode": "33647", "city": "Bielefeld", "country": "DE", "amount": 2569, "amountUnitOm": "KG", "takeoverDate": "2024-11-22", "businessPartnerId": "0002012384"}, {"id": "C09500000", "checkId": "7", "type": "BEF.1", "layername": "BGSENTLayer", "supervisionDocumentId": "16926188900699", "name1": "PreZero Service Mitte", "name2": "GmbH & Co. KG", "street": "Kreisstr.", "houseNumber": "30", "postalCode": "30629", "city": "Hannover", "country": "DE", "licencePlateNumber1": "MI-PR 1017", "amount": 2569, "amountUnitOm": "KG", "takeoverDate": "2024-11-22", "businessPartnerId": "0001115400"}, {"id": "C8G000000", "checkId": "4", "type": "ENT", "layername": "BGSENTLayer", "supervisionDocumentId": "16926188900699", "name1": "PreZero Service Mitte", "name2": "GmbH & Co. KG", "street": "Dieselstr.", "houseNumber": "7", "postalCode": "31789", "city": "Hameln", "country": "DE", "amount": 2569, "amountUnitOm": "KG", "takeoverDate": "2024-11-22", "businessPartnerId": "0001115400"}], "signData": [{"sequence": 1, "id": "ENT-e4db6d61-01fb-47a6-ba1c-4fd60ee01340", "count": 1, "role": "ENT", "signDate": "2024-11-27", "signTime": "12:32:06", "signer": "<PERSON><PERSON><PERSON>, <PERSON>ne", "status": "STA_OK", "statusText": "Signaturs<PERSON>us gut", "valid": "X"}, {"sequence": 2, "id": "BEF-7e827443-bad5-48f9-8e0c-8ea273732376", "count": 1, "role": "BEF", "signDate": "2024-11-26", "signTime": "12:15:26", "signer": "<PERSON><PERSON><PERSON>, <PERSON>", "status": "STA_OK", "statusText": "Signaturs<PERSON>us gut", "valid": "X"}, {"sequence": 3, "id": "ERZ-8ec6023c-0709-49bd-af83-0dcd0f114898", "count": 1, "role": "ERZ", "signDate": "2024-11-26", "signTime": "12:15:01", "signer": "<PERSON><PERSON><PERSON>, <PERSON>", "status": "STA_OK", "statusText": "Signaturs<PERSON>us gut", "valid": "X"}]}