{"uuid": "{{uuid}}", "id": "{{id}}", "docId": "{{doc_id}}", "verificationDocumentId": "{{verification_document_id}}", "verificationDocumentVersion": 1, "certificateNumber": "{{certificate_number}}", "routeId": "{{route_id}}", "description": "{{description}}", "avvId": "{{avv_id}}", "avvText": "{{avv_text}}", "amount": "{{amount}}", "amountUnitOm": "KG", "orderObjectId": "{{order_object_id}}", "orderId": "{{order_id}}", "orderPosId": "{{order_pos_id}}", "orderDate": "2024-11-22", "salesOrganisationId": "0381", "createdDate": "2024-11-21", "role": "ENT", "status": "SIGNED", "supervisionDocumentPartner": [{"id": "E71100261", "checkId": "1", "type": "ERZ", "layername": "BGSENTLayer", "supervisionDocumentId": "{{id}}", "name1": "{{partner_name1}}", "street": "{{partner_street}}", "house_number": "{{partner_house_number}}", "postalCode": "{{partner_postal_code}}", "city": "{{partner_city}}", "country": "DE", "amount": "{{amount}}", "amountUnitOm": "KG", "takeoverDate": "2024-11-22", "businessPartnerId": "{{business_partner_id}}"}, {"id": "C09500000", "checkId": "7", "type": "BEF.1", "layername": "BGSENTLayer", "supervisionDocumentId": "{{id}}", "name1": "PreZero Service Mitte", "name2": "GmbH & Co. KG", "street": "Kreisstr.", "houseNumber": "30", "postalCode": "30629", "city": "Hannover", "country": "DE", "licencePlateNumber1": "MI-PR 1017", "amount": "{{amount}}", "amountUnitOm": "KG", "takeoverDate": "2024-11-22", "businessPartnerId": "0001115400"}, {"id": "C8G000000", "checkId": "4", "type": "ENT", "layername": "BGSENTLayer", "supervisionDocumentId": "{{id}}", "name1": "PreZero Service Mitte", "name2": "GmbH & Co. KG", "street": "Dieselstr.", "houseNumber": "7", "postalCode": "31789", "city": "Hameln", "country": "DE", "amount": "{{amount}}", "amountUnitOm": "KG", "takeoverDate": "2024-11-22", "businessPartnerId": "0001115400"}], "signData": [{"sequence": 1, "id": "ENT-{{sign_data_id_1}}", "count": 1, "role": "ENT", "signDate": "2024-11-27", "signTime": "12:32:06", "signer": "{{signer_1}}", "status": "STA_OK", "statusText": "Signaturs<PERSON>us gut", "valid": "X"}, {"sequence": 2, "id": "BEF-{{sign_data_id_2}}", "count": 1, "role": "BEF", "signDate": "2024-11-26", "signTime": "12:15:26", "signer": "{{signer_2}}", "status": "STA_OK", "statusText": "Signaturs<PERSON>us gut", "valid": "X"}, {"sequence": 3, "id": "ERZ-{{sign_data_id_3}}", "count": 1, "role": "ERZ", "signDate": "2024-11-26", "signTime": "12:15:01", "signer": "{{signer_3}}", "status": "STA_OK", "statusText": "Signaturs<PERSON>us gut", "valid": "X"}]}