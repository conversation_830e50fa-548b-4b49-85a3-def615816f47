<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class SupervisionDocumentFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function supervisionDocumentCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function supervisionDocumentInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function supervisionDocumentWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function supervisionDocumentNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function supervisionDocumentMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function supervisionDocumentEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
