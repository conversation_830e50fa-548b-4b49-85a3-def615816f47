<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class ServiceLocationFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function serviceLocationCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function serviceLocationInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function serviceLocationWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function serviceLocationNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function serviceLocationMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function serviceLocationEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
