<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

trait FixtureTrait
{
    private static function getFixtureContent(string $name): string
    {
        $className = new \ReflectionClass(objectOrClass: static::class)->getShortName();
        $objectName = str_replace(search: 'Fixtures', replace: '', subject: $className);
        $path = sprintf('%s/%s/%s.json', __DIR__, $objectName, $name);

        $content = file_get_contents(filename: $path);

        if (false === $content) {
            throw new \RuntimeException(message: sprintf('File not found at path: %s', $path));
        }

        return $content;
    }

    /** @param Example<array<string, mixed>> $data */
    private static function getJson(Example $data,
    ): string {
        $json = self::getFixtureContent('Create');
        $fileId = is_int(value: $data['file_id']) ? $data['file_id'] : 1;

        if ($fileId > 0) {
            $json = self::getFixtureContent('Create-'.$fileId);
        }

        /**
         * @var string           $key
         * @var string|int|float $value
         */
        foreach ($data as $key => $value) {
            $search = '{{'.$key.'}}';
            if (is_int(value: $value) || is_float(value: $value)) {
                $search = '"{{'.$key.'}}"';
            }

            $json = str_replace(search: $search, replace: (string) $value, subject: $json);
        }

        return $json;
    }
}
