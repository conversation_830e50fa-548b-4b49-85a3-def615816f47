<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class ContractServiceLocationFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function contractServiceLocationCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function contractServiceLocationInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function contractServiceLocationWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function contractServiceLocationNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function contractServiceLocationMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function contractServiceLocationEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
