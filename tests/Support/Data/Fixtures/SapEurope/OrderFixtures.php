<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class OrderFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function orderCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function orderInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function orderWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function orderNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function orderMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function orderEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
