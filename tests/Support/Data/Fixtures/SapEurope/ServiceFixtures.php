<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class ServiceFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function serviceCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function serviceInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function serviceWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function serviceNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function serviceMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function serviceEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
