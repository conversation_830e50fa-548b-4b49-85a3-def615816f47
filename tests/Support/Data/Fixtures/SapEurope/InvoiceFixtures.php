<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class InvoiceFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function invoiceCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function invoiceInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function invoiceWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function invoiceNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function invoiceMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function invoiceEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
