<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class ContractFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function contractCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function contractInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function contractWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function contractNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function contractMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function contractEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
