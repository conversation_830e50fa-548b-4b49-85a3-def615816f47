<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;

class RouteFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     */
    public static function routeCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function routeInvalidFieldValue(
    ): string {
        return self::getFixtureContent('InvalidFieldValue');
    }

    public static function routeWrongDataType(
    ): string {
        return self::getFixtureContent('WrongDataType');
    }

    public static function routeNullValue(
    ): string {
        return self::getFixtureContent('NullValue');
    }

    public static function routeMissingRequiredField(
    ): string {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function routeEmptyBody(
    ): string {
        return self::getFixtureContent('EmptyBody');
    }
}
