<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\SapEurope;

use Codeception\Example;
use Random\RandomException;

class BusinessPartnerFixtures
{
    use FixtureTrait;

    /**
     * @param Example<array<string, mixed>> $data
     *
     * @throws RandomException
     */
    public static function businessPartnerCreate(Example $data,
    ): string {
        return self::getJson(data: $data);
    }

    public static function businessPartnerInvalidFieldValue(): string
    {
        return self::getFixtureContent('InvalidFieldValue');
    }

    /**
     * @throws RandomException
     */
    public static function businessPartnerWrongDataType(): string
    {
        return self::getFixtureContent('WrongDataType');
    }

    public static function businessPartnerNullValue(): string
    {
        return self::getFixtureContent('NullValue');
    }

    public static function businessPartnerMissingRequiredField(): string
    {
        return self::getFixtureContent('MissingRequiredField');
    }

    public static function businessPartnerEmptyBody(): string
    {
        return self::getFixtureContent('EmptyBody');
    }

    public static function response(
    ): string {
        return self::getFixtureContent('Response');
    }
}
