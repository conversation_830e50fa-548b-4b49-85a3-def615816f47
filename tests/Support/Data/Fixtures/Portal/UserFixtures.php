<?php

declare(strict_types=1);

namespace App\Tests\Support\Data\Fixtures\Portal;

class UserFixtures
{
    public static function userCreate(
        string $groupId,
    ): string {
        $unique = uniqid();

        return <<<JSON
        {
          "email": "$<EMAIL>",
          "firstname": "firstname",
          "lastname": "lastname",
          "groups": [
            {
              "id": "$groupId"
            }
          ],
          "permissions": ["permission.user.management"]
        }
        JSON;
    }

    public static function userUpdate(
        string $id,
        string $groupId,
    ): string {
        return <<<JSON
        {
          "id": "$id",
          "email": "<EMAIL>",
          "firstname": "firstname",
          "lastname": "lastname",
          "groups": [
            {
              "id": "$groupId"
            }
          ],
          "permissions": ["permission.order.create"]
        }
        JSON;
    }
}
