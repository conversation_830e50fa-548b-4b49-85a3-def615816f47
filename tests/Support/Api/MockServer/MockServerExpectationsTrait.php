<?php

declare(strict_types=1);

namespace App\Tests\Support\Api\MockServer;

use Random\RandomException;

trait MockServerExpectationsTrait
{
    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapEnsSignatureCallExpectation(
        string $documentId = 'fdb883b9-2b3d-4420-ba14-2e84e9576cc6',
    ): mixed {
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')/signatureDatas';

        $responseBody = [
            'd' => [
                'results' => [
                    [
                        '__metadata' => [
                            'id' => 'https://F0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'1\',id=\'ENT-5a8962d0-fb7f-4143-8ba2-ba2468ff7f0b\')',
                            'uri' => 'https://F0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'1\',id=\'ENT-5a8962d0-fb7f-4143-8ba2-ba2468ff7f0b\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SignatureDataType',
                        ],
                        'docId' => $documentId,
                        'sequence' => '1',
                        'id' => 'ENT-5a8962d0-fb7f-4143-8ba2-ba2468ff7f0b',
                        'count' => '1',
                        'role' => 'ENT',
                        'signDate' => '/Date(1741651200000)/',
                        'signTime' => '13:36:25',
                        'signer' => 'Bialek, Dennis',
                        'status' => 'STA_OK',
                        'statusText' => 'Signaturstatus gut',
                        'valid' => 'X',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https://F0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'2\',id=\'ERZ-d8073550-11dd-44f8-a585-c1e12ccc2c75\')',
                            'uri' => 'https://F0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'2\',id=\'ERZ-d8073550-11dd-44f8-a585-c1e12ccc2c75\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SignatureDataType',
                        ],
                        'docId' => $documentId,
                        'sequence' => '2',
                        'id' => 'ERZ-d8073550-11dd-44f8-a585-c1e12ccc2c75',
                        'count' => '1',
                        'role' => 'ERZ',
                        'signDate' => '/Date(1741651200000)/',
                        'signTime' => '06:09:46',
                        'signer' => 'Schwenk, Max',
                        'status' => 'STA_OK',
                        'statusText' => 'Signaturstatus gut',
                        'valid' => 'X',
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapEnsPartnerCallExpectation(
        string $documentId = 'fdb883b9-2b3d-4420-ba14-2e84e9576cc6',
        string $firstBusinessPartnerId = '2144108',
        string $secondBusinessPartnerId = '2039749',
    ): mixed {
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')/verificationDocumentPartners';

        $responseBody = [
            'd' => [
                'results' => [
                    [
                        '__metadata' => [
                            'id' => 'https://F0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocumentPartners(docId=\''.$documentId.'\',id=\'E77003080\',checkId=\'\')',
                            'uri' => 'https://F0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocumentPartners(docId=\''.$documentId.'\',id=\'E77003080\',checkId=\'\')',
                            'type' => 'ZISU_WEBSHOP_SRV.VerificationDocumentPartnerType',
                        ],
                        'docId' => $documentId,
                        'roleAllowed' => false,
                        'id' => 'E77003080',
                        'checkId' => '',
                        'type' => 'ERZ',
                        'businessPartnerId' => $firstBusinessPartnerId,
                        'verificationDocumentId' => '0000000000',
                        'verificationDocumentVersion' => '00',
                        'name1' => 'Follmann Chemie GmbH',
                        'name2' => '',
                        'street' => 'Heinrich-Follmann-Str. 1',
                        'houseNumber' => '',
                        'postalCode' => '32423',
                        'city' => 'Minden',
                        'district' => '',
                        'country' => '',
                        'contactPerson' => '',
                        'telephone' => '0571/9339-127',
                        'fax' => '',
                        'email' => '',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https://F0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocumentPartners(docId=\''.$documentId.'\',id=\'E15417040\',checkId=\'\')',
                            'uri' => 'https://F0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocumentPartners(docId=\''.$documentId.'\',id=\'E15417040\',checkId=\'\')',
                            'type' => 'ZISU_WEBSHOP_SRV.VerificationDocumentPartnerType',
                        ],
                        'docId' => $documentId,
                        'roleAllowed' => true,
                        'id' => 'E15417040',
                        'checkId' => '',
                        'type' => 'ENT',
                        'businessPartnerId' => $secondBusinessPartnerId,
                        'verificationDocumentId' => '0000000000',
                        'verificationDocumentVersion' => '00',
                        'name1' => 'RCN Chemie GmbH & Co. KG',
                        'name2' => '',
                        'street' => 'Daimlerstr. 26',
                        'houseNumber' => '',
                        'postalCode' => '47574',
                        'city' => 'Goch',
                        'district' => '',
                        'country' => '',
                        'contactPerson' => '',
                        'telephone' => '',
                        'fax' => '',
                        'email' => '',
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapEnsDetailCallExpectation(
        string $documentId = 'fdb883b9-2b3d-4420-ba14-2e84e9576cc6',
        string $businessPartnerId = '2144108',
    ): mixed {
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')';

        $responseBody = [
            'd' => [
                '__metadata' => [
                    'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')',
                    'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')',
                    'type' => 'ZISU_WEBSHOP_SRV.VerificationDocumentType',
                ],
                'id' => '18451',
                'version' => '1',
                'certificateNumber' => 'ENE1WGX01281',
                'certificateType' => 'EN',
                'businessPartnerId' => $businessPartnerId,
                'salesOrganisationId' => '0515',
                'contractId' => '',
                'avvId' => '080113',
                'avvText' => 'Farb- und Lackschlämme, die organische Lösemittel oder andere gefährliche Stoffe enthalten',
                'serviceLocationId' => '526953',
                'approvedAmountDocument' => '600.000',
                'approvedAmountYear' => '600.000',
                'approvedRemAmountDocument' => '592.321',
                'approvedRemAmountYear' => '592.321',
                'amountUnitOM' => 'TO',
                'deletionFlag' => '',
                'applicationDate' => '/Date(1741305600000)/',
                'dateCustomer' => '/Date(1741564800000)/',
                'dateDisposer' => '/Date(1741651200000)/',
                'confirmationAuthority' => null,
                'approval' => '/Date(1741651200000)/',
                'endDate' => '/Date(1899331200000)/',
                'lockedDate' => null,
                'objectId' => 'ZE00000000000106884573',
                'description' => 'Follmann - 080113 - RCN Goch',
                'docId' => $documentId,
                'status' => 'EGEN',
                'statusText' => 'EN genehmigt',
                'dataStatus' => '',
                'signatureDatas' => [
                    '__deferred' => [
                        'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')/signatureDatas',
                    ],
                ],
                'verificationDocumentPartners' => [
                    '__deferred' => [
                        'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')/verificationDocumentPartners',
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapBgsSignatureDataCallExpectation(
        string $documentId = 'f09d26a3-9952-4a15-b739-bc744acadf93',
    ): mixed {
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')/signatureDatas';

        $responseBody = [
            'd' => [
                'results' => [
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'1\',id=\'ENT-caab1a8a-eb43-4d9d-b141-9af220ee94bc\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'1\',id=\'ENT-caab1a8a-eb43-4d9d-b141-9af220ee94bc\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SignatureDataType',
                        ],
                        'docId' => $documentId,
                        'sequence' => '1',
                        'id' => 'ENT-caab1a8a-eb43-4d9d-b141-9af220ee94bc',
                        'count' => '1',
                        'role' => 'ENT',
                        'signDate' => '/Date(1752796800000)/',
                        'signTime' => '10 => 30 => 32',
                        'signer' => 'Niehoff, Susanne',
                        'status' => 'STA_OK',
                        'statusText' => 'Signaturstatus gut',
                        'valid' => 'X',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'2\',id=\'BEF-3919cfa1-d0fb-4cd2-a7e6-f687ad9eff2d\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'2\',id=\'BEF-3919cfa1-d0fb-4cd2-a7e6-f687ad9eff2d\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SignatureDataType',
                        ],
                        'docId' => $documentId,
                        'sequence' => '2',
                        'id' => 'BEF-3919cfa1-d0fb-4cd2-a7e6-f687ad9eff2d',
                        'count' => '1',
                        'role' => 'BEF',
                        'signDate' => '/Date(1752537600000)/',
                        'signTime' => '13 => 47 => 27',
                        'signer' => 'Rossa, Thomas Franz',
                        'status' => 'STA_OK',
                        'statusText' => 'Signaturstatus gut',
                        'valid' => 'X',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'3\',id=\'ERZ-512ba408-1430-42aa-a8de-458447c99909\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'3\',id=\'ERZ-512ba408-1430-42aa-a8de-458447c99909\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SignatureDataType',
                        ],
                        'docId' => $documentId,
                        'sequence' => '3',
                        'id' => 'ERZ-512ba408-1430-42aa-a8de-458447c99909',
                        'count' => '1',
                        'role' => 'ERZ',
                        'signDate' => '/Date(1752537600000)/',
                        'signTime' => '09 => 57 => 58',
                        'signer' => 'Kruse, Edgar',
                        'status' => 'STA_OK',
                        'statusText' => 'Signaturstatus gut',
                        'valid' => 'X',
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapBgsPartnerCallExpectation(
        string $documentId = 'f09d26a3-9952-4a15-b739-bc744acadf93',
        string $firstBusinessPartnerId = '2144108',
        string $secondBusinessPartnerId = '1115400',
        string $thirdBusinessPartnerId = '1115400',
    ): mixed {
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')/supervisionDocumentPartners';

        $responseBody = [
            'd' => [
                'results' => [
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocumentPartners(docId=\''.$documentId.'\',id=\'E77003080\',checkId=\'6\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocumentPartners(docId=\''.$documentId.'\',id=\'E77003080\',checkId=\'6\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SupervisionDocumentPartnerType',
                        ],
                        'businessPartnerId' => $firstBusinessPartnerId,
                        'docId' => $documentId,
                        'id' => 'E77003080',
                        'checkId' => '6',
                        'type' => 'ERZ',
                        'layername' => 'BGSENTLayer',
                        'supervisionDocumentId' => '',
                        'name1' => 'Follmann Chemie GmbH',
                        'name2' => '',
                        'street' => 'Heinrich-Follmann-Str.',
                        'houseNumber' => '1',
                        'postalCode' => '32423',
                        'city' => 'Minden',
                        'district' => '',
                        'country' => 'DE',
                        'contactPerson' => '',
                        'telephone' => '',
                        'fax' => '',
                        'email' => '',
                        'licencePlateNumber1' => '',
                        'licencePlateNumber2' => '',
                        'amount' => '1600.000',
                        'amountUnitOM' => 'KG',
                        'takeoverDate' => '/Date(1752624000000)/',
                        'receiptFlag' => false,
                        'roleAllowed' => false,
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocumentPartners(docId=\''.$documentId.'\',id=\'C09500000\',checkId=\'7\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocumentPartners(docId=\''.$documentId.'\',id=\'C09500000\',checkId=\'7\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SupervisionDocumentPartnerType',
                        ],
                        'businessPartnerId' => $secondBusinessPartnerId,
                        'docId' => $documentId,
                        'id' => 'C09500000',
                        'checkId' => '7',
                        'type' => 'BEF.1',
                        'layername' => 'BGSENTLayer',
                        'supervisionDocumentId' => '',
                        'name1' => 'PreZero Service Mitte',
                        'name2' => 'GmbH & Co. KG',
                        'street' => 'Kreisstr.',
                        'houseNumber' => '30',
                        'postalCode' => '30629',
                        'city' => 'Hannover',
                        'district' => '',
                        'country' => 'DE',
                        'contactPerson' => '',
                        'telephone' => '',
                        'fax' => '',
                        'email' => '',
                        'licencePlateNumber1' => 'MI-GK 1042',
                        'licencePlateNumber2' => '',
                        'amount' => '1600.000',
                        'amountUnitOM' => 'KG',
                        'takeoverDate' => '/Date(1752624000000)/',
                        'receiptFlag' => false,
                        'roleAllowed' => false,
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocumentPartners(docId=\''.$documentId.'\',id=\'C8G000000\',checkId=\'4\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocumentPartners(docId=\''.$documentId.'\',id=\'C8G000000\',checkId=\'4\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SupervisionDocumentPartnerType',
                        ],
                        'businessPartnerId' => $thirdBusinessPartnerId,
                        'docId' => $documentId,
                        'id' => 'C8G000000',
                        'checkId' => '4',
                        'type' => 'ENT',
                        'layername' => 'BGSENTLayer',
                        'supervisionDocumentId' => '',
                        'name1' => 'PreZero Service Mitte',
                        'name2' => 'GmbH & Co. KG',
                        'street' => 'Dieselstr.',
                        'houseNumber' => '7',
                        'postalCode' => '31789',
                        'city' => 'Hameln',
                        'district' => '',
                        'country' => 'DE',
                        'contactPerson' => '',
                        'telephone' => '',
                        'fax' => '',
                        'email' => '',
                        'licencePlateNumber1' => '',
                        'licencePlateNumber2' => '',
                        'amount' => '1600.000',
                        'amountUnitOM' => 'KG',
                        'takeoverDate' => '/Date(1752624000000)/',
                        'receiptFlag' => false,
                        'roleAllowed' => false,
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapBgsDetailCallExpectation(
        string $documentId = 'f09d26a3-9952-4a15-b739-bc744acadf93',
    ): mixed {
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')';

        $responseBody = [
            'd' => [
                '__metadata' => [
                    'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')',
                    'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')',
                    'type' => 'ZISU_WEBSHOP_SRV.SupervisionDocumentType',
                ],
                'id' => '16823722385697',
                'docId' => $documentId,
                'verificationDocumentId' => '18113',
                'verificationDocumentVersion' => '1',
                'certificateNumber' => 'ENC241114A19',
                'routeId' => '2563',
                'description' => 'Follmann - 150110 HDPE KU - ZL HM - VE 257845',
                'avvId' => '150110',
                'avvText' => 'Verpackungen, die Rückstände gefährlicher Stoffe enthalten oder durch gefährliche Stoffe verunreinigt sind',
                'wasteMaterialId' => '',
                'amount' => '1600.000',
                'amountUnitOm' => 'KG',
                'orderObjectId' => 'WE00000000000111221653',
                'orderId' => '9858331',
                'orderPosId' => '0002',
                'orderDate' => '/Date(1752624000000)/',
                'salesOrganisationId' => '0515',
                'createdDate' => '/Date(1752537600000)/',
                'role' => 'ENT',
                'status' => 'SIGNED',
                'dataStatus' => '',
                'supervisionDocumentReceipts' => [
                    '__deferred' => [
                        'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')/supervisionDocumentReceipts',
                    ],
                ],
                'supervisionDocumentLayers' => [
                    '__deferred' => [
                        'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')/supervisionDocumentLayers',
                    ],
                ],
                'signatureDatas' => [
                    '__deferred' => [
                        'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')/signatureDatas',
                    ],
                ],
                'supervisionDocumentPartners' => [
                    '__deferred' => [
                        'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')/supervisionDocumentPartners',
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapBgsSignatureCallExpectation(
        string $documentId = '582b91ca-33b4-4d2b-bd86-6b4b6fba848a',
    ): mixed {
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/supervisionDocuments(\''.$documentId.'\')/signatureDatas';

        $responseBody = [
            'd' => [
                'results' => [
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'1\',id=\'ENT-caab1a8a-eb43-4d9d-b141-9af220ee94bc\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'1\',id=\'ENT-caab1a8a-eb43-4d9d-b141-9af220ee94bc\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SignatureDataType',
                        ],
                        'docId' => $documentId,
                        'sequence' => '1',
                        'id' => 'ENT-caab1a8a-eb43-4d9d-b141-9af220ee94bc',
                        'count' => '1',
                        'role' => 'ENT',
                        'signDate' => '/Date(1752796800000)/',
                        'signTime' => '10 => 30 => 32',
                        'signer' => 'Niehoff, Susanne',
                        'status' => 'STA_OK',
                        'statusText' => 'Signaturstatus gut',
                        'valid' => 'X',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'2\',id=\'BEF-3919cfa1-d0fb-4cd2-a7e6-f687ad9eff2d\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'2\',id=\'BEF-3919cfa1-d0fb-4cd2-a7e6-f687ad9eff2d\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SignatureDataType',
                        ],
                        'docId' => $documentId,
                        'sequence' => '2',
                        'id' => 'BEF-3919cfa1-d0fb-4cd2-a7e6-f687ad9eff2d',
                        'count' => '1',
                        'role' => 'BEF',
                        'signDate' => '/Date(1752537600000)/',
                        'signTime' => '13 => 47 => 27',
                        'signer' => 'Rossa, Thomas Franz',
                        'status' => 'STA_OK',
                        'statusText' => 'Signaturstatus gut',
                        'valid' => 'X',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'3\',id=\'ERZ-512ba408-1430-42aa-a8de-458447c99909\')',
                            'uri' => 'https => //F0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/signatureDatas(docId=\''.$documentId.'\',sequence=\'3\',id=\'ERZ-512ba408-1430-42aa-a8de-458447c99909\')',
                            'type' => 'ZISU_WEBSHOP_SRV.SignatureDataType',
                        ],
                        'docId' => $documentId,
                        'sequence' => '3',
                        'id' => 'ERZ-512ba408-1430-42aa-a8de-458447c99909',
                        'count' => '1',
                        'role' => 'ERZ',
                        'signDate' => '/Date(1752537600000)/',
                        'signTime' => '09 => 57 => 58',
                        'signer' => 'Kruse, Edgar',
                        'status' => 'STA_OK',
                        'statusText' => 'Signaturstatus gut',
                        'valid' => 'X',
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapEnsCallExpectation(
        string $search = '2144108',
        string $synchroDate = '20250710081232',
        string $documentId = 'e2788107-a02d-4fff-b0ce-da6b978e06f1',
        string $businessPartnerId = '2144108',
    ): mixed {
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments?search=\''.$search.'\'%26synchroDate%3d'.$synchroDate;

        $responseBody = [
            'd' => [
                '__metadata' => [
                    'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')',
                    'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')',
                    'type' => 'ZISU_WEBSHOP_SRV.VerificationDocumentType',
                ],
                'id' => '18420',
                'version' => '1',
                'certificateNumber' => '',
                'certificateType' => 'EN',
                'businessPartnerId' => $businessPartnerId,
                'salesOrganisationId' => '0515',
                'contractId' => '40083008',
                'avvId' => '170204',
                'avvText' => 'Glas, Kunststoff und Holz, die gefährliche Stoffe enthalten oder durch gefährliche Stoffe verunreinigt sind',
                'serviceLocationId' => '526953',
                'approvedAmountDocument' => '1234.000',
                'approvedAmountYear' => '20.000',
                'approvedRemAmountDocument' => '1234.000',
                'approvedRemAmountYear' => '20.000',
                'amountUnitOM' => 'TO',
                'deletionFlag' => '',
                'applicationDate' => '\/Date(1746662400000)\/',
                'dateCustomer' => '\/Date(1746662400000)\/',
                'dateDisposer' => null,
                'confirmationAuthority' => null,
                'approval' => null,
                'endDate' => null,
                'lockedDate' => null,
                'objectId' => 'ZE00000000000106342349',
                'description' => 'Test OP25_24 Korrektur  zu INC10778549',
                'docId' => $documentId,
                'status' => 'EKUN',
                'statusText' => 'EN Versand Kunde',
                'dataStatus' => '',
                'signatureDatas' => [
                    '__deferred' => [
                        'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')/signatureDatas',
                    ],
                ],
                'verificationDocumentPartners' => [
                    '__deferred' => [
                        'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/verificationDocuments(\''.$documentId.'\')/verificationDocumentPartners',
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws RandomException
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapServiceCallExpectation(): mixed
    {
        $url = '/sap-europe/services';

        $requestBody = [
            'extServiceId' => '',
            'startDate' => '2025-12-12 00:00:00',
            'endDate' => '2025-12-12 00:00:00',
            'period' => '1',
            'contractId' => '41543800',
            'contractPosId' => '10',
            'serviceType' => '10',
            'containerMaterialId' => '751226',
            'wasteMaterialId' => '702627',
            'serviceLocationId' => '1385429',
            'containerCount' => 2,
            'equipment' => '000000000002721065',
            'routeId' => '37104',
            'serviceFrequency' => 'D',
            'dailyFrequency' => '1',
            'noticeTexts' => [
                [
                    'type' => 'ZDRIV',
                    'content' => '',
                ],
            ],
        ];

        $responseBody = [
            'd' => [
                '__metadata' => [
                    'id' => "http://api.prezero.network:1337/sap/opu/odata/sap/service/ressource(id='13371337',posId='13')",
                    'uri' => "http://api.prezero.network:1337/sap/opu/odata/sap/service/ressource(id='13371337',posId='13')",
                    'type' => 'service.foo',
                ],
                'id' => (string) random_int(min: 1000000, max: 9999999),
                'posId' => '1',
                'extServiceId' => '2601375',
                'startDate' => '2025-12-12 00:00:00',
                'endDate' => '2025-12-12 00:00:00',
                'period' => '1',
                'contractId' => '41543800',
                'contractPosId' => '10',
                'serviceType' => '10',
                'containerMaterialId' => '751226',
                'wasteMaterialId' => '702627',
                'serviceLocationId' => '1385429',
                'containerCount' => 2,
                'equipment' => '000000000002721065',
                'routeId' => '37104',
                'serviceFrequency' => 'D',
                'dailyFrequency' => '1',
                'noticeTexts' => [
                    [
                        'type' => 'ZDRIV',
                        'content' => '',
                    ],
                ],
            ],
        ];

        $this->callMockServerSetPermanentExpectation(
            method: 'POST',
            path: $url,
            requestBody: $requestBody,
            responseBody: $responseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }

    /**
     * @throws \JsonException
     * @throws \Exception
     */
    public function setSapWasteStatisticsCallExpectation(
        string $businessPartnerId = '272144108',
    ): mixed {
        $filter = 'businessPartnerId+eq+%'.$businessPartnerId.'%27+and+dateFrom+eq+%2720250101%27+and+dateTo+eq+%2720251231%27';
        $url = '/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics?%24filter='.$filter;

        $responseBody = [
            'd' => [
                'results' => [
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'\',contractId=\'41583415\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'\',contractId=\'41583415\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '',
                        'serviceLocationAddress' => '',
                        'serviceLocationInfo' => '',
                        'avvId' => '130205',
                        'avvText' => 'nichtchlorierte Maschinen-, Getriebe- und Schmieröle auf Mineralölbasis',
                        'wasteMaterialId' => '702074',
                        'wasteMaterialText' => 'Altöl halogenfrei-130205*-ABF',
                        'orderDate' => '2025-02-20',
                        'amount' => '0.45',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '122.63',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055778471',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41583415',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1002466\',contractId=\'41408604\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1002466\',contractId=\'41408604\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '1002466',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Zum Industriehafen 8 , 32423 Minden',
                        'serviceLocationInfo' => 'Zum Industriehafen 8 Werk II',
                        'avvId' => '170201',
                        'avvText' => 'Holz',
                        'wasteMaterialId' => '701646',
                        'wasteMaterialText' => 'Altholz A2-170201-ABF',
                        'orderDate' => '2025-01-14',
                        'amount' => '0.62',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '39.28',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055512936',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41408604',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1002466\',contractId=\'\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1002466\',contractId=\'\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '1002466',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Zum Industriehafen 8 , 32423 Minden',
                        'serviceLocationInfo' => 'Zum Industriehafen 8 Werk II',
                        'avvId' => '191207',
                        'avvText' => 'Holz mit Ausnahme desjenigen, das unter 19 12 06 fällt',
                        'wasteMaterialId' => '702696',
                        'wasteMaterialText' => 'Altholz bis A3-191207-ABF',
                        'orderDate' => '2025-01-23',
                        'amount' => '0.72',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '31.21',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055587469',
                        'subsequentContractPosId' => '000070',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1002466\',contractId=\'41380196\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1002466\',contractId=\'41380196\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '1002466',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Zum Industriehafen 8 , 32423 Minden',
                        'serviceLocationInfo' => 'Zum Industriehafen 8 Werk II',
                        'avvId' => '200301',
                        'avvText' => 'gemischte Siedlungsabfälle',
                        'wasteMaterialId' => '707966',
                        'wasteMaterialText' => 'Abfall zur energet. Verwert.-200301-ABF',
                        'orderDate' => '2025-01-16',
                        'amount' => '1.15',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '215.26',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055516847',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41380196',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1002466\',contractId=\'41380196\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1002466\',contractId=\'41380196\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '1002466',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Zum Industriehafen 8 , 32423 Minden',
                        'serviceLocationInfo' => 'Zum Industriehafen 8 Werk II',
                        'avvId' => '200301',
                        'avvText' => 'gemischte Siedlungsabfälle',
                        'wasteMaterialId' => '707966',
                        'wasteMaterialText' => 'Abfall zur energet. Verwert.-200301-ABF',
                        'orderDate' => '2025-01-23',
                        'amount' => '0.91',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '170.33',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055587467',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41380196',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1321448\',contractId=\'41593444\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'1321448\',contractId=\'41593444\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '1321448',
                        'serviceLocationAddress' => 'Duni GmbH  , Robert-Bosch-Str. 4 , 49565 Bramsche',
                        'serviceLocationInfo' => '',
                        'avvId' => '080111',
                        'avvText' => 'Farb- und Lackabfälle, die organische Lösemittel oder andere gefährliche Stoffe enthalten',
                        'wasteMaterialId' => '701710',
                        'wasteMaterialText' => 'Farb/Lackab d org Lösem ent-080111*-ABF',
                        'orderDate' => '2025-01-23',
                        'amount' => '0.796',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '515.17',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055610971',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41593444',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41388828\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41388828\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '070312',
                        'avvText' => 'Schlämme aus der betriebseigenen Abwasserbehandlung mit Ausnahme derjenigen, die unter 07 03 11 fallen',
                        'wasteMaterialId' => '701971',
                        'wasteMaterialText' => 'Schlämme-070312ü-ABF',
                        'orderDate' => '2025-01-06',
                        'amount' => '4.0',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055548895',
                        'subsequentContractPosId' => '000045',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41388828',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41388828\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41388828\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '070312',
                        'avvText' => 'Schlämme aus der betriebseigenen Abwasserbehandlung mit Ausnahme derjenigen, die unter 07 03 11 fallen',
                        'wasteMaterialId' => '701971',
                        'wasteMaterialText' => 'Schlämme-070312ü-ABF',
                        'orderDate' => '2025-01-10',
                        'amount' => '4.38',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055506193',
                        'subsequentContractPosId' => '000045',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41388828',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '070312',
                        'avvText' => 'Schlämme aus der betriebseigenen Abwasserbehandlung mit Ausnahme derjenigen, die unter 07 03 11 fallen',
                        'wasteMaterialId' => '701971',
                        'wasteMaterialText' => 'Schlämme-070312ü-ABF',
                        'orderDate' => '2025-01-14',
                        'amount' => '4.23',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055512928',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41031379',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '070312',
                        'avvText' => 'Schlämme aus der betriebseigenen Abwasserbehandlung mit Ausnahme derjenigen, die unter 07 03 11 fallen',
                        'wasteMaterialId' => '701971',
                        'wasteMaterialText' => 'Schlämme-070312ü-ABF',
                        'orderDate' => '2025-01-16',
                        'amount' => '4.56',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055516857',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41031379',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '070312',
                        'avvText' => 'Schlämme aus der betriebseigenen Abwasserbehandlung mit Ausnahme derjenigen, die unter 07 03 11 fallen',
                        'wasteMaterialId' => '701971',
                        'wasteMaterialText' => 'Schlämme-070312ü-ABF',
                        'orderDate' => '2025-01-21',
                        'amount' => '4.93',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055571506',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41031379',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41388828\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41388828\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '070312',
                        'avvText' => 'Schlämme aus der betriebseigenen Abwasserbehandlung mit Ausnahme derjenigen, die unter 07 03 11 fallen',
                        'wasteMaterialId' => '701971',
                        'wasteMaterialText' => 'Schlämme-070312ü-ABF',
                        'orderDate' => '2025-01-23',
                        'amount' => '3.15',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055587471',
                        'subsequentContractPosId' => '000045',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41388828',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '070312',
                        'avvText' => 'Schlämme aus der betriebseigenen Abwasserbehandlung mit Ausnahme derjenigen, die unter 07 03 11 fallen',
                        'wasteMaterialId' => '701971',
                        'wasteMaterialText' => 'Schlämme-070312ü-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '4.04',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055630497',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41031379',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41031379\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '070312',
                        'avvText' => 'Schlämme aus der betriebseigenen Abwasserbehandlung mit Ausnahme derjenigen, die unter 07 03 11 fallen',
                        'wasteMaterialId' => '701971',
                        'wasteMaterialText' => 'Schlämme-070312ü-ABF',
                        'orderDate' => '2025-01-30',
                        'amount' => '3.81',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055676427',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41031379',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41202269\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41202269\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '150101',
                        'avvText' => 'Verpackungen aus Papier und Pappe',
                        'wasteMaterialId' => '701772',
                        'wasteMaterialText' => '1.04 B19 Kartonage-150101-ABF',
                        'orderDate' => '2025-01-29',
                        'amount' => '3.13',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '-225.2',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055644957',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41202269',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41050468\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41050468\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '150102',
                        'avvText' => 'Verpackungen aus Kunststoff',
                        'wasteMaterialId' => '703663',
                        'wasteMaterialText' => 'Folie 80/20-150102-ABF',
                        'orderDate' => '2025-01-13',
                        'amount' => '2.41',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '-445.85',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055512676',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41050468',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41204364\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41204364\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '150106',
                        'avvText' => 'gemischte Verpackungen',
                        'wasteMaterialId' => '707951',
                        'wasteMaterialText' => 'Abfall zur Vorbehandlung-150106-ABF',
                        'orderDate' => '2025-01-13',
                        'amount' => '4.66',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '872.26',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055512641',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41204364',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41203263\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41203263\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '150106',
                        'avvText' => 'gemischte Verpackungen',
                        'wasteMaterialId' => '707958',
                        'wasteMaterialText' => 'Abfall zur energet. Verwert.-150106-ABF',
                        'orderDate' => '2025-01-13',
                        'amount' => '3.92',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '733.75',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055512636',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41203263',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41035244\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41035244\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '150106',
                        'avvText' => 'gemischte Verpackungen',
                        'wasteMaterialId' => '707958',
                        'wasteMaterialText' => 'Abfall zur energet. Verwert.-150106-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '2.82',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '556.05',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055630385',
                        'subsequentContractPosId' => '000090',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41035244',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41203263\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41203263\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '150106',
                        'avvText' => 'gemischte Verpackungen',
                        'wasteMaterialId' => '707958',
                        'wasteMaterialText' => 'Abfall zur energet. Verwert.-150106-ABF',
                        'orderDate' => '2025-01-29',
                        'amount' => '4.55',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '851.67',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055644956',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41203263',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41150306\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41150306\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '170201',
                        'avvText' => 'Holz',
                        'wasteMaterialId' => '702283',
                        'wasteMaterialText' => 'Altholz Baustellen A3-170201-ABF',
                        'orderDate' => '2025-01-14',
                        'amount' => '0.51',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '22.11',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055512933',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41150306',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41635137\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'41635137\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '170603',
                        'avvText' => 'anderes Dämmmaterial, das aus gefährlichen Stoffen besteht oder solche Stoffe enthält',
                        'wasteMaterialId' => '702512',
                        'wasteMaterialText' => 'Dämmmaterial gefährlich-170603*-ABF',
                        'orderDate' => '2025-01-16',
                        'amount' => '0.2',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '70.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055516851',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41635137',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '191207',
                        'avvText' => 'Holz mit Ausnahme desjenigen, das unter 19 12 06 fällt',
                        'wasteMaterialId' => '702696',
                        'wasteMaterialText' => 'Altholz bis A3-191207-ABF',
                        'orderDate' => '2025-01-02',
                        'amount' => '0.73',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '31.65',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055544895',
                        'subsequentContractPosId' => '000070',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'502875\',contractId=\'\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '502875',
                        'serviceLocationAddress' => 'Follmann & Co. Gesellschaft für Chemiewerkstofe u. Verfahrenstechnik mbH , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'WERK 1',
                        'avvId' => '191207',
                        'avvText' => 'Holz mit Ausnahme desjenigen, das unter 19 12 06 fällt',
                        'wasteMaterialId' => '702696',
                        'wasteMaterialText' => 'Altholz bis A3-191207-ABF',
                        'orderDate' => '2025-01-27',
                        'amount' => '0.49',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '21.25',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055630338',
                        'subsequentContractPosId' => '000070',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41587542\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41587542\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '070208',
                        'avvText' => 'andere Reaktions- und Destillationsrückstände',
                        'wasteMaterialId' => '702346',
                        'wasteMaterialText' => 'and Reaktions u Destillatio-070208*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '0.906',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '504.82',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657871',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41587542',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41464292\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41464292\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '070208',
                        'avvText' => 'andere Reaktions- und Destillationsrückstände',
                        'wasteMaterialId' => '702346',
                        'wasteMaterialText' => 'and Reaktions u Destillatio-070208*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '0.533',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '296.99',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657844',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41464292',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41434331\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41434331\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '070704',
                        'avvText' => 'andere organische Lösemittel, Waschflüssigkeiten und Mutterlaugen',
                        'wasteMaterialId' => '701663',
                        'wasteMaterialText' => 'and org Lösem Waschf u Laug-070704*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '0.194',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '57.66',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657941',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41434331',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384760\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384760\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '070704',
                        'avvText' => 'andere organische Lösemittel, Waschflüssigkeiten und Mutterlaugen',
                        'wasteMaterialId' => '701663',
                        'wasteMaterialText' => 'and org Lösem Waschf u Laug-070704*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '1.013',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '301.07',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657894',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41384760',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384760\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384760\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '070704',
                        'avvText' => 'andere organische Lösemittel, Waschflüssigkeiten und Mutterlaugen',
                        'wasteMaterialId' => '701663',
                        'wasteMaterialText' => 'and org Lösem Waschf u Laug-070704*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '2.092',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '621.74',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657825',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41384760',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384628\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384628\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '080112',
                        'avvText' => 'Farb- und Lackabfälle mit Ausnahme derjenigen, die unter 08 01 11 fallen',
                        'wasteMaterialId' => '701737',
                        'wasteMaterialText' => 'Farb u Lack Abf-080112-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '2.762',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '1041.83',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657947',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41384628',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41530786\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41530786\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '080410',
                        'avvText' => 'Klebstoff- und Dichtmassenabfälle mit Ausnahme derjenigen, die unter 08 04 09 fallen',
                        'wasteMaterialId' => '701713',
                        'wasteMaterialText' => 'Klebst u Dichtm abf m Ausn-080410-ABF',
                        'orderDate' => '2025-01-21',
                        'amount' => '26.1',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '8931.42',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055563967',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41530786',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41583415\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41583415\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '130205',
                        'avvText' => 'nichtchlorierte Maschinen-, Getriebe- und Schmieröle auf Mineralölbasis',
                        'wasteMaterialId' => '702074',
                        'wasteMaterialText' => 'Altöl halogenfrei-130205*-ABF',
                        'orderDate' => '2025-01-24',
                        'amount' => '0.107',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '29.16',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055619357',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41583415',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384731\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384731\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '150110',
                        'avvText' => 'Verpackungen, die Rückstände gefährlicher Stoffe enthalten oder durch gefährliche Stoffe verunreinigt sind',
                        'wasteMaterialId' => '702091',
                        'wasteMaterialText' => 'Eisenbehältn mit Restanhaft-150110*-ABF',
                        'orderDate' => '2025-01-13',
                        'amount' => '2.92',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '1282.46',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055514417',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41384731',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384731\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384731\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '150110',
                        'avvText' => 'Verpackungen, die Rückstände gefährlicher Stoffe enthalten oder durch gefährliche Stoffe verunreinigt sind',
                        'wasteMaterialId' => '702091',
                        'wasteMaterialText' => 'Eisenbehältn mit Restanhaft-150110*-ABF',
                        'orderDate' => '2025-01-27',
                        'amount' => '2.05',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '900.36',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055621105',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41384731',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384718\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384718\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '150202',
                        'avvText' => 'Aufsaug- und Filtermaterialien (einschließlich Ölfilter a. n. g.), Wischtücher und Schutzkleidung, die durch gefährliche Stoffe verunreinigt sind',
                        'wasteMaterialId' => '701718',
                        'wasteMaterialText' => 'Aufs u Filtermat d gef St-150202*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '0.34',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '121.79',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657924',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41384718',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41587654\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41587654\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '150202',
                        'avvText' => 'Aufsaug- und Filtermaterialien (einschließlich Ölfilter a. n. g.), Wischtücher und Schutzkleidung, die durch gefährliche Stoffe verunreinigt sind',
                        'wasteMaterialId' => '701718',
                        'wasteMaterialText' => 'Aufs u Filtermat d gef St-150202*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '1.17',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '450.88',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055636921',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41587654',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384714\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384714\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '160508',
                        'avvText' => 'gebrauchte organische Chemikalien, die aus gefährlichen Stoffen bestehen oder solche enthalten',
                        'wasteMaterialId' => '701721',
                        'wasteMaterialText' => 'gebr org Chem a gef Stoff-160508*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '0.11',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '323.64',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657909',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41384714',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41408378\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41408378\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '160601',
                        'avvText' => 'Bleibatterien',
                        'wasteMaterialId' => '701722',
                        'wasteMaterialText' => 'Bleibatterien-160601*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '1.013',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '-202.6',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657957',
                        'subsequentContractPosId' => '000060',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41408378',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41383876\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41383876\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '160604',
                        'avvText' => 'Alkalibatterien (außer 16 06 03)',
                        'wasteMaterialId' => '702089',
                        'wasteMaterialText' => 'Trockenbatterien-160604-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '0.071',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '7.1',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657809',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41383876',
                    ],
                    [
                        '__metadata' => [
                            'id' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384094\')',
                            'uri' => 'https => //A0Y.SYS.SCHWARZ => 443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId=\''.$businessPartnerId.'\',dateFrom=\'20250101\',dateTo=\'20251231\',serviceLocationId=\'526953\',contractId=\'41384094\')',
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => $businessPartnerId,
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '526953',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Heinrich-Follmann-Str. 1 , 32423 Minden',
                        'serviceLocationInfo' => 'SOABF',
                        'avvId' => '200121',
                        'avvText' => 'Leuchtstoffröhren und andere quecksilberhaltige Abfälle',
                        'wasteMaterialId' => '701784',
                        'wasteMaterialText' => 'Leuchtstoffröhren-200121*-ABF',
                        'orderDate' => '2025-01-28',
                        'amount' => '0.132',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '0.0',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055657802',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41384094',
                    ],
                ],
            ],
        ];

        $jsonResponseBody = json_encode(value: $responseBody);
        assert(assertion: $jsonResponseBody, description: 'Unable to encode response body');

        $this->callMockServerSetEmptyRequestBodyExpectation(
            method: 'GET',
            path: $url,
            responseBody: $jsonResponseBody,
        );

        return $this->grabDataFromResponseByJsonPath('$.*.id')[0];
    }
}
