<?php

declare(strict_types=1);

namespace App\Tests\Support;

class InMemoryCache
{
    /** @var array<mixed> */
    private static array $cache = [];

    public static function get(string $key): mixed
    {
        return self::$cache[$key] ?? null;
    }

    public static function set(string $key, mixed $value): void
    {
        self::$cache[$key] = $value;
    }

    public static function clear(): void
    {
        self::$cache = [];
    }
}
