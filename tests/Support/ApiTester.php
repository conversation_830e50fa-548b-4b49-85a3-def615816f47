<?php

declare(strict_types=1);

namespace App\Tests\Support;

use App\Tests\Support\_generated\ApiTesterActions;
use App\Tests\Support\Api\MockServer\MockServerExpectationsTrait;
use App\Tests\Support\Helper\ApiEndpoints\FileEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\PortalCustomerEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\PortalManagementEndpointsTrait;
use App\Tests\Support\Helper\ApiEndpoints\SapEuropeEndpointsTrait;
use App\Tests\Support\Helper\Keycloak\KeycloakTestUserTrait;
use App\Tests\Support\Helper\Mockserver\MockServerMethodsTrait;
use Codeception\Actor;

/**
 * Inherited Methods.
 *
 * @method void wantTo($text)
 * @method void wantToTest($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method void pause($vars = [])
 *
 * @SuppressWarnings(PHPMD)
 */
class ApiTester extends Actor
{
    use ApiTesterActions;

    use MockServerMethodsTrait;
    use MockServerExpectationsTrait;
    use SapEuropeEndpointsTrait;
    use PortalCustomerEndpointsTrait;
    use PortalManagementEndpointsTrait;
    use FileEndpointsTrait;
    use KeycloakTestUserTrait;

    protected function getMockServerUrl(): string
    {
        $mockserverEndpoint = is_string(value: $_ENV['MOCK_SERVER_ENDPOINT']) ? $_ENV['MOCK_SERVER_ENDPOINT'] : '';
        $parts = parse_url(url: $mockserverEndpoint);

        $url = sprintf('%s://%s', $parts['scheme'] ?? '', $parts['host'] ?? '');

        if (isset($parts['port']) && 0 !== $parts['port']) {
            $url .= ':'.$parts['port'];
        }

        return $url;
    }

    public function waitUntil(
        callable $callable,
    ): void {
        $attempts = 30;
        $delayBetweenAttempts = 200_000; // microseconds = 200ms

        while (--$attempts > 0) {
            $isLastAttempt = 1 === $attempts;

            try {
                $callable($this);
            } catch (\Throwable $e) {
                if ($isLastAttempt) {
                    /** @noinspection PhpUnhandledExceptionInspection */
                    throw $e;
                }

                usleep(microseconds: $delayBetweenAttempts);

                continue;
            }

            break;
        }
    }
}
