<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;
use Symfony\Component\HttpFoundation\Response;

/**
 * @group Portal
 */
class AuthenticationCest
{
    /**
     * @throws \Exception
     */
    public function canAuthenticateUser(ApiTester $I): void
    {
        $keycloakUrl = is_string(value: $_ENV['KEYCLOAK_URL']) ? $_ENV['KEYCLOAK_URL'] : '';
        $kycloakRealm = is_string(value: $_ENV['KEYCLOAK_REALM']) ? $_ENV['KEYCLOAK_REALM'] : '';

        $data = [
            'grant_type' => 'password',
            'username' => $I->getKeycloakTestUser(),
            'password' => $I->getKeycloakTestUserPassword(),
            'audience' => 'myprezero-backend',
            'scope' => 'myprezero-portal',
            'client_id' => $_ENV['KEYCLOAK_PORTAL_CLIENT_ID'],
        ];

        $I->sendPost(url: $keycloakUrl.'/realms/'.$kycloakRealm.'/protocol/openid-connect/token', params: $data);

        $I->seeResponseCodeIsSuccessful();
    }

    public function cantUsePortalApiWithoutClientId(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $I->unsetHttpHeader(name: 'x-current-user-context-id');

        $I->sendGet(url: '/api/portal/v1/user/me');

        $I->seeResponseCodeIs(code: Response::HTTP_BAD_REQUEST);
    }

    public function cantUsePortalApiInvalidClientId(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $I->haveHttpHeader(name: 'x-current-user-context-id', value: 'invalid');

        $I->sendGet(url: '/api/portal/v1/user/me');

        $I->seeResponseCodeIs(code: Response::HTTP_BAD_REQUEST);
    }

    public function canUsePortalApiWithClientId(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $I->sendGet(url: '/api/portal/v1/user/me');

        $I->seeResponseCodeIsSuccessful();
    }
}
