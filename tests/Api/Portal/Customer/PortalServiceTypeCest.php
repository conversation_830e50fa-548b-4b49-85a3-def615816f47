<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 */
class PortalServiceTypeCest
{
    /**
     * @throws \Exception
     */
    public function canGetServiceTypeOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $I->callGetPortalServiceTypeOptions(contractId: $contractId);
    }

    /**
     * @throws \Exception
     */
    public function canGetServiceTypeOptionsFailsWithoutFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalServiceTypeOptionsWithoutFilter();
    }
}
