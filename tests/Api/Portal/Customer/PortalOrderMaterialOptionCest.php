<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

class PortalOrderMaterialOptionCest
{
    /**
     * @throws \Exception
     */
    public function canGetMaterialOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $I->callGetPortalOrderMaterialOptions(contractId: $contractId);
    }

    /**
     * @throws \Exception
     */
    public function canGetMaterialOptionsFailsWithoutFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalOrderMaterialOptionsWithoutFilter();
    }
}
