<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

/**
 * @group Portal
 * @group BusinessPartner
 */
#[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
class PortalBusinessPartnerCest
{
    /**
     * @throws \Exception
     */
    public function canGetBusinessPartnerOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalBusinessPartnerOptions();
    }
}
