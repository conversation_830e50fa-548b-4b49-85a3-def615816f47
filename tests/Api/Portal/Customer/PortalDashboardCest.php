<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;

/**
 * @group Portal
 */
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceCest:canCreateService')]
class PortalDashboardCest
{
    /**
     * @throws \Exception
     */
    public function canGetWasteStatistic(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalDashboardWasteStatistic();
    }

    /**
     * @throws \Exception
     */
    public function canGetCo2Report(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalDashboardCo2Report();
    }

    /**
     * @throws \Exception
     */
    public function canGetNextOrderDates(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalDashboardNextOrderDates();
    }

    /**
     * @throws \Exception
     */
    public function canGetLastOrders(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalDashboardLastOrders();
    }

    /**
     * @throws \Exception
     */
    public function canGetInvoices(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalDashboardInvoices();
    }
}
