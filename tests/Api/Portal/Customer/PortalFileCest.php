<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Symfony\Component\Uid\Uuid;

/**
 * @group Portal
 */
class PortalFileCest
{
    private string $filepath = 'document';

    public function canUploadFile(ApiTester $I): void
    {
        $this->filepath = Uuid::v4()->toString();

        $dataDir = is_string(value: codecept_data_dir(appendPath: 'document-1.pdf')) ? codecept_data_dir(appendPath: 'document-1.pdf') : '';
        $fileContents = file_get_contents(filename: $dataDir) ?: '';

        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callApiFileUpload(filePath: $this->filepath, fileContent: $fileContents);
    }

    #[Depends('canUploadFile')]
    public function canRetrieveFile(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callApiFileRetrieve(filePath: $this->filepath);
    }
}
