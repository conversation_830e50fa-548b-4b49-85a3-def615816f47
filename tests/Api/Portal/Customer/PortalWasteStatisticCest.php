<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group Portal
 */
class PortalWasteStatisticCest extends SapEuropeDataProvider
{
    /**
     * @throws \Exception
     */
    public function canGetPortalWasteStatistics(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalWasteStatistics();
    }

    /**
     * @throws \Exception
     */
    public function canGetPortalWasteStatisticById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalWasteStatisticById(wasteStatisticId: '0bf25422-3e7f-488e-b950-f07b361505fd');
    }

    /**
     * @throws \Exception
     */
    public function canExportPortalWasteStatistic(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callExportPortalWasteStatistics();
    }

    /**
     * @param Example<array<string, array<string, mixed>>> $example
     *
     * @throws \Exception
     *
     * @dataProvider sapWasteStatisticDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function canFetchPortalWasteStatisticFromSap(ApiTester $I, Example $example): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $extBusinessPartnerId = '2144108';
        $dateFrom = '20250101';
        $dateTo = '20251231';

        $response = $example['results'];

        $this->setMockServerExpectations(I: $I, response: $response, businessPartnerId: $extBusinessPartnerId, dateFrom: $dateFrom, dateTo: $dateTo);
        $I->callFetchPortalWasteStatisticFromSap(businessPartnerId: $extBusinessPartnerId, dateFrom: $dateFrom, dateTo: $dateTo);
    }

    /**
     * @throws \JsonException
     */
    private function setMockServerExpectations(ApiTester $I, mixed $response, string $businessPartnerId, string $dateFrom, string $dateTo): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        /** @var array<string, array<string>> $query */
        $query = [
            'businessPartnerId' => [$businessPartnerId],
            'dateFrom' => [$dateFrom],
            'dateTo' => [$dateTo],
        ];

        $I->callMockServerSetEmptyRequestBodyExpectationWithFilter(
            method: 'GET',
            path: '/sap-europe/wasteStatistics',
            query: $query,
            responseBody: (string) json_encode(value: $response)
        );
    }
}
