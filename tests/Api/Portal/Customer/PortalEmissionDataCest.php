<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;

/**
 * @group Portal
 */
class PortalEmissionDataCest
{
    /**
     * @throws \Exception
     */
    public function canGetEmissionDataOverview(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetEmissionDataOverview();
    }

    /**
     * @throws \Exception
     */
    public function canGetEmissionDataMaterial(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetEmissionDataMaterial();
    }
}
