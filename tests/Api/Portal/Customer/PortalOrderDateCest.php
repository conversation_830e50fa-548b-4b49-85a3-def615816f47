<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 */
class PortalOrderDateCest
{
    /**
     * @throws \Exception
     */
    public function canGetOrderDates(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalOrderDates();
    }

    /**
     * @throws \Exception
     */
    public function canGetOrderDatesWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $serviceLocationId = $serviceLocation['id'] ?? '';

        $orderDate = PortalEndpointsService::getOrderDate(I: $I);
        if (!isset($orderDate['date'])) {
            $dateFrom = '';
            $dateTo = '';
        } else {
            $dateFrom = new \DateTimeImmutable(datetime: $orderDate['date'])->modify(modifier: '-1 day')->format(format: 'Y-m-d');
            $dateTo = new \DateTimeImmutable(datetime: $orderDate['date'])->modify(modifier: '+1 day')->format(format: 'Y-m-d');
        }

        $I->callGetPortalOrderDatesWithFilter(contractId: $contractId, serviceLocationId: $serviceLocationId, dateFrom: $dateFrom, dateTo: $dateTo);
    }
}
