<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 */
class PortalContractPositionCest
{
    /**
     * @throws \Exception
     */
    public function canGetContractPositions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $I->callGetPortalContractPositions(contractId: $contractId);
    }

    /**
     * @throws \Exception
     */
    public function canGetContractPositionsWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $contractPosition = PortalEndpointsService::getContractPosition(I: $I);
        $search = $contractPosition['description'] ?? '';

        $I->callGetPortalContractPositionsWithFilter(contractId: $contractId, search: $search);
    }
}
