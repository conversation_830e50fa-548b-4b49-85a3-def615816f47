<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;

/**
 * @group Portal
 */
class PortalEmissionDataTotalCest
{
    /**
     * @throws \Exception
     */
    public function canGetEmissionDataTotal(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetEmissionDataTotal();
    }
}
