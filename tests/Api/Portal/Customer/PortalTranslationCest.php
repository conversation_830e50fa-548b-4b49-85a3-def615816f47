<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;

/**
 * @group Portal
 */
class PortalTranslationCest
{
    /**
     * @throws \Exception
     */
    public function canGetLocales(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalTranslationLocales();
    }

    /**
     * @throws \Exception
     */
    public function canGetTranslationsByIso(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalTranslationsByIso(iso: 'de_DE');
    }

    /**
     * @throws \Exception
     */
    public function canGetTranslationsByIsoNotFound(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalTranslationsByIso(iso: 'de', expectedStatusCode: 404);
    }
}
