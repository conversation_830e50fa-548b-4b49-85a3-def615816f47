<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;
use Codeception\Attribute\Depends;

/**
 * @group Portal
 */
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
class PortalGroupCest
{
    /**
     * @throws \Exception
     */
    public function canGetGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalGroups();
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupsWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $search = $group['group_name'] ?? '';

        $I->callGetPortalGroupsWithFilter(search: $search);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $I->callGetPortalGroupById(groupId: $groupId);
    }

    /**
     * @throws \Exception
     */
    public function canCreateGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callPostPortalGroup(data: '{"group_name": "Empty Group Create", "selectedUsers": [], "selectedServiceLocations": []}');
    }

    /**
     * @throws \Exception
     */
    public function canCreateGroupWithServiceLocation(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $serviceLocationId = $serviceLocation['id'] ?? '';

        $data = [
            'group_name' => 'Group Create',
            'selectedServiceLocations' => [
                ['id' => $serviceLocationId],
            ],
        ];

        $I->callPostPortalGroup(data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canUpdateGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $serviceLocationId = $serviceLocation['id'] ?? '';

        $data = [
            'group_name' => 'Group Patch',
            'selectedServiceLocations' => [
                ['id' => $serviceLocationId],
            ],
        ];

        $I->callPatchPortalGroup(groupId: $groupId, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canUpdateUsersInGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $data = [
            'group_name' => 'Group Patch',
            'selectedUsers' => [
                ['id' => '92d5dd94-418b-4d07-b0ce-a81c5e3262cc'],
            ],
        ];

        $I->callPatchPortalUsersInGroup(groupId: $groupId, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canDeleteGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $I->callDeletePortalGroup(groupId: $groupId);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupServiceLocationOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $I->callGetPortalGroupServiceLocationOptions(groupId: $groupId);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupServiceLocationOptionWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $display = $serviceLocation['display'] ?? '';
        $search = explode(separator: ' - ', string: $display)[0];

        $I->callGetPortalGroupServiceLocationOptionsWithFilter(groupId: $groupId, search: $search);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupUserOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $I->callGetPortalGroupUserOptions(groupId: $groupId);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupUserOptionWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $I->callGetPortalGroupUserOptionsWithFilter(groupId: $groupId, search: 'Portal2');
    }
}
