<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 * @group ContractServiceLocation
 */
class PortalContractServiceLocationCest
{
    /**
     * @throws \Exception
     */
    public function canGetContractServiceLocations(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $I->callGetPortalContractServiceLocations(contractId: $contractId);
    }

    /**
     * @throws \Exception
     */
    public function canGetContractServiceLocationsWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $contractServiceLocation = PortalEndpointsService::getContractServiceLocation(I: $I);
        $search = $contractServiceLocation['service_location'] ?? '';

        $I->callGetPortalContractServiceLocationsWithFilter(contractId: $contractId, search: $search);
    }
}
