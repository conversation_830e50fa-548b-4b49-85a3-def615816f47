<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;
use Codeception\Attribute\Depends;

#[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
class PortalOrderDatePickerCest
{
    /**
     * @throws \Exception
     */
    public function canGetPortalOrderDatePickerOption(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $serviceLocationId = $serviceLocation['id'] ?? '';

        $I->callGetPortalOrderDatePickerOptions(serviceLocationId: $serviceLocationId, contractId: $contractId);
    }

    /**
     * @throws \Exception
     */
    public function canGetPortalOrderDatePickerOptionWithoutFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalOrderDatePickerOptionsWithoutFilter();
    }
}
