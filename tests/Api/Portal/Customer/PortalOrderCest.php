<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Domain\Entity\Enum\ServiceType;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;
use Codeception\Attribute\Depends;

/**
 * @group Portal
 * @group Order
 */
#[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
class PortalOrderCest
{
    /**
     * @throws \Exception
     */
    public function canGetOrders(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalOrders();
    }

    /**
     * @throws \Exception
     * @throws \Throwable
     */
    public function canCreateOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $serviceLocationId = 'dafb6fec-035d-389a-be7d-c722c9df2512';
        $contractId = 'b93312cf-8fce-33ad-bb40-23e10dc245c3';

        $data = [
            'date' => '2025-12-12',
            'material_id' => '4536f839-719e-4e7d-be73-e5312ea5fa18',
            'service_location_id' => $serviceLocationId,
            'service_type' => '10',
            'contract_id' => $contractId,
            'amount' => 2,
            'adr_text' => '',
        ];

        $I->setSapServiceCallExpectation();
        $I->waitUntil(callable: function (ApiTester $I) use ($data): void {
            $I->callPostPortalOrder(data: (string) json_encode(value: $data));
        });
    }

    /**
     * @throws \Exception
     */
    public function canCancelOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $order = PortalEndpointsService::getOrder(I: $I);
        $orderId = $order['id'] ?? '';

        $I->callPatchPortalOrderCancel(orderId: $orderId);
    }

    /**
     * @throws \Exception
     */
    public function canGetAgreementOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $serviceType = ServiceType::SERVICE_TYPE_10->value;

        $I->callGetPortalOrderAgreementOptions(serviceType: $serviceType);
    }

    /**
     * @throws \Exception
     */
    public function canGetAgreementOptionsFailsWithoutFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalOrderAgreementOptionsFailsWithoutFilter();
    }

    /**
     * @throws \Exception
     */
    public function canGetOrderContainerOption(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $serviceLocationId = $serviceLocation['id'] ?? '';

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $I->callGetPortalOrderContainerOptions(
            serviceLocationId: $serviceLocationId,
            contractId: $contractId,
            serviceType: ServiceType::SERVICE_TYPE_10->value,
        );
    }

    /**
     * @throws \Exception
     */
    public function canGetOrderContainerOptionWithoutFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $I->callGetPortalOrderContainerOptionsWithoutFilter();
    }
}
