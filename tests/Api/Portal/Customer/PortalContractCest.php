<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;
use Codeception\Attribute\Depends;

/**
 * @group Portal
 * @group Contract
 */
#[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
#[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
class PortalContractCest
{
    /**
     * @throws \Exception
     */
    public function canGetContracts(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalContracts();
    }

    /**
     * @throws \Exception
     */
    public function canGetContractsWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $search = $contract['contract_number'] ?? '';

        $serviceLocation = PortalEndpointsService::getContractServiceLocation(I: $I);
        $extServiceLocationId = $serviceLocation['service_location'] ?? '';

        $I->callGetPortalContractsWithFilter(search: $search, extServiceLocationId: $extServiceLocationId);
    }

    /**
     * @throws \Exception
     */
    public function canGetContractById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $contract = PortalEndpointsService::getContract(I: $I);
        $contractId = $contract['id'] ?? '';

        $I->callGetPortalContractById(contractId: $contractId);
    }

    /**
     * @throws \Exception
     */
    public function canGetContractOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $serviceLocationId = $serviceLocation['id'] ?? '';

        $I->callGetPortalContractOptions(serviceLocationId: $serviceLocationId);
    }
}
