<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 */
class PortalEmissionDataMonthlyCest
{
    /**
     * @throws \Exception
     */
    public function canGetEmissionDataMonthlyEmissions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetEmissionDataMonthlyEmissions();
    }

    /**
     * @throws \Exception
     */
    public function canGetEmissionDataMonthlyEmissionsWithFilters(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $monthlyEmissions = PortalEndpointsService::getMonthlyEmissions(I: $I);

        if ([] === $monthlyEmissions) {
            return;
        }

        /** @var array<int, array<string, string>> $monthlyEmissions */
        $monthlyEmissions = $monthlyEmissions['data'];

        $dateFrom = $monthlyEmissions[0]['date'];
        $dateTo = $monthlyEmissions[0]['date'];

        foreach ($monthlyEmissions as $data) {
            if ($dateFrom > $data['date']) {
                $dateFrom = $data['date'];
            }
            if ($dateTo < $data['date']) {
                $dateTo = $data['date'];
            }
        }

        $I->callGetEmissionDataMonthlyEmissionsWithFilter(dateFrom: $dateFrom, dateTo: $dateTo);
    }

    /**
     * @throws \Exception
     */
    public function canGetEmissionDataMonthlySavings(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetEmissionDataMonthlySavings();
    }

    /**
     * @throws \Exception
     */
    public function canGetEmissionDataMonthlySavingsWithFilters(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $monthlySavings = PortalEndpointsService::getMonthlySavings(I: $I);

        if ([] === $monthlySavings) {
            return;
        }

        /** @var array<int, array<string, string>> $monthlySavings */
        $monthlySavings = $monthlySavings['data'];

        $dateFrom = $monthlySavings[0]['date'];
        $dateTo = $monthlySavings[0]['date'];

        foreach ($monthlySavings as $data) {
            if ($dateFrom > $data['date']) {
                $dateFrom = $data['date'];
            }
            if ($dateTo < $data['date']) {
                $dateTo = $data['date'];
            }

            $I->callGetEmissionDataMonthlySavingsWithFilter(dateFrom: $dateFrom, dateTo: $dateTo);
        }
    }
}
