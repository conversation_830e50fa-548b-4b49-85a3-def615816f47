<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 */
class PortalVerificationDocumentSupervisionDocumentCest
{
    /**
     * @throws \Exception
     */
    public function canGetVerificationDocumentSupervisionDocuments(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $verificationDocument = PortalEndpointsService::getVerificationDocument(I: $I);
        $verificationDocumentId = $verificationDocument['id'] ?? '';

        $I->callGetPortalVerificationDocumentSupervisionDocuments(verificationDocumentId: $verificationDocumentId);
    }
}
