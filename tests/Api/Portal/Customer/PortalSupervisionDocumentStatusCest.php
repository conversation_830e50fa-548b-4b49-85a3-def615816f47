<?php

declare(strict_types=1);

namespace Api\Portal\Customer;

use App\Tests\Support\ApiTester;

/**
 * @group Portal
 */
class PortalSupervisionDocumentStatusCest
{
    /**
     * @throws \Exception
     */
    public function canGetSupervisionDocumentStatusOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalSupervisionDocumentStatusOptions();
    }
}
