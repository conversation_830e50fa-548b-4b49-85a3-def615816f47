<?php

declare(strict_types=1);

namespace Api\Portal\Management;

use App\Domain\Entity\Enum\Permission;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;
use Symfony\Component\Uid\Uuid;

/**
 * @group Portal
 */
class PortalManagementUserCest
{
    /**
     * @throws \Exception
     */
    public function canGetManagementUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalManagementUsers();
    }

    public function canGetManagementUserWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalManagementUsers(search: 'user');
    }

    public function canCreateManagementUser(ApiTester $I): void
    {
        $data = [
            'email' => '<EMAIL>',
            'firstname' => 'User',
            'lastname' => 'Management',
            'permissions' => [Permission::INVOICE->value, Permission::EANV->value],
        ];

        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callPostPortalManagementUser(data: (string) json_encode(value: $data));
    }

    public function canCreateManagementUserPermissionEmpty(ApiTester $I): void
    {
        $data = [
            'email' => 'user@without_permissions.de',
            'firstname' => 'User',
            'lastname' => 'Management',
            'permissions' => [],
        ];

        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callPostPortalManagementUser(data: (string) json_encode(value: $data));
    }

    public function canCreateManagementUserPermissionNotFound(ApiTester $I): void
    {
        $data = [
            'email' => '<EMAIL>',
            'firstname' => 'User',
            'lastname' => 'Management',
            'permissions' => ['test'],
        ];

        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callPostPortalManagementUser(data: (string) json_encode(value: $data), expectedStatusCode: 404);
    }

    /**
     * @throws \Exception
     */
    public function canUpdateManagementUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $id = PortalEndpointsService::getManagementUser(I: $I)['id'] ?? '';

        $data = [
            'id' => $id,
            'email' => '<EMAIL>',
            'firstname' => 'User',
            'lastname' => 'Lastname',
            'permissions' => [Permission::INVOICE->value, Permission::EANV->value],
        ];

        $I->callPatchPortalManagementUser(userId: $id, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canUpdateManagementUserPermissionEmpty(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $user = PortalEndpointsService::getManagementUser(I: $I);
        $id = $user['id'] ?? '';

        $data = [
            'id' => $id,
            'email' => 'user@update_without_permissions.de',
            'firstname' => 'User',
            'lastname' => 'Management',
            'permissions' => [],
        ];

        $I->callPatchPortalManagementUser(userId: $id, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canUpdateManagementUserPermissionNotFound(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $id = PortalEndpointsService::getManagementUser(I: $I)['id'] ?? '';

        $data = [
            'id' => $id,
            'email' => '<EMAIL>',
            'firstname' => 'User',
            'lastname' => 'Management',
            'permissions' => ['test'],
        ];

        $I->callPatchPortalManagementUser(userId: $id, data: (string) json_encode(value: $data), expectedStatusCode: 404);
    }

    /**
     * @throws \Exception
     */
    public function canGetManagementUserById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $id = PortalEndpointsService::getManagementUser(I: $I)['id'] ?? '';

        $I->callGetPortalManagementUser(userId: $id);
    }

    /**
     * @throws \Exception
     */
    public function canGetManagementUserByIdNotFound(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $I->callGetPortalManagementUser(userId: Uuid::v4()->toRfc4122(), expectedStatusCode: 404);
    }

    /**
     * @throws \Exception
     */
    public function canDeleteManagementUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $id = PortalEndpointsService::getManagementUser(I: $I)['id'] ?? '';

        $I->callDeletePortalManagementUser(userId: $id);
    }

    /**
     * @throws \Exception
     */
    public function canDeleteManagementUserNotFound(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $I->callDeletePortalManagementUser(userId: Uuid::v4()->toRfc4122(), expectedStatusCode: 404);
    }

    public function canGetManagementUserPermissionOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $I->callGetPortalManagementUserPermissionOptions();
    }
}
