<?php

declare(strict_types=1);

namespace Api\Portal\Management;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 */
class PortalManagementServiceProductCest
{
    /**
     * @throws \Exception
     */
    public function canGetManagementServiceProducts(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalManagementServiceProducts();
    }

    /**
     * @throws \Exception
     */
    public function canGetManagementServiceProductsWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $serviceProduct = PortalEndpointsService::getServiceProduct(I: $I);
        $serviceProductFilter = $serviceProduct['service_product'] ?? '';
        $serviceType = $serviceProduct['service_type'] ?? '';
        $search = $serviceProduct['container'] ?? '';

        $I->callGetPortalManagementServiceProductsWithFilter(serviceProduct: $serviceProductFilter, serviceType: $serviceType, search: $search);
    }

    /**
     * @throws \Exception
     */
    public function canGetManagementServiceProductServiceTypeOption(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetManagementServiceProductServiceTypeOption();
    }

    /**
     * @throws \Exception
     */
    public function canCreateManagementServiceProduct(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callPostPortalManagementServiceProduct(
            data: '{"service_product": "200001", "service_type": "32", "container": "751120"}',
        );
    }

    /**
     * @throws \Exception
     */
    public function canUpdateManagementServiceProduct(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $serviceProduct = PortalEndpointsService::getServiceProduct(I: $I);
        $serviceProductId = $serviceProduct['id'] ?? '';

        $I->callPatchPortalManagementServiceProduct(
            serviceProductId: $serviceProductId,
            data: '{"service_product": "123456", "service_type": "32", "container": "751120"}',
        );
    }

    /**
     * @throws \Exception
     */
    public function canDeleteManagementServiceProduct(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $serviceProduct = PortalEndpointsService::getServiceProduct(I: $I);
        $serviceProductId = $serviceProduct['id'] ?? '';

        $I->callDeletePortalManagementServiceProduct(serviceProductId: $serviceProductId);
    }
}
