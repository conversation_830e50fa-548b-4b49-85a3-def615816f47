<?php

declare(strict_types=1);

namespace Api\Portal\Management;

use App\Exception\BadRequestException;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 */
class PortalManagementSupplierCest
{
    /**
     * @throws \Exception
     */
    public function canGetManagementSuppliers(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalManagementSuppliers();
    }

    /**
     * @throws \Exception
     */
    public function canGetManagementSupplierById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $supplier = PortalEndpointsService::getSupplier(I: $I);
        $supplierId = $supplier['id'] ?? '';

        $I->callGetPortalManagementSupplierById(supplierId: $supplierId);
    }

    /**
     * @throws \Exception
     */
    public function canGetManagementSupplierDelayOption(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalManagementSuppliersDelayOption();
    }

    /**
     * @throws \Exception
     */
    public function canGetManagementSupplierDailyDelayOption(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalManagementSuppliersDailyDelayOption();
    }

    public function canCreateManagementSupplier(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $vkorg = uniqid();

        $data = [
            'region' => 'Region Mitte BK 1715',
            'location' => 'NL Fulda',
            'description' => 'PreZero Portal Region Mitte BK 1715NL Fulda',
            'number' => '9223',
            'name' => 'test',
            'vkorg' => $vkorg,
            'order_delay' => '12',
            'street' => 'Musterstraße',
            'house_number' => '1',
            'city' => 'Musterstadt',
            'postal_code' => '12345',
            'country' => 'NL',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'opening_hours' => '8 bis 17 Uhr',
            'order_delay_monday' => '13:30',
            'order_delay_tuesday' => '23:30',
            'order_confirmation' => [
                [
                    'name' => 'Mustername',
                    'email' => '<EMAIL>',
                ],
            ],
        ];

        $data = json_encode(value: $data);
        if (!$data) {
            throw new BadRequestException(message: 'Data for creating a Supplier could not be encoded to JSON');
        }

        $I->callPostPortalManagementSupplier(data: $data);
    }

    /**
     * @throws \Exception
     */
    public function canUpdateManagementSupplier(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $supplier = PortalEndpointsService::getSupplier(I: $I);
        $supplierId = $supplier['id'] ?? '';

        $I->callPatchPortalManagementSupplier(supplierId: $supplierId, data: '{
            "name": "Mustername",
            "email": "<EMAIL>",
            "order_confirmation": [
                {
                    "name": "Mustername2",
                    "email": "<EMAIL>"
                }
            ]
        }');
    }
}
