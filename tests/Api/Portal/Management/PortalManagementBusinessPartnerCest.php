<?php

declare(strict_types=1);

namespace Api\Portal\Management;

use App\Domain\Entity\Enum\Permission;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;

/**
 * @group Portal
 */
class PortalManagementBusinessPartnerCest
{
    /**
     * @throws \Exception
     */
    public function canCreateUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';
        $groupId = '54d69430-56db-475b-a695-9311ffccf820';

        $data = [
            'email' => '<EMAIL>',
            'firstname' => 'User',
            'lastname' => 'BusinessPartner Management',
            'permissions' => [Permission::INVOICE->value],
            'groups' => [
                ['id' => $groupId],
            ],
        ];

        $I->callPostPortalBusinessPartnerManagementUser(businessPartnerId: $businessPartnerId, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canGetBusinessPartners(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalManagementBusinessPartners();
    }

    /**
     * @throws \Exception
     */
    public function canGetBusinessPartnerById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $id = PortalEndpointsService::getBusinessPartner(I: $I)['id'] ?? '';

        $I->callGetPortalManagementBusinessPartnerById(businessPartnerId: $id);
    }

    /**
     * @throws \Exception
     */
    public function canGetUsers(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';

        $I->callGetPortalManagementBusinessPartnerUsers(businessPartnerId: $businessPartnerId);
    }

    /**
     * @throws \Exception
     */
    public function canGetUsersWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';
        $search = 'portal-user';

        $I->callGetPortalManagementBusinessPartnerUsersWithFilter(businessPartnerId: $businessPartnerId, search: $search);
    }

    /**
     * @throws \Exception
     */
    public function canGetUserById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';
        $userId = '92d5dd94-418b-4d07-b0ce-a81c5e3262cc';

        $I->callGetPortalManagementBusinessPartnerUserById(businessPartnerId: $businessPartnerId, userId: $userId);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroups(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $I->callGetPortalManagementGroups(businessPartnerId: $businessPartnerId);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $I->callGetPortalManagementGroupById(businessPartnerId: $businessPartnerId, groupId: $groupId);
    }

    /**
     * @throws \Exception
     */
    public function canCreateGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $data = '{"name": "Empty MBP Group Create", "selectedUsers": [], "selectedServiceLocations": []}';

        $I->callPostPortalManagementGroup(businessPartnerId: $businessPartnerId, data: $data);
    }

    /**
     * @throws \Exception
     */
    public function canCreateGroupWithServiceLocation(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $serviceLocationId = $serviceLocation['id'] ?? '';

        $data = [
            'name' => 'MBP Group Create',
            'selectedServiceLocations' => [
                ['id' => $serviceLocationId],
            ],
        ];

        $I->callPostPortalManagementGroup(businessPartnerId: $businessPartnerId, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canDeleteGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $I->callDeletePortalManagementGroup(businessPartnerId: $businessPartnerId, groupId: $groupId);
    }

    /**
     * @throws \Exception
     */
    public function canUpdateGroup(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $serviceLocationId = $serviceLocation['id'] ?? '';

        $data = [
            'name' => 'MBP Group Patch',
            'selectedServiceLocations' => [
                ['id' => $serviceLocationId],
            ],
        ];

        $I->callUpdatePortalManagementGroup(businessPartnerId: $businessPartnerId, groupId: $groupId, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canUpdateUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';
        $userId = '92d5dd94-418b-4d07-b0ce-a81c5e3262cc';
        $groupId = '2723d0ab-21d1-4e3f-a583-dd420d9db1aa';

        $data = [
            'email' => '<EMAIL>',
            'firstname' => 'BusinessPartner',
            'lastname' => 'User',
            'permissions' => [Permission::INVOICE->value, Permission::EANV->value],
            'groups' => [
                ['id' => $groupId],
            ],
        ];

        $I->callUpdatePortalManagementUser(businessPartnerId: $businessPartnerId, userId: $userId, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canGetServiceLocations(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $I->callGetPortalManagementServiceLocations(businessPartnerId: $businessPartnerId);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupServiceLocations(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $I->callGetPortalManagementGroupServiceLocations(businessPartnerId: $businessPartnerId, groupId: $groupId);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupServiceLocationsWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $group = PortalEndpointsService::getGroup(I: $I);
        $groupId = $group['id'] ?? '';

        $serviceLocation = PortalEndpointsService::getServiceLocation(I: $I);
        $display = $serviceLocation['display'] ?? '';
        $search = explode(separator: ' - ', string: $display)[0];

        $I->callGetPortalManagementGroupServiceLocationsWithFilter(businessPartnerId: $businessPartnerId, groupId: $groupId, search: $search);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupUsers(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';
        $groupId = '54d69430-56db-475b-a695-9311ffccf820';

        $I->callGetPortalManagementGroupUser(businessPartnerId: $businessPartnerId, groupId: $groupId);
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupUsersWithFilter(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';
        $groupId = '54d69430-56db-475b-a695-9311ffccf820';
        $search = 'portal-user';

        $I->callGetPortalManagementGroupUserWithFilter(businessPartnerId: $businessPartnerId, groupId: $groupId, search: $search);
    }

    /**
     * @throws \Exception
     */
    public function canGetUserPermissionOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $I->callGetPortalManagementUserPermissionsOption(businessPartnerId: $businessPartnerId);
    }

    public function canPatchGroupUsers(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';
        $groupId = '54d69430-56db-475b-a695-9311ffccf820';
        $data = ['selectedUsers' => [
            ['id' => '92d5dd94-418b-4d07-b0ce-a81c5e3262cc'],
            ['id' => '2ca5ca1c-e9ba-4a9a-8ad2-724de4cc46a9'],
        ]];

        $I->callPatchPortalManagementGroupUsers(businessPartnerId: $businessPartnerId, groupId: $groupId, data: (string) json_encode(value: $data));
    }

    /**
     * @throws \Exception
     */
    public function canGetGroupOptions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartner = PortalEndpointsService::getBusinessPartner(I: $I);
        $businessPartnerId = $businessPartner['id'] ?? '';

        $I->callGetPortalManagementGroupsOption(businessPartnerId: $businessPartnerId);
    }

    /**
     * @throws \Exception
     */
    public function canDeleteUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $businessPartnerId = 'e5027939-6448-4328-a792-61bb18139a88';
        $userId = 'c8ffa6b7-a23e-4ff6-9b9b-792e1a4365d8';

        $I->callDeletePortalBusinessPartnerManagementUser(businessPartnerId: $businessPartnerId, userId: $userId);
    }
}
