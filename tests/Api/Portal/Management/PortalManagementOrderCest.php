<?php

declare(strict_types=1);

namespace Api\Portal\Management;

use App\Tests\Support\ApiTester;

/**
 * @group Portal
 */
class PortalManagementOrderCest
{
    /**
     * @throws \Exception
     */
    public function canGetManagementOrder(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalManagementOrder();
    }

    /**
     * @throws \Exception
     */
    public function canGetManagementOrderById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $order = $this->getOrder(I: $I);

        $I->callGetPortalManagementOrderById(orderId: $order['id'] ?? '');
    }

    /**
     * @return array<string, string>
     *
     * @throws \Exception
     */
    private function getOrder(ApiTester $I): array
    {
        $I->callGetPortalManagementOrder();
        /** @var array<string, int|array<int, array<string, string>>> $response */
        $response = json_decode(json: $I->grabResponse(), associative: true);
        $order = $response['items'] ?? [];

        return $order[0] ?? [];
    }
}
