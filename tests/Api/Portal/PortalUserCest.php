<?php

declare(strict_types=1);

namespace App\Tests\Api\Portal;

use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\Portal\UserFixtures;
use App\Tests\Support\Helper\ApiEndpoints\PortalEndpointsService;
use Symfony\Component\Uid\Uuid;

/**
 * @group Portal
 */
class PortalUserCest
{
    /**
     * @throws \Exception
     */
    public function canGetPortalUserMe(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalUserMe();
    }

    /**
     * @throws \Exception
     */
    public function canGetPortalUserById(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $user = PortalEndpointsService::getUser(I: $I);

        $I->callGetPortalUserById(userId: $user['id'] ?? '');
    }

    /**
     * @throws \Exception
     */
    public function canGetPortalUsers(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalUsers();
    }

    /**
     * @throws \Exception
     */
    public function canCreatePortalUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);

        $I->callPostPortalUser(data: UserFixtures::userCreate(groupId: $group['id'] ?? ''), expectedStatusCode: 201);
    }

    /**
     * @throws \Exception
     */
    public function canUpdatePortalUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $group = PortalEndpointsService::getGroup(I: $I);
        $user = PortalEndpointsService::getUser(I: $I);

        $I->callUpdatePortalUser(
            userId: $user['id'] ?? '',
            data: UserFixtures::userUpdate(id: $user['id'] ?? '', groupId: $group['id'] ?? ''),
        );
    }

    /**
     * @throws \Exception
     */
    public function canCheckPortalUsername(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalUserCheckUsername(username: 'username');
    }

    /**
     * @throws \Exception
     */
    public function canCheckPortalEmail(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalUserCheckEmail(email: '<EMAIL>');
    }

    /**
     * @throws \Exception
     */
    public function canNotDeletePortalUserWithDifferentBusinessPartner(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $user = PortalEndpointsService::getUser(I: $I);

        $I->callDeletePortalUser(
            userId: $user['id'] ?? '',
            businessPartnerId: Uuid::v4()->toRfc4122(),
            expectedStatusCode: 404,
        );
    }

    /**
     * @throws \Exception
     */
    public function canDeletePortalUser(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());

        $user = PortalEndpointsService::getUser(I: $I);

        $I->callDeletePortalUser(userId: $user['id'] ?? '');
    }

    /**
     * @throws \Exception
     */
    public function canGetPortalUserPermissions(ApiTester $I): void
    {
        $I->amAuthenticatedAsPortalUser(cacheIdentifier: 'portal-user', username: $I->getKeycloakTestUser(), password: $I->getKeycloakTestUserPassword());
        $I->callGetPortalUserPermission();
    }
}
