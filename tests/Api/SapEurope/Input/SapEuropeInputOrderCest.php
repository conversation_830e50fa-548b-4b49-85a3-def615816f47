<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\OrderFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group Order
 * @group Invoice
 * @group Archive
 */
class SapEuropeInputOrderCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider orderDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceCest:canCreateService')]
    public function canCreateOrder(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeOrderUpsert(
            data: OrderFixtures::orderCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceCest:canCreateService')]
    public function invalidFieldValueOrder(ApiTester $I): void
    {
        $I->callSapEuropeOrderUpsert(
            data: OrderFixtures::orderInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceCest:canCreateService')]
    public function wrongDataTypeOrder(ApiTester $I): void
    {
        $I->callSapEuropeOrderUpsert(
            data: OrderFixtures::orderWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceCest:canCreateService')]
    public function nullValueOrder(ApiTester $I): void
    {
        $I->callSapEuropeOrderUpsert(
            data: OrderFixtures::orderNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceCest:canCreateService')]
    public function missingRequiredFieldOrder(ApiTester $I): void
    {
        $I->callSapEuropeOrderUpsert(
            data: OrderFixtures::orderMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceCest:canCreateService')]
    public function emptyBodyOrder(ApiTester $I): void
    {
        $I->callSapEuropeOrderUpsert(
            data: OrderFixtures::orderEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
