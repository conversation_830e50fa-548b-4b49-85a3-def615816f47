<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\RouteFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group Route
 */
class SapEuropeInputRouteCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider routeDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function canCreateRoute(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeRouteUpsert(
            data: RouteFixtures::routeCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function invalidFieldValueOrder(ApiTester $I): void
    {
        $I->callSapEuropeRouteUpsert(
            data: RouteFixtures::routeInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function wrongDataTypeOrder(ApiTester $I): void
    {
        $I->callSapEuropeRouteUpsert(
            data: RouteFixtures::routeWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function nullValueOrder(ApiTester $I): void
    {
        $I->callSapEuropeRouteUpsert(
            data: RouteFixtures::routeNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function missingRequiredFieldOrder(ApiTester $I): void
    {
        $I->callSapEuropeRouteUpsert(
            data: RouteFixtures::routeMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function emptyBodyOrder(ApiTester $I): void
    {
        $I->callSapEuropeRouteUpsert(
            data: RouteFixtures::routeEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
