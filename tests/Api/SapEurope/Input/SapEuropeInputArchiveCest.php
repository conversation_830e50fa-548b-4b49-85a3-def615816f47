<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group Archive
 */
class SapEuropeInputArchiveCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider archiveDocumentDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputInvoiceCest:canCreateInvoice')]
    public function canGetArchiveDocument(ApiTester $I, Example $example): void
    {
        $archiveId = is_string(value: $example['archive_id']) ? $example['archive_id'] : 'T9';
        $documentId = is_string(value: $example['document_id']) ? $example['document_id'] : '005056B40A031EDFA9CC51692FED1C47';

        $this->setMockServerExpectations(I: $I, archiveId: $archiveId, documentId: $documentId);

        $I->callSapEuropeArchiveDocumentGet(
            archiveId: $archiveId,
            documentId: $documentId
        );
    }

    /**
     * @throws \Exception
     */
    private function setMockServerExpectations(ApiTester $I, string $archiveId, string $documentId): void
    {
        $dataDir = is_string(value: codecept_data_dir(appendPath: 'document-1.pdf')) ? codecept_data_dir(appendPath: 'document-1.pdf') : '';
        $fileContent = file_get_contents(filename: $dataDir) ?: '';

        $I->callMockServerSetBinaryExpectation(
            method: 'GET',
            path: "/sap-europe/archives(id='$archiveId',documentId='$documentId')/\$value",
            responseBodyBase64: base64_encode(string: $fileContent)
        );
    }
}
