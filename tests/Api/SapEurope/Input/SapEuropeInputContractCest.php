<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\ContractFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group Contract
 * @group VerificationDocument
 * @group SupervisionDocument
 * @group ContractServiceLocation
 * @group Invoice
 * @group Service
 * @group Order
 * @group Route
 * @group Archive
 */
class SapEuropeInputContractCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider contractDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
    public function canCreateContract(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeContractUpsert(
            data: ContractFixtures::contractCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
    public function invalidFieldValueContract(ApiTester $I): void
    {
        $I->callSapEuropeContractUpsert(
            data: ContractFixtures::contractInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
    public function wrongDataTypeContract(ApiTester $I): void
    {
        $I->callSapEuropeContractUpsert(
            data: ContractFixtures::contractWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
    public function nullValueContract(ApiTester $I): void
    {
        $I->callSapEuropeContractUpsert(
            data: ContractFixtures::contractNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
    public function missingRequiredFieldContract(ApiTester $I): void
    {
        $I->callSapEuropeContractUpsert(
            data: ContractFixtures::contractMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
    public function emptyBodyContract(ApiTester $I): void
    {
        $I->callSapEuropeContractUpsert(
            data: ContractFixtures::contractEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
