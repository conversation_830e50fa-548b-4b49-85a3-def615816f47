<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\ServiceLocationFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group ServiceLocation
 * @group ContractServiceLocation
 * @group Invoice
 * @group Service
 * @group Order
 * @group Archive
 */
class SapEuropeInputServiceLocationCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider serviceLocationDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function canCreateServiceLocation(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeServiceLocationUpsert(
            data: ServiceLocationFixtures::serviceLocationCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function invalidFieldValueServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeServiceLocationUpsert(
            data: ServiceLocationFixtures::serviceLocationInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function wrongDataTypeServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeServiceLocationUpsert(
            data: ServiceLocationFixtures::serviceLocationWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function nullValueServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeServiceLocationUpsert(
            data: ServiceLocationFixtures::serviceLocationNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function missingRequiredFieldServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeServiceLocationUpsert(
            data: ServiceLocationFixtures::serviceLocationMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function emptyBodyServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeServiceLocationUpsert(
            data: ServiceLocationFixtures::serviceLocationEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
