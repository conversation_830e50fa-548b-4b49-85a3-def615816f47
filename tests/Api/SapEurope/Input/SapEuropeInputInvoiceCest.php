<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\InvoiceFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group Invoice
 * @group Archive
 */
class SapEuropeInputInvoiceCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider invoiceDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputBusinessPartnerCest:canCreateBusinessPartner')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputOrderCest:canCreateOrder')]
    public function canCreateInvoice(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeInvoiceUpsert(
            data: InvoiceFixtures::invoiceCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputOrderCest:canCreateOrder')]
    public function invalidFieldValueInvoice(ApiTester $I): void
    {
        $I->callSapEuropeInvoiceUpsert(
            data: InvoiceFixtures::invoiceInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputOrderCest:canCreateOrder')]
    public function wrongDataTypeInvoice(ApiTester $I): void
    {
        $I->callSapEuropeInvoiceUpsert(
            data: InvoiceFixtures::invoiceWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputOrderCest:canCreateOrder')]
    public function nullValueInvoice(ApiTester $I): void
    {
        $I->callSapEuropeInvoiceUpsert(
            data: InvoiceFixtures::invoiceNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputOrderCest:canCreateOrder')]
    public function missingRequiredFieldInvoice(ApiTester $I): void
    {
        $I->callSapEuropeInvoiceUpsert(
            data: InvoiceFixtures::invoiceMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputOrderCest:canCreateOrder')]
    public function EmptyBodyInvoice(ApiTester $I): void
    {
        $I->callSapEuropeInvoiceUpsert(
            data: InvoiceFixtures::invoiceEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
