<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\BusinessPartnerFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group BusinessPartner
 * @group Contract
 * @group VerificationDocument
 * @group SupervisionDocument
 * @group ContractServiceLocation
 * @group Invoice
 * @group Service
 * @group Order
 * @group Route
 * @group Archive
 */
class SapEuropeInputBusinessPartnerCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider businessPartnerDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function canCreateBusinessPartner(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeBusinessPartnerUpsert(
            data: BusinessPartnerFixtures::businessPartnerCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function invalidFieldValueBusinessPartner(ApiTester $I): void
    {
        $I->callSapEuropeBusinessPartnerUpsert(
            data: BusinessPartnerFixtures::businessPartnerInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function wrongDataTypeBusinessPartner(ApiTester $I): void
    {
        $I->callSapEuropeBusinessPartnerUpsert(
            data: BusinessPartnerFixtures::businessPartnerWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function nullValueBusinessPartner(ApiTester $I): void
    {
        $I->callSapEuropeBusinessPartnerUpsert(
            data: BusinessPartnerFixtures::businessPartnerNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function missingRequiredFieldBusinessPartner(ApiTester $I): void
    {
        $I->callSapEuropeBusinessPartnerUpsert(
            data: BusinessPartnerFixtures::businessPartnerMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    public function emptyBodyBusinessPartner(ApiTester $I): void
    {
        $I->callSapEuropeBusinessPartnerUpsert(
            data: BusinessPartnerFixtures::businessPartnerEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
