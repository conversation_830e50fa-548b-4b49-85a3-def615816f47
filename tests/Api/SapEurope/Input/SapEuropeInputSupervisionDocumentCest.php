<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\SupervisionDocumentFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group SupervisionDocument
 */
class SapEuropeInputSupervisionDocumentCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider supervisionDocumentDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputVerificationDocumentCest:canCreateVerificationDocument')]
    public function canCreateSupervisionDocument(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeSupervisionDocumentUpsert(
            data: SupervisionDocumentFixtures::supervisionDocumentCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputVerificationDocumentCest:canCreateVerificationDocument')]
    public function invalidFieldValueSupervisionDocument(ApiTester $I): void
    {
        $I->callSapEuropeSupervisionDocumentUpsert(
            data: SupervisionDocumentFixtures::supervisionDocumentInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputVerificationDocumentCest:canCreateVerificationDocument')]
    public function wrongDataTypeSupervisionDocument(ApiTester $I): void
    {
        $I->callSapEuropeSupervisionDocumentUpsert(
            data: SupervisionDocumentFixtures::supervisionDocumentWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputVerificationDocumentCest:canCreateVerificationDocument')]
    public function nullValueSupervisionDocument(ApiTester $I): void
    {
        $I->callSapEuropeSupervisionDocumentUpsert(
            data: SupervisionDocumentFixtures::supervisionDocumentNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputVerificationDocumentCest:canCreateVerificationDocument')]
    public function missingRequiredFieldSupervisionDocument(ApiTester $I): void
    {
        $I->callSapEuropeSupervisionDocumentUpsert(
            data: SupervisionDocumentFixtures::supervisionDocumentMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputVerificationDocumentCest:canCreateVerificationDocument')]
    public function emptyBodySupervisionDocument(ApiTester $I): void
    {
        $I->callSapEuropeSupervisionDocumentUpsert(
            data: SupervisionDocumentFixtures::supervisionDocumentEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
