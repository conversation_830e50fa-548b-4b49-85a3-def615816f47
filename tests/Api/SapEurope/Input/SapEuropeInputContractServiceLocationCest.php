<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\ContractServiceLocationFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group ContractServiceLocation
 * @group Invoice
 * @group Service
 * @group Order
 * @group Archive
 */
class SapEuropeInputContractServiceLocationCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider contractServiceLocationDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    public function canCreateContractServiceLocation(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeContractServiceLocationUpsert(
            data: ContractServiceLocationFixtures::contractServiceLocationCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    public function invalidFieldValueContractServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeContractServiceLocationUpsert(
            data: ContractServiceLocationFixtures::contractServiceLocationInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    public function wrongDataTypeContractServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeContractServiceLocationUpsert(
            data: ContractServiceLocationFixtures::contractServiceLocationWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    public function nullValueContractServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeContractServiceLocationUpsert(
            data: ContractServiceLocationFixtures::contractServiceLocationNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    public function missingRequiredFieldContractServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeContractServiceLocationUpsert(
            data: ContractServiceLocationFixtures::contractServiceLocationMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    public function emptyBodyContractServiceLocation(ApiTester $I): void
    {
        $I->callSapEuropeContractServiceLocationUpsert(
            data: ContractServiceLocationFixtures::contractServiceLocationEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
