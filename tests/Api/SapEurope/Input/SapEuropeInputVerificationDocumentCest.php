<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\VerificationDocumentFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group VerificationDocument
 * @group SupervisionDocument
 */
class SapEuropeInputVerificationDocumentCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider verificationDocumentDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function canCreateVerificationDocument(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeVerificationDocumentUpsert(
            data: VerificationDocumentFixtures::verificationDocumentCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function invalidFieldValueVerificationDocument(ApiTester $I): void
    {
        $I->callSapEuropeVerificationDocumentUpsert(
            data: VerificationDocumentFixtures::verificationDocumentInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function wrongDataTypeVerificationDocument(ApiTester $I): void
    {
        $I->callSapEuropeVerificationDocumentUpsert(
            data: VerificationDocumentFixtures::verificationDocumentWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function nullValueVerificationDocument(ApiTester $I): void
    {
        $I->callSapEuropeVerificationDocumentUpsert(
            data: VerificationDocumentFixtures::verificationDocumentNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function missingRequiredFieldVerificationDocument(ApiTester $I): void
    {
        $I->callSapEuropeVerificationDocumentUpsert(
            data: VerificationDocumentFixtures::verificationDocumentMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    public function emptyBodyVerificationDocument(ApiTester $I): void
    {
        $I->callSapEuropeVerificationDocumentUpsert(
            data: VerificationDocumentFixtures::verificationDocumentEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
