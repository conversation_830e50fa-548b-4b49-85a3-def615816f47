<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope\Input;

use App\Tests\Api\SapEurope\SapEuropeDataProvider;
use App\Tests\Support\ApiTester;
use App\Tests\Support\Data\Fixtures\SapEurope\ServiceFixtures;
use Codeception\Attribute\Depends;
use Codeception\Example;

/**
 * @group SapEurope
 * @group Service
 * @group Order
 * @group Invoice
 * @group Archive
 */
class SapEuropeInputServiceCest extends SapEuropeDataProvider
{
    /**
     * @param Example<array<string, mixed>> $example
     *
     * @throws \Exception
     *
     * @dataProvider serviceDataProvider
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
    public function canCreateService(ApiTester $I, Example $example): void
    {
        $I->callSapEuropeServiceUpsert(
            data: ServiceFixtures::serviceCreate(data: $example),
            expectedStatusCode: 201
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
    public function invalidFieldValueService(ApiTester $I): void
    {
        $I->callSapEuropeServiceUpsert(
            data: ServiceFixtures::serviceInvalidFieldValue(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
    public function wrongDataTypeService(ApiTester $I): void
    {
        $I->callSapEuropeServiceUpsert(
            data: ServiceFixtures::serviceWrongDataType(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
    public function nullValueService(ApiTester $I): void
    {
        $I->callSapEuropeServiceUpsert(
            data: ServiceFixtures::serviceNullValue(),
            expectedStatusCode: 400
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
    public function missingRequiredFieldService(ApiTester $I): void
    {
        $I->callSapEuropeServiceUpsert(
            data: ServiceFixtures::serviceMissingRequiredField(),
            expectedStatusCode: 422
        );
    }

    /**
     * @throws \Exception
     */
    #[Depends('App\Tests\Api\MockserverCest:canConnectToMockserver')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractCest:canCreateContract')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputServiceLocationCest:canCreateServiceLocation')]
    #[Depends('App\Tests\Api\SapEurope\Input\SapEuropeInputContractServiceLocationCest:canCreateContractServiceLocation')]
    public function emptyBodyService(ApiTester $I): void
    {
        $I->callSapEuropeServiceUpsert(
            data: ServiceFixtures::serviceEmptyBody(),
            expectedStatusCode: 422
        );
    }
}
