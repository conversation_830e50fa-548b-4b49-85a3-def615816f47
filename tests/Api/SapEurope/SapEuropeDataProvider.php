<?php

declare(strict_types=1);

namespace App\Tests\Api\SapEurope;

use App\Tests\Support\Data\Config\SapEuropeTestDataConfig;
use Faker\Factory;
use Faker\Generator;

class SapEuropeDataProvider
{
    private const array ID_RANGES = [
        'BusinessPartner' => 2000000,
        'Contract' => 40000000,
        'ServiceLocation' => 1200000,
        'ContractServiceLocation' => 1600000,
        'Invoice' => 118000000,
        'Service' => 2600000,
        'Order' => 7500000,
        'Route' => 40000,
        'VerificationDocument' => 8000,
        'SupervisionDocument' => 16926180000000,
    ];

    private const array DEVELOPMENT_MODES = ['development', 'stress', 'custom'];

    private readonly SapEuropeTestDataConfig $config;

    public function __construct()
    {
        $this->config = new SapEuropeTestDataConfig();
    }

    private function createFaker(int $seed): Generator
    {
        $faker = Factory::create();
        $faker->seed(seed: $seed);

        return $faker;
    }

    private function getLoopCount(string $object): int
    {
        return $this->config->getCountForObject(objectType: $object);
    }

    private function getFileId(string $object): ?int
    {
        return $this->config->getFileIdForObject(objectType: $object);
    }

    private function calculateFileId(?int $baseFileId, int $index): int
    {
        if (null === $baseFileId) {
            return 0;
        }

        $mode = $this->config->getCurrentMode();

        return match ($mode) {
            'ci' => 1,
            'full' => $index - 1,
            default => 0,
        };
    }

    /**
     * @param array<int, array<string, mixed>> $parentData
     *
     * @return array<string, mixed>
     */
    private function selectParentData(array $parentData, int $index, string $objectType): array
    {
        if ([] === $parentData) {
            throw new \Exception(message: "Parent data is empty for {$objectType}");
        }

        if (1 === $index) {
            return $parentData[0];
        }

        $mode = $this->config->getCurrentMode();
        $totalCount = $this->getLoopCount(object: $objectType);
        $halfCount = (int) ceil(num: $totalCount / 2);

        if (in_array(needle: $mode, haystack: self::DEVELOPMENT_MODES) && $index <= $halfCount) {
            return $parentData[0];
        }

        return $parentData[array_rand($parentData)];
    }

    /**
     * @param array<int, array<string, mixed>> ...$parentDataArrays
     *
     * @return array<int, array<string, mixed>>
     */
    private function selectMultipleParentData(int $index, string $objectType, array ...$parentDataArrays): array
    {
        $result = [];
        foreach ($parentDataArrays as $parentData) {
            $result[] = $this->selectParentData(parentData: $parentData, index: $index, objectType: $objectType);
        }

        return $result;
    }

    /**
     * @return array<string, mixed>
     */
    private function createAddressData(Generator $faker): array
    {
        return [
            'street' => $faker->streetName(),
            'house_number' => (string) $faker->numberBetween(int1: 1, int2: 999),
            'postal_code' => $faker->postcode(),
            'city' => $faker->city(),
        ];
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function businessPartnerDataProvider(): array
    {
        $data = [];
        $id = self::ID_RANGES['BusinessPartner'];
        $fileId = $this->getFileId(object: 'BusinessPartner');

        for ($i = 1; $i <= $this->getLoopCount(object: 'BusinessPartner'); ++$i) {
            ++$id;
            $faker = $this->createFaker(seed: $id);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);
            $addressData = $this->createAddressData(faker: $faker);

            $data[] = array_merge([
                'index' => $i,
                'file_id' => $currentFileId,
                'uuid' => $faker->uuid(),
                'id' => (string) $id,
                'id_int' => $id,
                'name1' => $faker->company(),
                'email' => $faker->companyEmail(),
                'phone' => $faker->phoneNumber(),
                'tax_number' => 'DE'.$faker->numberBetween(int1: *********, int2: *********),
            ], $addressData);
        }

        return $data;
    }

    /**
     * @return array<string, mixed>
     */
    private function createContractPositions(Generator $faker): array
    {
        $positions = [];
        for ($j = 10; $j <= 130; $j += 10) {
            $positions['quantity_'.$j] = $faker->randomFloat(nbMaxDecimals: 2, min: 0.10, max: 999.99);
            $positions['material_id_'.$j] = (string) $faker->numberBetween(int1: 700000, int2: 799999);
            $positions['net_value_'.$j] = $faker->randomFloat(nbMaxDecimals: 2, min: 1);
        }

        return $positions;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function contractDataProvider(): array
    {
        $businessPartnerData = $this->businessPartnerDataProvider();
        $routeData = $this->routeDataProvider();

        if ([] === $businessPartnerData || [] === $routeData) {
            return [];
        }

        $data = [];
        $id = self::ID_RANGES['Contract'];
        $fileId = $this->getFileId(object: 'Contract');

        for ($i = 1; $i <= $this->getLoopCount(object: 'Contract'); ++$i) {
            ++$id;
            $faker = $this->createFaker(seed: $id);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);

            [$businessPartner, $route] = $this->selectMultipleParentData(
                $i,
                'Contract',
                $businessPartnerData,
                $routeData
            );

            $addressData = $this->createAddressData(faker: $faker);
            $positions = $this->createContractPositions(faker: $faker);

            $contract = array_merge([
                'index' => $i,
                'uuid' => $faker->uuid(),
                'id' => (string) $id,
                'file_id' => $currentFileId,
                'business_partner_id' => $businessPartner['id'],
                'route_id' => $route['id'],
                'description' => $faker->text(40),
                'name1' => $faker->company(),
                'district' => $faker->text(10),
            ], $addressData, $positions);

            $data[] = $contract;
        }

        return $data;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function serviceLocationDataProvider(): array
    {
        $data = [];
        $id = self::ID_RANGES['ServiceLocation'];
        $fileId = $this->getFileId(object: 'ServiceLocation');

        for ($i = 1; $i <= $this->getLoopCount(object: 'ServiceLocation'); ++$i) {
            ++$id;
            $faker = $this->createFaker(seed: $id);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);
            $addressData = $this->createAddressData(faker: $faker);

            $data[] = array_merge([
                'index' => $i,
                'uuid' => $faker->uuid(),
                'file_id' => $currentFileId,
                'id' => (string) $id,
                'id_int' => $id,
                'name1' => $faker->company(),
            ], $addressData);
        }

        return $data;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function contractServiceLocationDataProvider(): array
    {
        $contractData = $this->contractDataProvider();
        $serviceLocationData = $this->serviceLocationDataProvider();

        if ([] === $contractData || [] === $serviceLocationData) {
            return [];
        }

        $data = [];
        $id = self::ID_RANGES['ContractServiceLocation'];
        $posId = 0;
        $fileId = $this->getFileId(object: 'ContractServiceLocation');

        for ($i = 1; $i <= $this->getLoopCount(object: 'ContractServiceLocation'); ++$i) {
            ++$id;
            $faker = $this->createFaker(seed: $id);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);

            [$contract, $serviceLocation] = $this->selectMultipleParentData(
                $i,
                'ContractServiceLocation',
                $contractData,
                $serviceLocationData
            );

            $data[] = [
                'index' => $i,
                'uuid' => $faker->uuid(),
                'id' => $id,
                'file_id' => $currentFileId,
                'pos_id' => ++$posId,
                'contract_id' => $contract['id'],
                'business_partner_id' => $contract['business_partner_id'],
                'container_material_id' => (string) $faker->numberBetween(int1: 700000, int2: 799999),
                'equipment' => '000000000000'.$faker->numberBetween(int1: 400000, int2: 499999),
                'serial_number' => '000000000000'.$faker->numberBetween(int1: 100000, int2: 999999),
                'service_location_id' => $serviceLocation['id'],
            ];
        }

        return $data;
    }

    /**
     * @return array<int, array<string, array<int, array<string, array<string, string>|string>>>>
     */
    protected function sapWasteStatisticDataProvider(): array
    {
        return [
            [
                'results' => [
                    [
                        '__metadata' => [
                            'id' => '123456',
                            'uri' => "https://A0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId='2144108',dateFrom='20250101',dateTo='20251231',serviceLocationId='1002466',contractId='41408604')",
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => '2144108',
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '1002466',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Zum Industriehafen 8 , 32423 Minden',
                        'serviceLocationInfo' => 'Zum Industriehafen 8 Werk II',
                        'avvId' => '170201',
                        'avvText' => 'Holz',
                        'wasteMaterialId' => '701646',
                        'wasteMaterialText' => 'Altholz A2-170201-ABF',
                        'orderDate' => '2025-01-14',
                        'amount' => '0.62',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '39.28',
                        'currency' => 'EUR',
                        'subsequentContractId' => '3055512936',
                        'subsequentContractPosId' => '000050',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '41408604',
                    ],
                    [
                        '__metadata' => [
                            'id' => '234567',
                            'uri' => "https://A0Y.SYS.SCHWARZ:443/sap/opu/odata/sap/ZISU_WEBSHOP_SRV/wasteStatistics(businessPartnerId='2144108',dateFrom='20250101',dateTo='20251231',serviceLocationId='1002466',contractId='')",
                            'type' => 'ZISU_WEBSHOP_SRV.WasteStatisticType',
                        ],
                        'businessPartnerId' => '2144108',
                        'dateFrom' => '20250101',
                        'dateTo' => '20251231',
                        'serviceLocationId' => '1002466',
                        'serviceLocationAddress' => 'Follmann Chemie GmbH  , Zum Industriehafen 8 , 32423 Minden',
                        'serviceLocationInfo' => 'Zum Industriehafen 8 Werk II',
                        'avvId' => '191207',
                        'avvText' => 'Holz mit Ausnahme desjenigen, das unter 19 12 06 fällt',
                        'wasteMaterialId' => '702696',
                        'wasteMaterialText' => 'Altholz bis A3-191207-ABF',
                        'orderDate' => '2025-01-23',
                        'amount' => '0.72',
                        'amountUnitOm' => 'TO',
                        'amountUnitExt' => 't',
                        'netValue' => '31.21',
                        'currency' => 'EUR',
                        'subsequentContractId' => '**********',
                        'subsequentContractPosId' => '000070',
                        'subsequentContractType' => 'ZTA',
                        'contractId' => '',
                    ],
                ],
            ],
        ];
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function archiveDocumentDataProvider(): array
    {
        $data = [];
        $fileId = $this->getFileId(object: 'ArchiveDocument');

        for ($i = 1; $i <= $this->getLoopCount(object: 'ArchiveDocument'); ++$i) {
            $faker = $this->createFaker(seed: $i);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);

            $data[] = [
                'uuid' => $faker->uuid(),
                'index' => $i,
                'file_id' => $currentFileId,
                'archive_id' => 'T'.$faker->numberBetween(int1: 1, int2: 9),
                'document_id' => strtoupper(string: str_replace(search: '-', replace: '', subject: $faker->uuid())),
            ];
        }

        return $data;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function invoiceDataProvider(): array
    {
        $contractData = $this->contractDataProvider();
        $archiveDocumentData = $this->archiveDocumentDataProvider();

        if ([] === $contractData || [] === $archiveDocumentData) {
            return [];
        }

        $data = [];
        $id = self::ID_RANGES['Invoice'];
        $oId = 9000000;
        $fileId = $this->getFileId(object: 'Invoice');

        for ($i = 1; $i <= $this->getLoopCount(object: 'Invoice'); ++$i) {
            ++$id;
            ++$oId;
            $faker = $this->createFaker(seed: $id);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);

            [$contract, $archiveDocument] = $this->selectMultipleParentData(
                $i,
                'Invoice',
                $contractData,
                $archiveDocumentData
            );

            $objectId = sprintf('0%s                   000%s', $id, $oId);
            $billingDate = new \DateTimeImmutable(datetime: 'now + '.$i.' days');

            $data[] = [
                'index' => $i,
                'uuid' => $faker->uuid(),
                'id' => (string) $id,
                'file_id' => $currentFileId,
                'net_value' => $faker->randomFloat(nbMaxDecimals: 2, min: 1),
                'payer_id' => $contract['business_partner_id'],
                'business_partner_id' => $contract['business_partner_id'],
                'contract_id' => $contract['id'],
                'archive_id' => $archiveDocument['archive_id'],
                'document_id' => $archiveDocument['document_id'],
                'object_id' => $objectId,
                'billing_date' => $billingDate->format(format: 'Y-m-d'),
            ];
        }

        return $data;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function serviceDataProvider(): array
    {
        $contractData = $this->contractDataProvider();
        $serviceLocationData = $this->serviceLocationDataProvider();

        if ([] === $contractData || [] === $serviceLocationData) {
            return [];
        }

        $data = [];
        $id = self::ID_RANGES['Service'];
        $fileId = $this->getFileId(object: 'Service');
        $posId = 0;

        for ($i = 1; $i <= $this->getLoopCount(object: 'Service'); ++$i) {
            ++$id;
            $faker = $this->createFaker(seed: (int) ($id.$posId));
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);

            [$contract, $serviceLocation] = $this->selectMultipleParentData(
                $i,
                'Service',
                $contractData,
                $serviceLocationData
            );

            $date = new \DateTimeImmutable(datetime: 'now -5 days');

            $data[] = [
                'index' => $i,
                'uuid' => $faker->uuid(),
                'id' => (string) $id,
                'file_id' => $currentFileId,
                'pos_id' => ++$posId,
                'contract_id' => $contract['id'],
                'start_date' => $date->add(interval: new \DateInterval(duration: 'P'.$i.'D'))->format(format: 'Ymd'),
                'service_type' => (string) $faker->numberBetween(int1: 10, int2: 12),
                'container_material_id' => (string) $faker->numberBetween(int1: 700000, int2: 799999),
                'waste_material_id' => (string) $faker->numberBetween(int1: 700000, int2: 799999),
                'container_count' => $faker->numberBetween(int1: 1, int2: 10),
                'service_location_id' => $serviceLocation['id'],
                'equipment' => (string) $faker->numberBetween(int1: 400000, int2: 499999),
            ];
        }

        return $data;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function orderDataProvider(): array
    {
        $serviceData = $this->serviceDataProvider();
        $invoiceData = $this->invoiceDataProvider();

        if ([] === $serviceData || [] === $invoiceData) {
            return [];
        }

        $data = [];
        $id = self::ID_RANGES['Order'];
        $objectId = 84000000;
        $posId = 0;
        $fileId = $this->getFileId(object: 'Order');

        for ($i = 1; $i <= $this->getLoopCount(object: 'Order'); ++$i) {
            ++$id;
            $faker = $this->createFaker(seed: $id);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);

            [$service, $invoice] = $this->selectMultipleParentData(
                $i,
                'Order',
                $serviceData,
                $invoiceData
            );

            // @todo phpstan ignore
            if (isset($service['start_date']) && is_string(value: $service['start_date'])
            ) {
                $date = new \DateTimeImmutable(datetime: $service['start_date']);
            } else {
                $date = new \DateTimeImmutable(datetime: 'now -5 days');
            }

            $data[] = [
                'index' => $i,
                'uuid' => $faker->uuid(),
                'id' => (string) $id,
                'file_id' => $currentFileId,
                'pos_id' => ++$posId,
                'service_id' => $service['id'],
                'service_pos_id' => $service['pos_id'],
                'order_date' => $date->format(format: 'Y-m-d'),
                'object_id' => 'WE000000000000'.++$objectId,
                'order_status' => (string) $faker->numberBetween(int1: 1, int2: 5),
                'archive_id' => $invoice['archive_id'],
                'archive_document_id' => $invoice['document_id'],
                'archive_object_id' => $invoice['object_id'],
            ];
        }

        return $data;
    }

    /**
     * @return array<string, mixed>
     */
    private function createServiceFrequencyData(Generator $faker): array
    {
        $serviceFrequencyId = $faker->numberBetween(int1: 2700000, int2: 2799999);
        $serviceFrequency = ['sf_id' => (string) $serviceFrequencyId];

        for ($j = 1; $j <= 5; ++$j) {
            $serviceFrequency['sf_pos_id_'.$j] = $j;
            $serviceFrequency['weekday_'.$j] = (string) $faker->numberBetween(int1: 1, int2: 6);
        }

        return $serviceFrequency;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function routeDataProvider(): array
    {
        $data = [];
        $id = self::ID_RANGES['Route'];
        $fileId = $this->getFileId(object: 'Route');

        for ($i = 1; $i <= $this->getLoopCount(object: 'Route'); ++$i) {
            $faker = $this->createFaker(seed: $i);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);
            $serviceFrequency = $this->createServiceFrequencyData(faker: $faker);

            $route = [
                'index' => $i,
                'uuid' => $faker->uuid(),
                'file_id' => $currentFileId,
                'id' => (string) ++$id,
                'text' => 'HN-XY '.$faker->numberBetween(int1: 1000, int2: 9999),
            ];

            $data[] = array_merge($route, $serviceFrequency);
        }

        return $data;
    }

    /**
     * @return array<string, mixed>
     */
    private function createSignData(Generator $faker): array
    {
        $signData = [];
        for ($j = 1; $j <= 3; ++$j) {
            $signData['sign_data_id_'.$j] = $faker->uuid();
            $signData['signer_'.$j] = $faker->lastName().', '.$faker->firstName();
        }

        return $signData;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function verificationDocumentDataProvider(): array
    {
        $contractData = $this->contractDataProvider();
        $serviceLocationData = $this->serviceLocationDataProvider();

        if ([] === $contractData || [] === $serviceLocationData) {
            return [];
        }

        $data = [];
        $id = self::ID_RANGES['VerificationDocument'];
        $objectId = 69700000;
        $fileId = $this->getFileId(object: 'VerificationDocument');

        for ($i = 1; $i <= $this->getLoopCount(object: 'VerificationDocument'); ++$i) {
            $faker = $this->createFaker(seed: $id);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);

            [$contract, $serviceLocation] = $this->selectMultipleParentData(
                $i,
                'VerificationDocument',
                $contractData,
                $serviceLocationData
            );

            $signData = $this->createSignData(faker: $faker);

            $verificationDocument = [
                'index' => $i,
                'id' => ++$id,
                'uuid' => $faker->uuid(),
                'file_id' => $currentFileId,
                'certificate_number' => sprintf(
                    'ENC%s%s%s',
                    $faker->numberBetween(int1: 200000, int2: 299999),
                    strtoupper(string: $faker->randomLetter()),
                    $faker->numberBetween(int1: 10, int2: 99)
                ),
                'business_partner_id' => $contract['business_partner_id'],
                'contract_id' => $contract['id'],
                'avv_id' => (string) $faker->numberBetween(int1: 120000, int2: 129999),
                'avv_text' => $faker->text(40),
                'service_location_id' => $serviceLocation['id'],
                'approved_amount_document' => $faker->randomFloat(nbMaxDecimals: 2, min: 1000, max: 9999),
                'approved_rem_amount_document' => $faker->randomFloat(nbMaxDecimals: 2, min: 1000, max: 9999),
                'approved_rem_amount_year' => $faker->randomFloat(nbMaxDecimals: 2, min: 1000, max: 9999),
                'object_id' => 'ZE000000000000'.++$objectId,
                'description' => $faker->text(40),
                'doc_id' => $faker->uuid(),
                'partner_id' => 'E'.$faker->numberBetween(int1: 71100000, int2: 71199999),
                'partner_name1' => $faker->company(),
                'partner_street' => $faker->streetName().' '.$faker->numberBetween(int1: 1, int2: 999),
                'partner_postal_code' => $faker->postcode(),
                'partner_city' => $faker->city(),
            ];

            $data[] = array_merge($verificationDocument, $signData);
        }

        return $data;
    }

    /**
     * @return array<int, array<string, mixed>>
     */
    protected function supervisionDocumentDataProvider(): array
    {
        $verificationDocumentData = $this->verificationDocumentDataProvider();
        $routeData = $this->routeDataProvider();
        $orderData = $this->orderDataProvider();

        if ([] === $verificationDocumentData || [] === $routeData || [] === $orderData) {
            return [];
        }

        $data = [];
        $id = self::ID_RANGES['SupervisionDocument'];
        $fileId = $this->getFileId(object: 'SupervisionDocument');

        for ($i = 1; $i <= $this->getLoopCount(object: 'SupervisionDocument'); ++$i) {
            $faker = $this->createFaker(seed: $id);
            $currentFileId = $this->calculateFileId(baseFileId: $fileId, index: $i);

            [$verificationDocument, $route, $order] = $this->selectMultipleParentData(
                $i,
                'SupervisionDocument',
                $verificationDocumentData,
                $routeData,
                $orderData
            );

            $signData = $this->createSignData(faker: $faker);

            $supervisionDocument = [
                'index' => $i,
                'uuid' => $faker->uuid(),
                'id' => (string) ++$id,
                'file_id' => $currentFileId,
                'doc_id' => $faker->uuid(),
                'verification_document_id' => $verificationDocument['id'],
                'certificate_number' => $verificationDocument['certificate_number'],
                'route_id' => $route['id'],
                'description' => $faker->text(40),
                'avv_id' => $verificationDocument['avv_id'],
                'avv_text' => $verificationDocument['avv_text'],
                'amount' => $faker->randomFloat(nbMaxDecimals: 2, min: 100, max: 9999),
                'order_object_id' => $order['object_id'],
                'order_id' => $order['id'],
                'order_pos_id' => $order['pos_id'],
                'business_partner_id' => $verificationDocument['business_partner_id'],
                'partner_name1' => $verificationDocument['business_partner_id'],
                'partner_street' => $faker->streetName(),
                'partner_house_number' => (string) $faker->numberBetween(int1: 1, int2: 999),
                'partner_postal_code' => $verificationDocument['partner_postal_code'],
                'partner_city' => $verificationDocument['partner_city'],
            ];

            $data[] = array_merge($supervisionDocument, $signData);
        }

        return $data;
    }
}
