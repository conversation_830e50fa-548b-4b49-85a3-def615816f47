<?php

declare(strict_types=1);

namespace App\Tests\Api;

use App\Tests\Support\ApiTester;

/**
 * @group SapEurope
 * @group Portal
 * @group Archive
 * @group BusinessPartner
 * @group Contract
 * @group ContractServiceLocation
 * @group Invoice
 * @group Order
 * @group Route
 * @group Service
 * @group ServiceLocation
 * @group SupervisionDocument
 * @group VerificationDocument
 */
class MockserverCest
{
    public function canConnectToMockserver(ApiTester $I): void
    {
        $I->callMockServerReset();
    }
}
